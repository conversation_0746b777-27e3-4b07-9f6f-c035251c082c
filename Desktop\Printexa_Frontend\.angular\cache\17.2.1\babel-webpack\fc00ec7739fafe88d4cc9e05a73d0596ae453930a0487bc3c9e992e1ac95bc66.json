{"ast": null, "code": "import toString from './toString.js';\nimport unescapeHtmlChar from './_unescapeHtmlChar.js';\n\n/** Used to match HTML entities and HTML characters. */\nvar reEscapedHtml = /&(?:amp|lt|gt|quot|#39);/g,\n  reHasEscapedHtml = RegExp(reEscapedHtml.source);\n\n/**\n * The inverse of `_.escape`; this method converts the HTML entities\n * `&amp;`, `&lt;`, `&gt;`, `&quot;`, and `&#39;` in `string` to\n * their corresponding characters.\n *\n * **Note:** No other HTML entities are unescaped. To unescape additional\n * HTML entities use a third-party library like [_he_](https://mths.be/he).\n *\n * @static\n * @memberOf _\n * @since 0.6.0\n * @category String\n * @param {string} [string=''] The string to unescape.\n * @returns {string} Returns the unescaped string.\n * @example\n *\n * _.unescape('fred, barney, &amp; pebbles');\n * // => 'fred, barney, & pebbles'\n */\nfunction unescape(string) {\n  string = toString(string);\n  return string && reHasEscapedHtml.test(string) ? string.replace(reEscapedHtml, unescapeHtmlChar) : string;\n}\nexport default unescape;", "map": {"version": 3, "names": ["toString", "unescapeHtmlChar", "reEscapedHtml", "reHasEscapedHtml", "RegExp", "source", "unescape", "string", "test", "replace"], "sources": ["C:/Users/<USER>/Desktop/Rapport_PFE/kuber/source/mian/node_modules/lodash-es/unescape.js"], "sourcesContent": ["import toString from './toString.js';\nimport unescapeHtmlChar from './_unescapeHtmlChar.js';\n\n/** Used to match HTML entities and HTML characters. */\nvar reEscapedHtml = /&(?:amp|lt|gt|quot|#39);/g,\n    reHasEscapedHtml = RegExp(reEscapedHtml.source);\n\n/**\n * The inverse of `_.escape`; this method converts the HTML entities\n * `&amp;`, `&lt;`, `&gt;`, `&quot;`, and `&#39;` in `string` to\n * their corresponding characters.\n *\n * **Note:** No other HTML entities are unescaped. To unescape additional\n * HTML entities use a third-party library like [_he_](https://mths.be/he).\n *\n * @static\n * @memberOf _\n * @since 0.6.0\n * @category String\n * @param {string} [string=''] The string to unescape.\n * @returns {string} Returns the unescaped string.\n * @example\n *\n * _.unescape('fred, barney, &amp; pebbles');\n * // => 'fred, barney, & pebbles'\n */\nfunction unescape(string) {\n  string = toString(string);\n  return (string && reHasEscapedHtml.test(string))\n    ? string.replace(reEscapedHtml, unescapeHtmlChar)\n    : string;\n}\n\nexport default unescape;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,gBAAgB,MAAM,wBAAwB;;AAErD;AACA,IAAIC,aAAa,GAAG,2BAA2B;EAC3CC,gBAAgB,GAAGC,MAAM,CAACF,aAAa,CAACG,MAAM,CAAC;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAE;EACxBA,MAAM,GAAGP,QAAQ,CAACO,MAAM,CAAC;EACzB,OAAQA,MAAM,IAAIJ,gBAAgB,CAACK,IAAI,CAACD,MAAM,CAAC,GAC3CA,MAAM,CAACE,OAAO,CAACP,aAAa,EAAED,gBAAgB,CAAC,GAC/CM,MAAM;AACZ;AAEA,eAAeD,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}