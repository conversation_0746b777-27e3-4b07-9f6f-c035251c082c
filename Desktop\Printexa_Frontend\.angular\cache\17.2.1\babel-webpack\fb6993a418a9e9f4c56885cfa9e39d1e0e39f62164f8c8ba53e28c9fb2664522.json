{"ast": null, "code": "import { ContactsComponent } from \"./contacts.component\";\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\nexport const CONTACT_ROUTE = [{\n  path: \"\",\n  component: ContactsComponent\n}, {\n  path: '**',\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["ContactsComponent", "Page404Component", "CONTACT_ROUTE", "path", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\contacts\\contacts.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { ContactsComponent } from \"./contacts.component\";\r\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\r\n\r\nexport const CONTACT_ROUTE: Route[] = [\r\n  {\r\n    path: \"\",\r\n    component: ContactsComponent,\r\n  },\r\n  { path: '**', component: Page404Component },\r\n];\r\n\r\n"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,gBAAgB,QAAQ,8CAA8C;AAE/E,OAAO,MAAMC,aAAa,GAAY,CACpC;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ;CACZ,EACD;EAAEG,IAAI,EAAE,IAAI;EAAEC,SAAS,EAAEH;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}