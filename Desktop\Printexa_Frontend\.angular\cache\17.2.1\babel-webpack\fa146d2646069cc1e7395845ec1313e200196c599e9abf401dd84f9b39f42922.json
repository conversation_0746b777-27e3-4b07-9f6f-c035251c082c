{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Bulgarian [bg]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/kraz\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var bg = moment.defineLocale('bg', {\n    months: 'януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември'.split('_'),\n    monthsShort: 'яну_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек'.split('_'),\n    weekdays: 'неделя_понеделник_вторник_сряда_четвъртък_петък_събота'.split('_'),\n    weekdaysShort: 'нед_пон_вто_сря_чет_пет_съб'.split('_'),\n    weekdaysMin: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'D.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY H:mm',\n      LLLL: 'dddd, D MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[Днес в] LT',\n      nextDay: '[Утре в] LT',\n      nextWeek: 'dddd [в] LT',\n      lastDay: '[Вчера в] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n          case 6:\n            return '[Миналата] dddd [в] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[Миналия] dddd [в] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'след %s',\n      past: 'преди %s',\n      s: 'няколко секунди',\n      ss: '%d секунди',\n      m: 'минута',\n      mm: '%d минути',\n      h: 'час',\n      hh: '%d часа',\n      d: 'ден',\n      dd: '%d дена',\n      w: 'седмица',\n      ww: '%d седмици',\n      M: 'месец',\n      MM: '%d месеца',\n      y: 'година',\n      yy: '%d години'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ев|ен|ти|ви|ри|ми)/,\n    ordinal: function (number) {\n      var lastDigit = number % 10,\n        last2Digits = number % 100;\n      if (number === 0) {\n        return number + '-ев';\n      } else if (last2Digits === 0) {\n        return number + '-ен';\n      } else if (last2Digits > 10 && last2Digits < 20) {\n        return number + '-ти';\n      } else if (lastDigit === 1) {\n        return number + '-ви';\n      } else if (lastDigit === 2) {\n        return number + '-ри';\n      } else if (lastDigit === 7 || lastDigit === 8) {\n        return number + '-ми';\n      } else {\n        return number + '-ти';\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return bg;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "bg", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "day", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "lastDigit", "last2Digits", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/moment/locale/bg.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Bulgarian [bg]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/kraz\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var bg = moment.defineLocale('bg', {\n        months: 'януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември'.split(\n            '_'\n        ),\n        monthsShort: 'яну_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек'.split('_'),\n        weekdays: 'неделя_понеделник_вторник_сряда_четвъртък_петък_събота'.split(\n            '_'\n        ),\n        weekdaysShort: 'нед_пон_вто_сря_чет_пет_съб'.split('_'),\n        weekdaysMin: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'D.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY H:mm',\n            LLLL: 'dddd, D MMMM YYYY H:mm',\n        },\n        calendar: {\n            sameDay: '[Днес в] LT',\n            nextDay: '[Утре в] LT',\n            nextWeek: 'dddd [в] LT',\n            lastDay: '[Вчера в] LT',\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                    case 3:\n                    case 6:\n                        return '[Миналата] dddd [в] LT';\n                    case 1:\n                    case 2:\n                    case 4:\n                    case 5:\n                        return '[Миналия] dddd [в] LT';\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'след %s',\n            past: 'преди %s',\n            s: 'няколко секунди',\n            ss: '%d секунди',\n            m: 'минута',\n            mm: '%d минути',\n            h: 'час',\n            hh: '%d часа',\n            d: 'ден',\n            dd: '%d дена',\n            w: 'седмица',\n            ww: '%d седмици',\n            M: 'месец',\n            MM: '%d месеца',\n            y: 'година',\n            yy: '%d години',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-(ев|ен|ти|ви|ри|ми)/,\n        ordinal: function (number) {\n            var lastDigit = number % 10,\n                last2Digits = number % 100;\n            if (number === 0) {\n                return number + '-ев';\n            } else if (last2Digits === 0) {\n                return number + '-ен';\n            } else if (last2Digits > 10 && last2Digits < 20) {\n                return number + '-ти';\n            } else if (lastDigit === 1) {\n                return number + '-ви';\n            } else if (lastDigit === 2) {\n                return number + '-ри';\n            } else if (lastDigit === 7 || lastDigit === 8) {\n                return number + '-ми';\n            } else {\n                return number + '-ти';\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return bg;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,mFAAmF,CAACC,KAAK,CAC7F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,wDAAwD,CAACF,KAAK,CACpE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,wBAAwB;UACnC,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,uBAAuB;QACtC;MACJ,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,6BAA6B;IACrDC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,SAAS,GAAGD,MAAM,GAAG,EAAE;QACvBE,WAAW,GAAGF,MAAM,GAAG,GAAG;MAC9B,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAOA,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIE,WAAW,KAAK,CAAC,EAAE;QAC1B,OAAOF,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIE,WAAW,GAAG,EAAE,IAAIA,WAAW,GAAG,EAAE,EAAE;QAC7C,OAAOF,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIC,SAAS,KAAK,CAAC,EAAE;QACxB,OAAOD,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIC,SAAS,KAAK,CAAC,EAAE;QACxB,OAAOD,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM,IAAIC,SAAS,KAAK,CAAC,IAAIA,SAAS,KAAK,CAAC,EAAE;QAC3C,OAAOD,MAAM,GAAG,KAAK;MACzB,CAAC,MAAM;QACH,OAAOA,MAAM,GAAG,KAAK;MACzB;IACJ,CAAC;IACDG,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO/C,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}