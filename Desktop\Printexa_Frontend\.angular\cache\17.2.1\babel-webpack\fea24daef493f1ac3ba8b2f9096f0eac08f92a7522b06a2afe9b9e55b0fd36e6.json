{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { BehaviorSubject, of, throwError } from 'rxjs';\nimport { Role } from '@core/models/role';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.users = [{\n      id: 1,\n      img: 'assets/images/user/admin.jpg',\n      username: '<EMAIL>',\n      password: 'admin@123',\n      firstName: 'Sarah',\n      lastName: '<PERSON>',\n      role: Role.Admin,\n      token: 'admin-token'\n    }, {\n      id: 2,\n      img: 'assets/images/user/employee.jpg',\n      username: '<EMAIL>',\n      password: 'employee@123',\n      firstName: 'Ashton',\n      lastName: 'Cox',\n      role: Role.Employee,\n      token: 'employee-token'\n    }, {\n      id: 3,\n      img: 'assets/images/user/client.jpg',\n      username: '<EMAIL>',\n      password: 'client@123',\n      firstName: 'Cara',\n      lastName: '<PERSON>',\n      role: Role.Client,\n      token: 'client-token'\n    }];\n    this.currentUserSubject = new BehaviorSubject(JSON.parse(localStorage.getItem('currentUser') || '{}'));\n    this.currentUser = this.currentUserSubject.asObservable();\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  login(username, password) {\n    const user = this.users.find(u => u.username === username && u.password === password);\n    if (!user) {\n      return this.error('Username or password is incorrect');\n    } else {\n      localStorage.setItem('currentUser', JSON.stringify(user));\n      this.currentUserSubject.next(user);\n      return this.ok({\n        id: user.id,\n        img: user.img,\n        username: user.username,\n        firstName: user.firstName,\n        lastName: user.lastName,\n        token: user.token\n      });\n    }\n  }\n  ok(body) {\n    return of(new HttpResponse({\n      status: 200,\n      body\n    }));\n  }\n  error(message) {\n    return throwError(message);\n  }\n  logout() {\n    // remove user from local storage to log user out\n    localStorage.removeItem('currentUser');\n    this.currentUserSubject.next(this.currentUserValue);\n    return of({\n      success: false\n    });\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpResponse", "BehaviorSubject", "of", "throwError", "Role", "AuthService", "constructor", "http", "users", "id", "img", "username", "password", "firstName", "lastName", "role", "Admin", "token", "Employee", "Client", "currentUserSubject", "JSON", "parse", "localStorage", "getItem", "currentUser", "asObservable", "currentUserValue", "value", "login", "user", "find", "u", "error", "setItem", "stringify", "next", "ok", "body", "status", "message", "logout", "removeItem", "success", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\core\\service\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpResponse } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, of, throwError } from 'rxjs';\r\nimport { User } from '../models/user';\r\nimport { Role } from '@core/models/role';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n  private currentUserSubject: BehaviorSubject<User>;\r\n  public currentUser: Observable<User>;\r\n\r\n  private users = [\r\n    {\r\n      id: 1,\r\n      img: 'assets/images/user/admin.jpg',\r\n      username: '<EMAIL>',\r\n      password: 'admin@123',\r\n      firstName: 'Sarah',\r\n      lastName: 'Smith',\r\n      role: Role.Admin,\r\n      token: 'admin-token',\r\n    },\r\n    {\r\n      id: 2,\r\n      img: 'assets/images/user/employee.jpg',\r\n      username: '<EMAIL>',\r\n      password: 'employee@123',\r\n      firstName: 'Ashton',\r\n      lastName: 'Cox',\r\n      role: Role.Employee,\r\n      token: 'employee-token',\r\n    },\r\n    {\r\n      id: 3,\r\n      img: 'assets/images/user/client.jpg',\r\n      username: '<EMAIL>',\r\n      password: 'client@123',\r\n      firstName: 'Cara',\r\n      lastName: '<PERSON>',\r\n      role: Role.Client,\r\n      token: 'client-token',\r\n    },\r\n  ];\r\n\r\n  constructor(private http: HttpClient) {\r\n    this.currentUserSubject = new BehaviorSubject<User>(\r\n      JSON.parse(localStorage.getItem('currentUser') || '{}')\r\n    );\r\n    this.currentUser = this.currentUserSubject.asObservable();\r\n  }\r\n\r\n  public get currentUserValue(): User {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n  login(username: string, password: string) {\r\n\r\n    const user = this.users.find((u) => u.username === username && u.password === password);\r\n\r\n    if (!user) {\r\n      return this.error('Username or password is incorrect');\r\n    } else {\r\n      localStorage.setItem('currentUser', JSON.stringify(user));\r\n      this.currentUserSubject.next(user);\r\n      return this.ok({\r\n        id: user.id,\r\n        img: user.img,\r\n        username: user.username,\r\n        firstName: user.firstName,\r\n        lastName: user.lastName,\r\n        token: user.token,\r\n      });\r\n    }\r\n  }\r\n  ok(body?: {\r\n    id: number;\r\n    img: string;\r\n    username: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    token: string;\r\n  }) {\r\n    return of(new HttpResponse({ status: 200, body }));\r\n  }\r\n  error(message: string) {\r\n    return throwError(message);\r\n  }\r\n\r\n  logout() {\r\n    // remove user from local storage to log user out\r\n    localStorage.removeItem('currentUser');\r\n    this.currentUserSubject.next(this.currentUserValue);\r\n    return of({ success: false });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,YAAY,QAAQ,sBAAsB;AAC/D,SAASC,eAAe,EAAcC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AAElE,SAASC,IAAI,QAAQ,mBAAmB;;;AAKxC,OAAM,MAAOC,WAAW;EAqCtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAjChB,KAAAC,KAAK,GAAG,CACd;MACEC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,8BAA8B;MACnCC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE,WAAW;MACrBC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAEX,IAAI,CAACY,KAAK;MAChBC,KAAK,EAAE;KACR,EACD;MACER,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,iCAAiC;MACtCC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAEX,IAAI,CAACc,QAAQ;MACnBD,KAAK,EAAE;KACR,EACD;MACER,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,+BAA+B;MACpCC,QAAQ,EAAE,qBAAqB;MAC/BC,QAAQ,EAAE,YAAY;MACtBC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,SAAS;MACnBC,IAAI,EAAEX,IAAI,CAACe,MAAM;MACjBF,KAAK,EAAE;KACR,CACF;IAGC,IAAI,CAACG,kBAAkB,GAAG,IAAInB,eAAe,CAC3CoB,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,CACxD;IACD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACL,kBAAkB,CAACM,YAAY,EAAE;EAC3D;EAEA,IAAWC,gBAAgBA,CAAA;IACzB,OAAO,IAAI,CAACP,kBAAkB,CAACQ,KAAK;EACtC;EAEAC,KAAKA,CAAClB,QAAgB,EAAEC,QAAgB;IAEtC,MAAMkB,IAAI,GAAG,IAAI,CAACtB,KAAK,CAACuB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrB,QAAQ,KAAKA,QAAQ,IAAIqB,CAAC,CAACpB,QAAQ,KAAKA,QAAQ,CAAC;IAEvF,IAAI,CAACkB,IAAI,EAAE;MACT,OAAO,IAAI,CAACG,KAAK,CAAC,mCAAmC,CAAC;KACvD,MAAM;MACLV,YAAY,CAACW,OAAO,CAAC,aAAa,EAAEb,IAAI,CAACc,SAAS,CAACL,IAAI,CAAC,CAAC;MACzD,IAAI,CAACV,kBAAkB,CAACgB,IAAI,CAACN,IAAI,CAAC;MAClC,OAAO,IAAI,CAACO,EAAE,CAAC;QACb5B,EAAE,EAAEqB,IAAI,CAACrB,EAAE;QACXC,GAAG,EAAEoB,IAAI,CAACpB,GAAG;QACbC,QAAQ,EAAEmB,IAAI,CAACnB,QAAQ;QACvBE,SAAS,EAAEiB,IAAI,CAACjB,SAAS;QACzBC,QAAQ,EAAEgB,IAAI,CAAChB,QAAQ;QACvBG,KAAK,EAAEa,IAAI,CAACb;OACb,CAAC;;EAEN;EACAoB,EAAEA,CAACC,IAOF;IACC,OAAOpC,EAAE,CAAC,IAAIF,YAAY,CAAC;MAAEuC,MAAM,EAAE,GAAG;MAAED;IAAI,CAAE,CAAC,CAAC;EACpD;EACAL,KAAKA,CAACO,OAAe;IACnB,OAAOrC,UAAU,CAACqC,OAAO,CAAC;EAC5B;EAEAC,MAAMA,CAAA;IACJ;IACAlB,YAAY,CAACmB,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAACtB,kBAAkB,CAACgB,IAAI,CAAC,IAAI,CAACT,gBAAgB,CAAC;IACnD,OAAOzB,EAAE,CAAC;MAAEyC,OAAO,EAAE;IAAK,CAAE,CAAC;EAC/B;EAAC,QAAAC,CAAA,G;qBAtFUvC,WAAW,EAAAwC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAX5C,WAAW;IAAA6C,OAAA,EAAX7C,WAAW,CAAA8C,IAAA;IAAAC,UAAA,EAFV;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}