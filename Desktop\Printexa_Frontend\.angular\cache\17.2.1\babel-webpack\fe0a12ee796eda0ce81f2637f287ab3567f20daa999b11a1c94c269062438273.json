{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ogContent, MatDialogActions } from \"@angular/material/dialog\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/button\";\nexport class SimpleDialogComponent {\n  constructor(dialogRef) {\n    this.dialogRef = dialogRef;\n  }\n  close() {\n    this.dialogRef.close();\n  }\n  static #_ = this.ɵfac = function SimpleDialogComponent_Factory(t) {\n    return new (t || SimpleDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SimpleDialogComponent,\n    selectors: [[\"ng-component\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 8,\n    vars: 0,\n    consts: [[\"mat-dialog-title\", \"\"], [\"mat-dialog-content\", \"\"], [\"mat-dialog-actions\", \"\"], [\"mat-button\", \"\", 3, \"click\"]],\n    template: function SimpleDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"h1\", 0);\n        i0.ɵɵtext(1, \"Hello There\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 1)(3, \"p\");\n        i0.ɵɵtext(4, \"This Is a Simple Dialog\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 2)(6, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function SimpleDialogComponent_Template_button_click_6_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵtext(7, \"Close\");\n        i0.ɵɵelementEnd()();\n      }\n    },\n    dependencies: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, i2.MatButton],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "MatButtonModule", "SimpleDialogComponent", "constructor", "dialogRef", "close", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SimpleDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SimpleDialogComponent_Template_button_click_6_listener", "i2", "MatButton", "encapsulation"], "sources": ["C:\\Users\\<USER>\\Desktop\\Rapport_PFE\\kuber\\source\\mian\\src\\app\\ui\\modal\\simpleDialog.component.ts"], "sourcesContent": ["import { Component } from \"@angular/core\";\r\nimport { MatD<PERSON>ogRef, MatDialogTitle, MatDialogContent, MatDialogActions } from \"@angular/material/dialog\";\r\nimport { MatButtonModule } from \"@angular/material/button\";\r\n@Component({\r\n    template: `\r\n    <h1 mat-dialog-title>Hello There</h1>\r\n    <div mat-dialog-content>\r\n      <p>This Is a Simple Dialog</p>\r\n    </div>\r\n    <div mat-dialog-actions>\r\n      <button mat-button (click)=\"close()\">Close</button>\r\n    </div>\r\n  `,\r\n    standalone: true,\r\n    imports: [\r\n        MatDialogTitle,\r\n        MatDialogContent,\r\n        MatDialogActions,\r\n        MatButtonModule,\r\n    ],\r\n})\r\nexport class SimpleDialogComponent {\r\n  constructor(public dialogRef: MatDialogRef<SimpleDialogComponent>) {}\r\n  close(): void {\r\n    this.dialogRef.close();\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAuBA,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,0BAA0B;AAC3G,SAASC,eAAe,QAAQ,0BAA0B;;;;AAmB1D,OAAM,MAAOC,qBAAqB;EAChCC,YAAmBC,SAA8C;IAA9C,KAAAA,SAAS,GAATA,SAAS;EAAwC;EACpEC,KAAKA,CAAA;IACH,IAAI,CAACD,SAAS,CAACC,KAAK,EAAE;EACxB;EAAC,QAAAC,CAAA,G;qBAJUJ,qBAAqB,EAAAK,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBT,qBAAqB;IAAAU,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAP,EAAA,CAAAQ,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAhB9Bd,EAAA,CAAAgB,cAAA,YAAqB;QAAAhB,EAAA,CAAAiB,MAAA,kBAAW;QAAAjB,EAAA,CAAAkB,YAAA,EAAK;QACrClB,EAAA,CAAAgB,cAAA,aAAwB;QACnBhB,EAAA,CAAAiB,MAAA,8BAAuB;QAAAjB,EAAA,CAAAkB,YAAA,EAAI;QAEhClB,EAAA,CAAAgB,cAAA,aAAwB;QACHhB,EAAA,CAAAmB,UAAA,mBAAAC,uDAAA;UAAA,OAASL,GAAA,CAAAjB,KAAA,EAAO;QAAA,EAAC;QAACE,EAAA,CAAAiB,MAAA,YAAK;QAAAjB,EAAA,CAAAkB,YAAA,EAAS;;;mBAKjD3B,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBC,eAAe,EAAA2B,EAAA,CAAAC,SAAA;IAAAC,aAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}