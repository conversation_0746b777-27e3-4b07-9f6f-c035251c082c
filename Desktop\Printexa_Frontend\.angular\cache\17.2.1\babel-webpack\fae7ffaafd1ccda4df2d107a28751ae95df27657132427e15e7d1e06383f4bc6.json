{"ast": null, "code": "/**\n * Subscription sink that holds Observable subscriptions\n * until you call unsubscribe on it in ngOnDestroy.\n */\nexport class SubSink {\n  /**\n   * Subscription sink that holds Observable subscriptions\n   * until you call unsubscribe on it in ngOnDestroy.\n   *\n   * @example\n   * In Angular:\n   * ```\n   *   private subs = new SubSink();\n   *   ...\n   *   this.subs.sink = observable$.subscribe(\n   *   this.subs.add(observable$.subscribe(...));\n   *   ...\n   *   ngOnDestroy() {\n   *     this.subs.unsubscribe();\n   *   }\n   * ```\n   */\n  constructor() {\n    this._subs = [];\n    // constructor\n  }\n  /**\n   * Add subscriptions to the tracked subscriptions\n   * @example\n   *  this.subs.add(observable$.subscribe(...));\n   */\n  add(...subscriptions) {\n    this._subs = this._subs.concat(subscriptions);\n  }\n  /**\n   * Assign subscription to this sink to add it to the tracked subscriptions\n   * @example\n   *  this.subs.sink = observable$.subscribe(...);\n   */\n  set sink(subscription) {\n    this._subs.push(subscription);\n  }\n  /**\n   * Unsubscribe to all subscriptions in ngOnDestroy()\n   * @example\n   *   ngOnDestroy() {\n   *     this.subs.unsubscribe();\n   *   }\n   */\n  unsubscribe() {\n    this._subs.forEach(sub => sub && sub.unsubscribe());\n    this._subs = [];\n  }\n}", "map": {"version": 3, "names": ["SubSink", "constructor", "_subs", "add", "subscriptions", "concat", "sink", "subscription", "push", "unsubscribe", "for<PERSON>ach", "sub"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\shared\\sub-sink.ts"], "sourcesContent": ["import { SubscriptionLike } from \"rxjs\";\r\n\r\n/**\r\n * Subscription sink that holds Observable subscriptions\r\n * until you call unsubscribe on it in ngOnDestroy.\r\n */\r\nexport class SubSink {\r\n  protected _subs: SubscriptionLike[] = [];\r\n\r\n  /**\r\n   * Subscription sink that holds Observable subscriptions\r\n   * until you call unsubscribe on it in ngOnDestroy.\r\n   *\r\n   * @example\r\n   * In Angular:\r\n   * ```\r\n   *   private subs = new SubSink();\r\n   *   ...\r\n   *   this.subs.sink = observable$.subscribe(\r\n   *   this.subs.add(observable$.subscribe(...));\r\n   *   ...\r\n   *   ngOnDestroy() {\r\n   *     this.subs.unsubscribe();\r\n   *   }\r\n   * ```\r\n   */\r\n  constructor() {\r\n    // constructor\r\n  }\r\n\r\n  /**\r\n   * Add subscriptions to the tracked subscriptions\r\n   * @example\r\n   *  this.subs.add(observable$.subscribe(...));\r\n   */\r\n  add(...subscriptions: SubscriptionLike[]) {\r\n    this._subs = this._subs.concat(subscriptions);\r\n  }\r\n\r\n  /**\r\n   * Assign subscription to this sink to add it to the tracked subscriptions\r\n   * @example\r\n   *  this.subs.sink = observable$.subscribe(...);\r\n   */\r\n  set sink(subscription: SubscriptionLike) {\r\n    this._subs.push(subscription);\r\n  }\r\n\r\n  /**\r\n   * Unsubscribe to all subscriptions in ngOnDestroy()\r\n   * @example\r\n   *   ngOnDestroy() {\r\n   *     this.subs.unsubscribe();\r\n   *   }\r\n   */\r\n  unsubscribe() {\r\n    this._subs.forEach((sub) => sub && sub.unsubscribe());\r\n    this._subs = [];\r\n  }\r\n}\r\n"], "mappings": "AAEA;;;;AAIA,OAAM,MAAOA,OAAO;EAGlB;;;;;;;;;;;;;;;;;EAiBAC,YAAA;IAnBU,KAAAC,KAAK,GAAuB,EAAE;IAoBtC;EACF;EAEA;;;;;EAKAC,GAAGA,CAAC,GAAGC,aAAiC;IACtC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACA,KAAK,CAACG,MAAM,CAACD,aAAa,CAAC;EAC/C;EAEA;;;;;EAKA,IAAIE,IAAIA,CAACC,YAA8B;IACrC,IAAI,CAACL,KAAK,CAACM,IAAI,CAACD,YAAY,CAAC;EAC/B;EAEA;;;;;;;EAOAE,WAAWA,CAAA;IACT,IAAI,CAACP,KAAK,CAACQ,OAAO,CAAEC,GAAG,IAAKA,GAAG,IAAIA,GAAG,CAACF,WAAW,EAAE,CAAC;IACrD,IAAI,CAACP,KAAK,GAAG,EAAE;EACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}