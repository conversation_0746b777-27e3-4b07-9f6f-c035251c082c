{"ast": null, "code": "export default function (parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n    node,\n    i = -1,\n    n = nodes.length,\n    k = parent.value && (x1 - x0) / parent.value;\n  while (++i < n) {\n    node = nodes[i], node.y0 = y0, node.y1 = y1;\n    node.x0 = x0, node.x1 = x0 += node.value * k;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}