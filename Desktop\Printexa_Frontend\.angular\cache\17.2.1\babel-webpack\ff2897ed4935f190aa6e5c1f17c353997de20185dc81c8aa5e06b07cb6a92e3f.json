{"ast": null, "code": "import { NgxEchartsDirective, provideEcharts } from 'ngx-echarts';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nconst _c0 = () => [\"Home\", \"Charts\"];\nexport class EchartComponent {\n  constructor() {\n    // line bar chart\n    this.line_bar_chart = {\n      grid: {\n        top: '6',\n        right: '0',\n        bottom: '17',\n        left: '25'\n      },\n      xAxis: {\n        data: ['2014', '2015', '2016', '2017', '2018'],\n        axisLine: {\n          lineStyle: {\n            color: '#eaeaea'\n          }\n        },\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      },\n      tooltip: {\n        show: true,\n        showContent: true,\n        alwaysShowContent: false,\n        triggerOn: 'mousemove',\n        trigger: 'axis'\n      },\n      yAxis: {\n        splitLine: {\n          lineStyle: {\n            color: '#eaeaea'\n          }\n        },\n        axisLine: {\n          lineStyle: {\n            color: '#eaeaea'\n          }\n        },\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      },\n      series: [{\n        name: 'sales',\n        type: 'bar',\n        data: [11, 14, 8, 16, 11, 13]\n      }, {\n        name: 'profit',\n        type: 'line',\n        smooth: true,\n        lineStyle: {\n          width: 3,\n          shadowColor: 'rgba(0,0,0,0.4)',\n          shadowBlur: 10,\n          shadowOffsetY: 10\n        },\n        data: [10, 7, 17, 11, 15],\n        symbolSize: 10\n      }, {\n        name: 'growth',\n        type: 'bar',\n        data: [10, 14, 10, 15, 9, 25]\n      }],\n      color: ['#9f78ff', '#3FA7DC', '#F6A025']\n    };\n    // line chart\n    this.line_chart = {\n      grid: {\n        top: '6',\n        right: '0',\n        bottom: '17',\n        left: '25'\n      },\n      xAxis: {\n        data: ['2014', '2015', '2016', '2017', '2018'],\n        axisLine: {\n          lineStyle: {\n            color: '#eaeaea'\n          }\n        },\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      },\n      tooltip: {\n        show: true,\n        showContent: true,\n        alwaysShowContent: false,\n        triggerOn: 'mousemove',\n        trigger: 'axis'\n      },\n      yAxis: {\n        splitLine: {\n          lineStyle: {\n            color: '#eaeaea'\n          }\n        },\n        axisLine: {\n          lineStyle: {\n            color: '#eaeaea'\n          }\n        },\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      },\n      series: [{\n        name: 'sales',\n        type: 'line',\n        smooth: true,\n        lineStyle: {\n          width: 3,\n          shadowColor: 'rgba(0,0,0,0.4)',\n          shadowBlur: 10,\n          shadowOffsetY: 10\n        },\n        data: [15, 22, 14, 31, 17, 41],\n        symbolSize: 10\n        // color: [\"#FF8D60\"]\n      }, {\n        name: 'Profit',\n        type: 'line',\n        smooth: true,\n        lineStyle: {\n          width: 3,\n          shadowColor: 'rgba(0,0,0,0.4)',\n          shadowBlur: 10,\n          shadowOffsetY: 10\n        },\n        symbolSize: 10,\n        // size: 10,\n        data: [8, 12, 28, 10, 10, 12]\n        // color: [\"#009DA0\"]\n      }],\n      color: ['#3FA7DC', '#F6A025']\n    };\n    // bar chart\n    this.bar_chart = {\n      grid: {\n        top: '6',\n        right: '0',\n        bottom: '17',\n        left: '25'\n      },\n      xAxis: {\n        data: ['2014', '2015', '2016', '2017', '2018'],\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      },\n      tooltip: {\n        show: true,\n        showContent: true,\n        alwaysShowContent: false,\n        triggerOn: 'mousemove',\n        trigger: 'axis'\n      },\n      yAxis: {\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      },\n      series: [{\n        name: 'sales',\n        type: 'bar',\n        data: [13, 14, 10, 16, 11, 13]\n      }, {\n        name: 'growth',\n        type: 'bar',\n        data: [10, 14, 10, 15, 9, 25]\n      }],\n      color: ['#A3A09D', '#32cafe']\n    };\n    // graph line chart\n    this.graph_line_chart = {\n      tooltip: {\n        trigger: 'axis'\n      },\n      legend: {\n        data: ['sales', 'purchases'],\n        textStyle: {\n          color: '#9aa0ac'\n        }\n      },\n      toolbox: {\n        show: !1\n      },\n      xAxis: [{\n        type: 'category',\n        data: ['2000', '2001', '2002', '2003', '2004', '2005'],\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      }],\n      yAxis: [{\n        type: 'value',\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      }],\n      series: [{\n        name: 'sales',\n        type: 'bar',\n        data: [22, 54, 37, 23, 25.6, 76],\n        markPoint: {\n          data: [{\n            type: 'max',\n            name: '???'\n          }, {\n            type: 'min',\n            name: '???'\n          }]\n        },\n        markLine: {\n          data: [{\n            type: 'average'\n          }]\n        }\n      }, {\n        name: 'purchases',\n        type: 'bar',\n        data: [35, 45, 47, 10, 35, 70],\n        markLine: {\n          data: [{\n            type: 'average'\n          }]\n        }\n      }],\n      color: ['#9f78ff', '#32cafe']\n    };\n    /* Pie Chart */\n    this.pie_chart = {\n      tooltip: {\n        trigger: 'item',\n        formatter: '{a} <br/>{b} : {c} ({d}%)'\n      },\n      legend: {\n        data: ['Data 1', 'Data 2', 'Data 3', 'Data 4', 'Data 5'],\n        textStyle: {\n          color: '#9aa0ac',\n          padding: [0, 5, 0, 5]\n        }\n      },\n      series: [{\n        name: 'Chart Data',\n        type: 'pie',\n        radius: '55%',\n        center: ['50%', '48%'],\n        data: [{\n          value: 335,\n          name: 'Data 1'\n        }, {\n          value: 310,\n          name: 'Data 2'\n        }, {\n          value: 234,\n          name: 'Data 3'\n        }, {\n          value: 135,\n          name: 'Data 4'\n        }, {\n          value: 548,\n          name: 'Data 5'\n        }]\n      }],\n      color: ['#575B7A', '#DE725C', '#DFC126', '#72BE81', '#50A5D8']\n    };\n    // area line chart\n    this.area_line_chart = {\n      tooltip: {\n        trigger: 'axis'\n      },\n      legend: {\n        data: ['Intent', 'Pre-order', 'Deal'],\n        textStyle: {\n          color: '#9aa0ac',\n          padding: [0, 5, 0, 5]\n        }\n      },\n      toolbox: {\n        show: !0,\n        feature: {\n          magicType: {\n            show: !0,\n            title: {\n              line: 'Line',\n              bar: 'Bar',\n              stack: 'Stack'\n            },\n            type: ['line', 'bar', 'stack']\n          },\n          restore: {\n            show: !0,\n            title: 'Restore'\n          },\n          saveAsImage: {\n            show: !0,\n            title: 'Save Image'\n          }\n        }\n      },\n      xAxis: [{\n        type: 'category',\n        boundaryGap: !1,\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      }],\n      yAxis: [{\n        type: 'value',\n        axisLabel: {\n          fontSize: 10,\n          color: '#9aa0ac'\n        }\n      }],\n      series: [{\n        name: 'Deal',\n        type: 'line',\n        smooth: !0,\n        areaStyle: {},\n        emphasis: {\n          focus: 'series'\n        },\n        data: [10, 12, 21, 54, 260, 830, 710]\n      }, {\n        name: 'Pre-order',\n        type: 'line',\n        smooth: !0,\n        areaStyle: {},\n        emphasis: {\n          focus: 'series'\n        },\n        data: [30, 182, 434, 791, 390, 30, 10]\n      }, {\n        name: 'Intent',\n        type: 'line',\n        smooth: !0,\n        areaStyle: {},\n        emphasis: {\n          focus: 'series'\n        },\n        data: [1320, 1132, 601, 234, 120, 90, 20]\n      }],\n      color: ['#9f78ff', '#fa626b', '#32cafe']\n    };\n    this.pie_chart2 = {\n      legend: {\n        top: 'bottom'\n      },\n      toolbox: {\n        show: true,\n        feature: {\n          mark: {\n            show: true\n          },\n          dataView: {\n            show: true,\n            readOnly: false\n          },\n          restore: {\n            show: true\n          },\n          saveAsImage: {\n            show: true\n          }\n        }\n      },\n      series: [{\n        name: 'Nightingale Chart',\n        type: 'pie',\n        radius: '55%',\n        center: ['50%', '48%'],\n        roseType: 'area',\n        itemStyle: {\n          borderRadius: 8\n        },\n        data: [{\n          value: 40,\n          name: 'rose 1'\n        }, {\n          value: 38,\n          name: 'rose 2'\n        }, {\n          value: 32,\n          name: 'rose 3'\n        }, {\n          value: 30,\n          name: 'rose 4'\n        }, {\n          value: 28,\n          name: 'rose 5'\n        }, {\n          value: 26,\n          name: 'rose 6'\n        }, {\n          value: 22,\n          name: 'rose 7'\n        }, {\n          value: 18,\n          name: 'rose 8'\n        }]\n      }]\n    };\n    // sunburst chart\n    this.sunburst_chart = {\n      series: {\n        type: 'sunburst',\n        // emphasis: {\n        //     focus: 'ancestor'\n        // },\n        data: [{\n          name: 'Grandpa',\n          children: [{\n            name: 'Uncle Leo',\n            value: 15,\n            children: [{\n              name: 'Cousin Jack',\n              value: 2\n            }, {\n              name: 'Cousin Mary',\n              value: 5,\n              children: [{\n                name: 'Jackson',\n                value: 2\n              }]\n            }, {\n              name: 'Cousin Ben',\n              value: 4\n            }]\n          }, {\n            name: 'Father',\n            value: 10,\n            children: [{\n              name: 'Me',\n              value: 5\n            }, {\n              name: 'Brother Peter',\n              value: 1\n            }]\n          }]\n        }, {\n          name: 'Nancy',\n          children: [{\n            name: 'Uncle Nike',\n            children: [{\n              name: 'Cousin Betty',\n              value: 1\n            }, {\n              name: 'Cousin Jenny',\n              value: 2\n            }]\n          }]\n        }],\n        radius: [0, '90%'],\n        label: {\n          rotate: 'radial'\n        }\n      }\n    };\n    // constructor\n  }\n  static #_ = this.ɵfac = function EchartComponent_Factory(t) {\n    return new (t || EchartComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EchartComponent,\n    selectors: [[\"app-echart\"]],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([provideEcharts()]), i0.ɵɵStandaloneFeature],\n    decls: 64,\n    vars: 12,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\", \"clearfix\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\"], [1, \"card\"], [1, \"header\"], [1, \"body\"], [\"echarts\", \"\", 1, \"echart-height\", 3, \"options\"]],\n    template: function EchartComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Bar chart with line\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8);\n        i0.ɵɵelement(11, \"div\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(12, \"div\", 5)(13, \"div\", 6)(14, \"div\", 7)(15, \"h2\");\n        i0.ɵɵtext(16, \"Line Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 8);\n        i0.ɵɵelement(18, \"div\", 9);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(19, \"div\", 4)(20, \"div\", 5)(21, \"div\", 6)(22, \"div\", 7)(23, \"h2\");\n        i0.ɵɵtext(24, \"Bar chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"div\", 8);\n        i0.ɵɵelement(26, \"div\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(27, \"div\", 5)(28, \"div\", 6)(29, \"div\", 7)(30, \"h2\");\n        i0.ɵɵtext(31, \"Graph Line Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 8);\n        i0.ɵɵelement(33, \"div\", 9);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(34, \"div\", 4)(35, \"div\", 5)(36, \"div\", 6)(37, \"div\", 7)(38, \"h2\");\n        i0.ɵɵtext(39, \"Pie Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"div\", 8);\n        i0.ɵɵelement(41, \"div\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(42, \"div\", 5)(43, \"div\", 6)(44, \"div\", 7)(45, \"h2\");\n        i0.ɵɵtext(46, \"Aria Line Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(47, \"div\", 8);\n        i0.ɵɵelement(48, \"div\", 9);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(49, \"div\", 4)(50, \"div\", 5)(51, \"div\", 6)(52, \"div\", 7)(53, \"h2\");\n        i0.ɵɵtext(54, \"Pie Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(55, \"div\", 8);\n        i0.ɵɵelement(56, \"div\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(57, \"div\", 5)(58, \"div\", 6)(59, \"div\", 7)(60, \"h2\");\n        i0.ɵɵtext(61, \"Sunburst Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(62, \"div\", 8);\n        i0.ɵɵelement(63, \"div\", 9);\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Echart\")(\"items\", i0.ɵɵpureFunction0(11, _c0))(\"active_item\", \"Echart\");\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"options\", ctx.line_bar_chart);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"options\", ctx.line_chart);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"options\", ctx.bar_chart);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"options\", ctx.graph_line_chart);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"options\", ctx.pie_chart);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"options\", ctx.area_line_chart);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"options\", ctx.pie_chart2);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"options\", ctx.sunburst_chart);\n      }\n    },\n    dependencies: [BreadcrumbComponent, NgxEchartsDirective],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["NgxEchartsDirective", "provideEcharts", "BreadcrumbComponent", "EchartComponent", "constructor", "line_bar_chart", "grid", "top", "right", "bottom", "left", "xAxis", "data", "axisLine", "lineStyle", "color", "axisLabel", "fontSize", "tooltip", "show", "showContent", "alwaysS<PERSON><PERSON><PERSON>nt", "triggerOn", "trigger", "yAxis", "splitLine", "series", "name", "type", "smooth", "width", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetY", "symbolSize", "line_chart", "bar_chart", "graph_line_chart", "legend", "textStyle", "toolbox", "markPoint", "markLine", "pie_chart", "formatter", "padding", "radius", "center", "value", "area_line_chart", "feature", "magicType", "title", "line", "bar", "stack", "restore", "saveAsImage", "boundaryGap", "areaStyle", "emphasis", "focus", "pie_chart2", "mark", "dataView", "readOnly", "roseType", "itemStyle", "borderRadius", "sunburst_chart", "children", "label", "rotate", "_", "_2", "selectors", "standalone", "features", "i0", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "EchartComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "styles"], "sources": ["C:\\Users\\<USER>\\mian\\src\\app\\charts\\echart\\echart.component.ts", "C:\\Users\\<USER>\\mian\\src\\app\\charts\\echart\\echart.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { EChartsOption } from 'echarts';\r\nimport { NgxEchartsDirective, provideEcharts } from 'ngx-echarts';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n\r\n@Component({\r\n  selector: 'app-echart',\r\n  templateUrl: './echart.component.html',\r\n  styleUrls: ['./echart.component.scss'],\r\n  standalone: true,\r\n  imports: [BreadcrumbComponent, NgxEchartsDirective],\r\n  providers: [\r\n    provideEcharts(),\r\n  ]\r\n})\r\nexport class EchartComponent {\r\n  // line bar chart\r\n  line_bar_chart: EChartsOption = {\r\n    grid: {\r\n      top: '6',\r\n      right: '0',\r\n      bottom: '17',\r\n      left: '25',\r\n    },\r\n    xAxis: {\r\n      data: ['2014', '2015', '2016', '2017', '2018'],\r\n      axisLine: {\r\n        lineStyle: {\r\n          color: '#eaeaea',\r\n        },\r\n      },\r\n      axisLabel: {\r\n        fontSize: 10,\r\n        color: '#9aa0ac',\r\n      },\r\n    },\r\n    tooltip: {\r\n      show: true,\r\n      showContent: true,\r\n      alwaysShowContent: false,\r\n      triggerOn: 'mousemove',\r\n      trigger: 'axis',\r\n    },\r\n    yAxis: {\r\n      splitLine: {\r\n        lineStyle: {\r\n          color: '#eaeaea',\r\n        },\r\n      },\r\n      axisLine: {\r\n        lineStyle: {\r\n          color: '#eaeaea',\r\n        },\r\n      },\r\n      axisLabel: {\r\n        fontSize: 10,\r\n        color: '#9aa0ac',\r\n      },\r\n    },\r\n    series: [\r\n      {\r\n        name: 'sales',\r\n        type: 'bar',\r\n        data: [11, 14, 8, 16, 11, 13],\r\n      },\r\n      {\r\n        name: 'profit',\r\n        type: 'line',\r\n        smooth: true,\r\n        lineStyle: {\r\n          width: 3,\r\n          shadowColor: 'rgba(0,0,0,0.4)',\r\n          shadowBlur: 10,\r\n          shadowOffsetY: 10,\r\n        },\r\n        data: [10, 7, 17, 11, 15],\r\n        symbolSize: 10,\r\n      },\r\n      {\r\n        name: 'growth',\r\n        type: 'bar',\r\n        data: [10, 14, 10, 15, 9, 25],\r\n      },\r\n    ],\r\n    color: ['#9f78ff', '#3FA7DC', '#F6A025'],\r\n  };\r\n\r\n  // line chart\r\n  line_chart: EChartsOption = {\r\n    grid: {\r\n      top: '6',\r\n      right: '0',\r\n      bottom: '17',\r\n      left: '25',\r\n    },\r\n    xAxis: {\r\n      data: ['2014', '2015', '2016', '2017', '2018'],\r\n      axisLine: {\r\n        lineStyle: {\r\n          color: '#eaeaea',\r\n        },\r\n      },\r\n      axisLabel: {\r\n        fontSize: 10,\r\n        color: '#9aa0ac',\r\n      },\r\n    },\r\n    tooltip: {\r\n      show: true,\r\n      showContent: true,\r\n      alwaysShowContent: false,\r\n      triggerOn: 'mousemove',\r\n      trigger: 'axis',\r\n    },\r\n    yAxis: {\r\n      splitLine: {\r\n        lineStyle: {\r\n          color: '#eaeaea',\r\n        },\r\n      },\r\n      axisLine: {\r\n        lineStyle: {\r\n          color: '#eaeaea',\r\n        },\r\n      },\r\n      axisLabel: {\r\n        fontSize: 10,\r\n        color: '#9aa0ac',\r\n      },\r\n    },\r\n    series: [\r\n      {\r\n        name: 'sales',\r\n        type: 'line',\r\n        smooth: true,\r\n        lineStyle: {\r\n          width: 3,\r\n          shadowColor: 'rgba(0,0,0,0.4)',\r\n          shadowBlur: 10,\r\n          shadowOffsetY: 10,\r\n        },\r\n        data: [15, 22, 14, 31, 17, 41],\r\n        symbolSize: 10,\r\n        // color: [\"#FF8D60\"]\r\n      },\r\n      {\r\n        name: 'Profit',\r\n        type: 'line',\r\n        smooth: true,\r\n        lineStyle: {\r\n          width: 3,\r\n          shadowColor: 'rgba(0,0,0,0.4)',\r\n          shadowBlur: 10,\r\n          shadowOffsetY: 10,\r\n        },\r\n        symbolSize: 10,\r\n        // size: 10,\r\n        data: [8, 12, 28, 10, 10, 12],\r\n        // color: [\"#009DA0\"]\r\n      },\r\n    ],\r\n    color: ['#3FA7DC', '#F6A025'],\r\n  };\r\n\r\n  // bar chart\r\n  bar_chart: EChartsOption = {\r\n    grid: {\r\n      top: '6',\r\n      right: '0',\r\n      bottom: '17',\r\n      left: '25',\r\n    },\r\n    xAxis: {\r\n      data: ['2014', '2015', '2016', '2017', '2018'],\r\n\r\n      axisLabel: {\r\n        fontSize: 10,\r\n        color: '#9aa0ac',\r\n      },\r\n    },\r\n    tooltip: {\r\n      show: true,\r\n      showContent: true,\r\n      alwaysShowContent: false,\r\n      triggerOn: 'mousemove',\r\n      trigger: 'axis',\r\n    },\r\n    yAxis: {\r\n      axisLabel: {\r\n        fontSize: 10,\r\n        color: '#9aa0ac',\r\n      },\r\n    },\r\n    series: [\r\n      {\r\n        name: 'sales',\r\n        type: 'bar',\r\n        data: [13, 14, 10, 16, 11, 13],\r\n      },\r\n\r\n      {\r\n        name: 'growth',\r\n        type: 'bar',\r\n        data: [10, 14, 10, 15, 9, 25],\r\n      },\r\n    ],\r\n    color: ['#A3A09D', '#32cafe'],\r\n  };\r\n\r\n  // graph line chart\r\n  graph_line_chart: EChartsOption = {\r\n    tooltip: {\r\n      trigger: 'axis',\r\n    },\r\n    legend: {\r\n      data: ['sales', 'purchases'],\r\n      textStyle: {\r\n        color: '#9aa0ac',\r\n      },\r\n    },\r\n    toolbox: {\r\n      show: !1,\r\n    },\r\n    xAxis: [\r\n      {\r\n        type: 'category',\r\n        data: ['2000', '2001', '2002', '2003', '2004', '2005'],\r\n        axisLabel: {\r\n          fontSize: 10,\r\n          color: '#9aa0ac',\r\n        },\r\n      },\r\n    ],\r\n    yAxis: [\r\n      {\r\n        type: 'value',\r\n        axisLabel: {\r\n          fontSize: 10,\r\n          color: '#9aa0ac',\r\n        },\r\n      },\r\n    ],\r\n    series: [\r\n      {\r\n        name: 'sales',\r\n        type: 'bar',\r\n        data: [22, 54, 37, 23, 25.6, 76],\r\n        markPoint: {\r\n          data: [\r\n            {\r\n              type: 'max',\r\n              name: '???',\r\n            },\r\n            {\r\n              type: 'min',\r\n              name: '???',\r\n            },\r\n          ],\r\n        },\r\n        markLine: {\r\n          data: [\r\n            {\r\n              type: 'average',\r\n            },\r\n          ],\r\n        },\r\n      },\r\n\r\n      {\r\n        name: 'purchases',\r\n        type: 'bar',\r\n        data: [35, 45, 47, 10, 35, 70],\r\n        markLine: {\r\n          data: [\r\n            {\r\n              type: 'average',\r\n            },\r\n          ],\r\n        },\r\n      },\r\n    ],\r\n    color: ['#9f78ff', '#32cafe'],\r\n  };\r\n\r\n  /* Pie Chart */\r\n  pie_chart: EChartsOption = {\r\n    tooltip: {\r\n      trigger: 'item',\r\n      formatter: '{a} <br/>{b} : {c} ({d}%)',\r\n    },\r\n    legend: {\r\n      data: ['Data 1', 'Data 2', 'Data 3', 'Data 4', 'Data 5'],\r\n      textStyle: {\r\n        color: '#9aa0ac',\r\n        padding: [0, 5, 0, 5],\r\n      },\r\n    },\r\n\r\n    series: [\r\n      {\r\n        name: 'Chart Data',\r\n        type: 'pie',\r\n        radius: '55%',\r\n        center: ['50%', '48%'],\r\n        data: [\r\n          {\r\n            value: 335,\r\n            name: 'Data 1',\r\n          },\r\n          {\r\n            value: 310,\r\n            name: 'Data 2',\r\n          },\r\n          {\r\n            value: 234,\r\n            name: 'Data 3',\r\n          },\r\n          {\r\n            value: 135,\r\n            name: 'Data 4',\r\n          },\r\n          {\r\n            value: 548,\r\n            name: 'Data 5',\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    color: ['#575B7A', '#DE725C', '#DFC126', '#72BE81', '#50A5D8'],\r\n  };\r\n\r\n  // area line chart\r\n  area_line_chart: EChartsOption = {\r\n    tooltip: {\r\n      trigger: 'axis',\r\n    },\r\n    legend: {\r\n      data: ['Intent', 'Pre-order', 'Deal'],\r\n      textStyle: {\r\n        color: '#9aa0ac',\r\n        padding: [0, 5, 0, 5],\r\n      },\r\n    },\r\n    toolbox: {\r\n      show: !0,\r\n      feature: {\r\n        magicType: {\r\n          show: !0,\r\n          title: {\r\n            line: 'Line',\r\n            bar: 'Bar',\r\n            stack: 'Stack',\r\n          },\r\n          type: ['line', 'bar', 'stack'],\r\n        },\r\n        restore: {\r\n          show: !0,\r\n          title: 'Restore',\r\n        },\r\n        saveAsImage: {\r\n          show: !0,\r\n          title: 'Save Image',\r\n        },\r\n      },\r\n    },\r\n    xAxis: [\r\n      {\r\n        type: 'category',\r\n        boundaryGap: !1,\r\n        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n        axisLabel: {\r\n          fontSize: 10,\r\n          color: '#9aa0ac',\r\n        },\r\n      },\r\n    ],\r\n    yAxis: [\r\n      {\r\n        type: 'value',\r\n        axisLabel: {\r\n          fontSize: 10,\r\n          color: '#9aa0ac',\r\n        },\r\n      },\r\n    ],\r\n    series: [\r\n      {\r\n        name: 'Deal',\r\n        type: 'line',\r\n        smooth: !0,\r\n        areaStyle: {},\r\n        emphasis: {\r\n          focus: 'series',\r\n        },\r\n        data: [10, 12, 21, 54, 260, 830, 710],\r\n      },\r\n      {\r\n        name: 'Pre-order',\r\n        type: 'line',\r\n        smooth: !0,\r\n        areaStyle: {},\r\n        emphasis: {\r\n          focus: 'series',\r\n        },\r\n        data: [30, 182, 434, 791, 390, 30, 10],\r\n      },\r\n      {\r\n        name: 'Intent',\r\n        type: 'line',\r\n        smooth: !0,\r\n        areaStyle: {},\r\n        emphasis: {\r\n          focus: 'series',\r\n        },\r\n        data: [1320, 1132, 601, 234, 120, 90, 20],\r\n      },\r\n    ],\r\n    color: ['#9f78ff', '#fa626b', '#32cafe'],\r\n  };\r\n\r\n  pie_chart2: EChartsOption = {\r\n    legend: {\r\n      top: 'bottom',\r\n    },\r\n    toolbox: {\r\n      show: true,\r\n      feature: {\r\n        mark: { show: true },\r\n        dataView: { show: true, readOnly: false },\r\n        restore: { show: true },\r\n        saveAsImage: { show: true },\r\n      },\r\n    },\r\n    series: [\r\n      {\r\n        name: 'Nightingale Chart',\r\n        type: 'pie',\r\n        radius: '55%',\r\n        center: ['50%', '48%'],\r\n        roseType: 'area',\r\n        itemStyle: {\r\n          borderRadius: 8,\r\n        },\r\n        data: [\r\n          { value: 40, name: 'rose 1' },\r\n          { value: 38, name: 'rose 2' },\r\n          { value: 32, name: 'rose 3' },\r\n          { value: 30, name: 'rose 4' },\r\n          { value: 28, name: 'rose 5' },\r\n          { value: 26, name: 'rose 6' },\r\n          { value: 22, name: 'rose 7' },\r\n          { value: 18, name: 'rose 8' },\r\n        ],\r\n      },\r\n    ],\r\n  };\r\n\r\n  // sunburst chart\r\n  sunburst_chart: EChartsOption = {\r\n    series: {\r\n      type: 'sunburst',\r\n      // emphasis: {\r\n      //     focus: 'ancestor'\r\n      // },\r\n      data: [\r\n        {\r\n          name: 'Grandpa',\r\n          children: [\r\n            {\r\n              name: 'Uncle Leo',\r\n              value: 15,\r\n              children: [\r\n                {\r\n                  name: 'Cousin Jack',\r\n                  value: 2,\r\n                },\r\n                {\r\n                  name: 'Cousin Mary',\r\n                  value: 5,\r\n                  children: [\r\n                    {\r\n                      name: 'Jackson',\r\n                      value: 2,\r\n                    },\r\n                  ],\r\n                },\r\n                {\r\n                  name: 'Cousin Ben',\r\n                  value: 4,\r\n                },\r\n              ],\r\n            },\r\n            {\r\n              name: 'Father',\r\n              value: 10,\r\n              children: [\r\n                {\r\n                  name: 'Me',\r\n                  value: 5,\r\n                },\r\n                {\r\n                  name: 'Brother Peter',\r\n                  value: 1,\r\n                },\r\n              ],\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          name: 'Nancy',\r\n          children: [\r\n            {\r\n              name: 'Uncle Nike',\r\n              children: [\r\n                {\r\n                  name: 'Cousin Betty',\r\n                  value: 1,\r\n                },\r\n                {\r\n                  name: 'Cousin Jenny',\r\n                  value: 2,\r\n                },\r\n              ],\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n      radius: [0, '90%'],\r\n      label: {\r\n        rotate: 'radial',\r\n      },\r\n    },\r\n  };\r\n  constructor() {\r\n    // constructor\r\n  }\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Echart'\" [items]=\"['Home','Charts']\" [active_item]=\"'Echart'\">\r\n      </app-breadcrumb>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <!-- Bar chart with line -->\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Bar chart with line</h2>\r\n\r\n          </div>\r\n          <div class=\"body\">\r\n            <div echarts [options]=\"line_bar_chart\" class=\"echart-height\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- line chart -->\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Line Chart</h2>\r\n\r\n          </div>\r\n          <div class=\"body\">\r\n            <div echarts [options]=\"line_chart\" class=\"echart-height\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <!-- Bar chart -->\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Bar chart</h2>\r\n\r\n          </div>\r\n          <div class=\"body\">\r\n            <div echarts [options]=\"bar_chart\" class=\"echart-height\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- Graph Line Chart -->\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Graph Line Chart</h2>\r\n\r\n          </div>\r\n          <div class=\"body\">\r\n            <div echarts [options]=\"graph_line_chart\" class=\"echart-height\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <!-- Pie Chart -->\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Pie Chart</h2>\r\n\r\n          </div>\r\n          <div class=\"body\">\r\n            <div echarts [options]=\"pie_chart\" class=\"echart-height\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- Line Graph Chart -->\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Aria Line Chart</h2>\r\n\r\n          </div>\r\n          <div class=\"body\">\r\n            <div echarts [options]=\"area_line_chart\" class=\"echart-height\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <!-- Pie Chart -->\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Pie Chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div echarts [options]=\"pie_chart2\" class=\"echart-height\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- Sunburst Chart -->\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Sunburst Chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div echarts [options]=\"sunburst_chart\" class=\"echart-height\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AAEA,SAASA,mBAAmB,EAAEC,cAAc,QAAQ,aAAa;AACjE,SAASC,mBAAmB,QAAQ,oDAAoD;;;AAYxF,OAAM,MAAOC,eAAe;EAsgB1BC,YAAA;IArgBA;IACA,KAAAC,cAAc,GAAkB;MAC9BC,IAAI,EAAE;QACJC,GAAG,EAAE,GAAG;QACRC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE;OACP;MACDC,KAAK,EAAE;QACLC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QAC9CC,QAAQ,EAAE;UACRC,SAAS,EAAE;YACTC,KAAK,EAAE;;SAEV;QACDC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV;MACDG,OAAO,EAAE;QACPC,IAAI,EAAE,IAAI;QACVC,WAAW,EAAE,IAAI;QACjBC,iBAAiB,EAAE,KAAK;QACxBC,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE;OACV;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE;UACTX,SAAS,EAAE;YACTC,KAAK,EAAE;;SAEV;QACDF,QAAQ,EAAE;UACRC,SAAS,EAAE;YACTC,KAAK,EAAE;;SAEV;QACDC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV;MACDW,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,KAAK;QACXhB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAC7B,EACD;QACEe,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,IAAI;QACZf,SAAS,EAAE;UACTgB,KAAK,EAAE,CAAC;UACRC,WAAW,EAAE,iBAAiB;UAC9BC,UAAU,EAAE,EAAE;UACdC,aAAa,EAAE;SAChB;QACDrB,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACzBsB,UAAU,EAAE;OACb,EACD;QACEP,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,KAAK;QACXhB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;OAC7B,CACF;MACDG,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;KACxC;IAED;IACA,KAAAoB,UAAU,GAAkB;MAC1B7B,IAAI,EAAE;QACJC,GAAG,EAAE,GAAG;QACRC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE;OACP;MACDC,KAAK,EAAE;QACLC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QAC9CC,QAAQ,EAAE;UACRC,SAAS,EAAE;YACTC,KAAK,EAAE;;SAEV;QACDC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV;MACDG,OAAO,EAAE;QACPC,IAAI,EAAE,IAAI;QACVC,WAAW,EAAE,IAAI;QACjBC,iBAAiB,EAAE,KAAK;QACxBC,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE;OACV;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE;UACTX,SAAS,EAAE;YACTC,KAAK,EAAE;;SAEV;QACDF,QAAQ,EAAE;UACRC,SAAS,EAAE;YACTC,KAAK,EAAE;;SAEV;QACDC,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV;MACDW,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,IAAI;QACZf,SAAS,EAAE;UACTgB,KAAK,EAAE,CAAC;UACRC,WAAW,EAAE,iBAAiB;UAC9BC,UAAU,EAAE,EAAE;UACdC,aAAa,EAAE;SAChB;QACDrB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC9BsB,UAAU,EAAE;QACZ;OACD,EACD;QACEP,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,IAAI;QACZf,SAAS,EAAE;UACTgB,KAAK,EAAE,CAAC;UACRC,WAAW,EAAE,iBAAiB;UAC9BC,UAAU,EAAE,EAAE;UACdC,aAAa,EAAE;SAChB;QACDC,UAAU,EAAE,EAAE;QACd;QACAtB,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAC5B;OACD,CACF;MACDG,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS;KAC7B;IAED;IACA,KAAAqB,SAAS,GAAkB;MACzB9B,IAAI,EAAE;QACJC,GAAG,EAAE,GAAG;QACRC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE;OACP;MACDC,KAAK,EAAE;QACLC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QAE9CI,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV;MACDG,OAAO,EAAE;QACPC,IAAI,EAAE,IAAI;QACVC,WAAW,EAAE,IAAI;QACjBC,iBAAiB,EAAE,KAAK;QACxBC,SAAS,EAAE,WAAW;QACtBC,OAAO,EAAE;OACV;MACDC,KAAK,EAAE;QACLR,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV;MACDW,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,KAAK;QACXhB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAC9B,EAED;QACEe,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,KAAK;QACXhB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;OAC7B,CACF;MACDG,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS;KAC7B;IAED;IACA,KAAAsB,gBAAgB,GAAkB;MAChCnB,OAAO,EAAE;QACPK,OAAO,EAAE;OACV;MACDe,MAAM,EAAE;QACN1B,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;QAC5B2B,SAAS,EAAE;UACTxB,KAAK,EAAE;;OAEV;MACDyB,OAAO,EAAE;QACPrB,IAAI,EAAE,CAAC;OACR;MACDR,KAAK,EAAE,CACL;QACEiB,IAAI,EAAE,UAAU;QAChBhB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QACtDI,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV,CACF;MACDS,KAAK,EAAE,CACL;QACEI,IAAI,EAAE,OAAO;QACbZ,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV,CACF;MACDW,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,KAAK;QACXhB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC;QAChC6B,SAAS,EAAE;UACT7B,IAAI,EAAE,CACJ;YACEgB,IAAI,EAAE,KAAK;YACXD,IAAI,EAAE;WACP,EACD;YACEC,IAAI,EAAE,KAAK;YACXD,IAAI,EAAE;WACP;SAEJ;QACDe,QAAQ,EAAE;UACR9B,IAAI,EAAE,CACJ;YACEgB,IAAI,EAAE;WACP;;OAGN,EAED;QACED,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,KAAK;QACXhB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC9B8B,QAAQ,EAAE;UACR9B,IAAI,EAAE,CACJ;YACEgB,IAAI,EAAE;WACP;;OAGN,CACF;MACDb,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS;KAC7B;IAED;IACA,KAAA4B,SAAS,GAAkB;MACzBzB,OAAO,EAAE;QACPK,OAAO,EAAE,MAAM;QACfqB,SAAS,EAAE;OACZ;MACDN,MAAM,EAAE;QACN1B,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;QACxD2B,SAAS,EAAE;UACTxB,KAAK,EAAE,SAAS;UAChB8B,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;;OAEvB;MAEDnB,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,KAAK;QACXkB,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACtBnC,IAAI,EAAE,CACJ;UACEoC,KAAK,EAAE,GAAG;UACVrB,IAAI,EAAE;SACP,EACD;UACEqB,KAAK,EAAE,GAAG;UACVrB,IAAI,EAAE;SACP,EACD;UACEqB,KAAK,EAAE,GAAG;UACVrB,IAAI,EAAE;SACP,EACD;UACEqB,KAAK,EAAE,GAAG;UACVrB,IAAI,EAAE;SACP,EACD;UACEqB,KAAK,EAAE,GAAG;UACVrB,IAAI,EAAE;SACP;OAEJ,CACF;MACDZ,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;KAC9D;IAED;IACA,KAAAkC,eAAe,GAAkB;MAC/B/B,OAAO,EAAE;QACPK,OAAO,EAAE;OACV;MACDe,MAAM,EAAE;QACN1B,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC;QACrC2B,SAAS,EAAE;UACTxB,KAAK,EAAE,SAAS;UAChB8B,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;;OAEvB;MACDL,OAAO,EAAE;QACPrB,IAAI,EAAE,CAAC,CAAC;QACR+B,OAAO,EAAE;UACPC,SAAS,EAAE;YACThC,IAAI,EAAE,CAAC,CAAC;YACRiC,KAAK,EAAE;cACLC,IAAI,EAAE,MAAM;cACZC,GAAG,EAAE,KAAK;cACVC,KAAK,EAAE;aACR;YACD3B,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO;WAC9B;UACD4B,OAAO,EAAE;YACPrC,IAAI,EAAE,CAAC,CAAC;YACRiC,KAAK,EAAE;WACR;UACDK,WAAW,EAAE;YACXtC,IAAI,EAAE,CAAC,CAAC;YACRiC,KAAK,EAAE;;;OAGZ;MACDzC,KAAK,EAAE,CACL;QACEiB,IAAI,EAAE,UAAU;QAChB8B,WAAW,EAAE,CAAC,CAAC;QACf9C,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QACvDI,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV,CACF;MACDS,KAAK,EAAE,CACL;QACEI,IAAI,EAAE,OAAO;QACbZ,SAAS,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZF,KAAK,EAAE;;OAEV,CACF;MACDW,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,CAAC,CAAC;QACV8B,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE;UACRC,KAAK,EAAE;SACR;QACDjD,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;OACrC,EACD;QACEe,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,CAAC,CAAC;QACV8B,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE;UACRC,KAAK,EAAE;SACR;QACDjD,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE;OACtC,EACD;QACEe,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,CAAC,CAAC;QACV8B,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE;UACRC,KAAK,EAAE;SACR;QACDjD,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE;OACzC,CACF;MACDG,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;KACxC;IAED,KAAA+C,UAAU,GAAkB;MAC1BxB,MAAM,EAAE;QACN/B,GAAG,EAAE;OACN;MACDiC,OAAO,EAAE;QACPrB,IAAI,EAAE,IAAI;QACV+B,OAAO,EAAE;UACPa,IAAI,EAAE;YAAE5C,IAAI,EAAE;UAAI,CAAE;UACpB6C,QAAQ,EAAE;YAAE7C,IAAI,EAAE,IAAI;YAAE8C,QAAQ,EAAE;UAAK,CAAE;UACzCT,OAAO,EAAE;YAAErC,IAAI,EAAE;UAAI,CAAE;UACvBsC,WAAW,EAAE;YAAEtC,IAAI,EAAE;UAAI;;OAE5B;MACDO,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,mBAAmB;QACzBC,IAAI,EAAE,KAAK;QACXkB,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACtBmB,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE;UACTC,YAAY,EAAE;SACf;QACDxD,IAAI,EAAE,CACJ;UAAEoC,KAAK,EAAE,EAAE;UAAErB,IAAI,EAAE;QAAQ,CAAE,EAC7B;UAAEqB,KAAK,EAAE,EAAE;UAAErB,IAAI,EAAE;QAAQ,CAAE,EAC7B;UAAEqB,KAAK,EAAE,EAAE;UAAErB,IAAI,EAAE;QAAQ,CAAE,EAC7B;UAAEqB,KAAK,EAAE,EAAE;UAAErB,IAAI,EAAE;QAAQ,CAAE,EAC7B;UAAEqB,KAAK,EAAE,EAAE;UAAErB,IAAI,EAAE;QAAQ,CAAE,EAC7B;UAAEqB,KAAK,EAAE,EAAE;UAAErB,IAAI,EAAE;QAAQ,CAAE,EAC7B;UAAEqB,KAAK,EAAE,EAAE;UAAErB,IAAI,EAAE;QAAQ,CAAE,EAC7B;UAAEqB,KAAK,EAAE,EAAE;UAAErB,IAAI,EAAE;QAAQ,CAAE;OAEhC;KAEJ;IAED;IACA,KAAA0C,cAAc,GAAkB;MAC9B3C,MAAM,EAAE;QACNE,IAAI,EAAE,UAAU;QAChB;QACA;QACA;QACAhB,IAAI,EAAE,CACJ;UACEe,IAAI,EAAE,SAAS;UACf2C,QAAQ,EAAE,CACR;YACE3C,IAAI,EAAE,WAAW;YACjBqB,KAAK,EAAE,EAAE;YACTsB,QAAQ,EAAE,CACR;cACE3C,IAAI,EAAE,aAAa;cACnBqB,KAAK,EAAE;aACR,EACD;cACErB,IAAI,EAAE,aAAa;cACnBqB,KAAK,EAAE,CAAC;cACRsB,QAAQ,EAAE,CACR;gBACE3C,IAAI,EAAE,SAAS;gBACfqB,KAAK,EAAE;eACR;aAEJ,EACD;cACErB,IAAI,EAAE,YAAY;cAClBqB,KAAK,EAAE;aACR;WAEJ,EACD;YACErB,IAAI,EAAE,QAAQ;YACdqB,KAAK,EAAE,EAAE;YACTsB,QAAQ,EAAE,CACR;cACE3C,IAAI,EAAE,IAAI;cACVqB,KAAK,EAAE;aACR,EACD;cACErB,IAAI,EAAE,eAAe;cACrBqB,KAAK,EAAE;aACR;WAEJ;SAEJ,EACD;UACErB,IAAI,EAAE,OAAO;UACb2C,QAAQ,EAAE,CACR;YACE3C,IAAI,EAAE,YAAY;YAClB2C,QAAQ,EAAE,CACR;cACE3C,IAAI,EAAE,cAAc;cACpBqB,KAAK,EAAE;aACR,EACD;cACErB,IAAI,EAAE,cAAc;cACpBqB,KAAK,EAAE;aACR;WAEJ;SAEJ,CACF;QACDF,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;QAClByB,KAAK,EAAE;UACLC,MAAM,EAAE;;;KAGb;IAEC;EACF;EAAC,QAAAC,CAAA,G;qBAxgBUtE,eAAe;EAAA;EAAA,QAAAuE,EAAA,G;UAAfvE,eAAe;IAAAwE,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,kBAAA,CAJf,CACT9E,cAAc,EAAE,CACjB,GAAA6E,EAAA,CAAAE,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbHR,EAAA,CAAAU,cAAA,iBAAyB;QAInBV,EAAA,CAAAW,SAAA,wBACiB;QACnBX,EAAA,CAAAY,YAAA,EAAM;QACNZ,EAAA,CAAAU,cAAA,aAA0B;QAKdV,EAAA,CAAAa,MAAA,0BAAmB;QAAAb,EAAA,CAAAY,YAAA,EAAK;QAG9BZ,EAAA,CAAAU,cAAA,cAAkB;QAChBV,EAAA,CAAAW,SAAA,cAAoE;QACtEX,EAAA,CAAAY,YAAA,EAAM;QAIVZ,EAAA,CAAAU,cAAA,cAAmD;QAGzCV,EAAA,CAAAa,MAAA,kBAAU;QAAAb,EAAA,CAAAY,YAAA,EAAK;QAGrBZ,EAAA,CAAAU,cAAA,cAAkB;QAChBV,EAAA,CAAAW,SAAA,cAAgE;QAClEX,EAAA,CAAAY,YAAA,EAAM;QAIZZ,EAAA,CAAAU,cAAA,cAA0B;QAKdV,EAAA,CAAAa,MAAA,iBAAS;QAAAb,EAAA,CAAAY,YAAA,EAAK;QAGpBZ,EAAA,CAAAU,cAAA,cAAkB;QAChBV,EAAA,CAAAW,SAAA,cAA+D;QACjEX,EAAA,CAAAY,YAAA,EAAM;QAIVZ,EAAA,CAAAU,cAAA,cAAmD;QAGzCV,EAAA,CAAAa,MAAA,wBAAgB;QAAAb,EAAA,CAAAY,YAAA,EAAK;QAG3BZ,EAAA,CAAAU,cAAA,cAAkB;QAChBV,EAAA,CAAAW,SAAA,cAAsE;QACxEX,EAAA,CAAAY,YAAA,EAAM;QAIZZ,EAAA,CAAAU,cAAA,cAA0B;QAKdV,EAAA,CAAAa,MAAA,iBAAS;QAAAb,EAAA,CAAAY,YAAA,EAAK;QAGpBZ,EAAA,CAAAU,cAAA,cAAkB;QAChBV,EAAA,CAAAW,SAAA,cAA+D;QACjEX,EAAA,CAAAY,YAAA,EAAM;QAIVZ,EAAA,CAAAU,cAAA,cAAmD;QAGzCV,EAAA,CAAAa,MAAA,uBAAe;QAAAb,EAAA,CAAAY,YAAA,EAAK;QAG1BZ,EAAA,CAAAU,cAAA,cAAkB;QAChBV,EAAA,CAAAW,SAAA,cAAqE;QACvEX,EAAA,CAAAY,YAAA,EAAM;QAIZZ,EAAA,CAAAU,cAAA,cAA0B;QAKdV,EAAA,CAAAa,MAAA,iBAAS;QAAAb,EAAA,CAAAY,YAAA,EAAK;QAEpBZ,EAAA,CAAAU,cAAA,cAAkB;QAChBV,EAAA,CAAAW,SAAA,cAAgE;QAClEX,EAAA,CAAAY,YAAA,EAAM;QAIVZ,EAAA,CAAAU,cAAA,cAAmD;QAGzCV,EAAA,CAAAa,MAAA,sBAAc;QAAAb,EAAA,CAAAY,YAAA,EAAK;QAEzBZ,EAAA,CAAAU,cAAA,cAAkB;QAChBV,EAAA,CAAAW,SAAA,cAAoE;QACtEX,EAAA,CAAAY,YAAA,EAAM;;;QArGMZ,EAAA,CAAAc,SAAA,GAAkB;QAAlBd,EAAA,CAAAe,UAAA,mBAAkB,UAAAf,EAAA,CAAAgB,eAAA,KAAAC,GAAA;QAYfjB,EAAA,CAAAc,SAAA,GAA0B;QAA1Bd,EAAA,CAAAe,UAAA,YAAAN,GAAA,CAAAlF,cAAA,CAA0B;QAY1ByE,EAAA,CAAAc,SAAA,GAAsB;QAAtBd,EAAA,CAAAe,UAAA,YAAAN,GAAA,CAAApD,UAAA,CAAsB;QActB2C,EAAA,CAAAc,SAAA,GAAqB;QAArBd,EAAA,CAAAe,UAAA,YAAAN,GAAA,CAAAnD,SAAA,CAAqB;QAYrB0C,EAAA,CAAAc,SAAA,GAA4B;QAA5Bd,EAAA,CAAAe,UAAA,YAAAN,GAAA,CAAAlD,gBAAA,CAA4B;QAc5ByC,EAAA,CAAAc,SAAA,GAAqB;QAArBd,EAAA,CAAAe,UAAA,YAAAN,GAAA,CAAA5C,SAAA,CAAqB;QAYrBmC,EAAA,CAAAc,SAAA,GAA2B;QAA3Bd,EAAA,CAAAe,UAAA,YAAAN,GAAA,CAAAtC,eAAA,CAA2B;QAa3B6B,EAAA,CAAAc,SAAA,GAAsB;QAAtBd,EAAA,CAAAe,UAAA,YAAAN,GAAA,CAAAzB,UAAA,CAAsB;QAWtBgB,EAAA,CAAAc,SAAA,GAA0B;QAA1Bd,EAAA,CAAAe,UAAA,YAAAN,GAAA,CAAAlB,cAAA,CAA0B;;;mBD9FvCnE,mBAAmB,EAAEF,mBAAmB;IAAAgG,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}