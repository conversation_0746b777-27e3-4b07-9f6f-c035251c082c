{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { each } from 'zrender/lib/core/util.js';\nvar BaseBarSeriesModel = /** @class */function (_super) {\n  __extends(BaseBarSeriesModel, _super);\n  function BaseBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BaseBarSeriesModel.type;\n    return _this;\n  }\n  BaseBarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  BaseBarSeriesModel.prototype.getMarkerPosition = function (value, dims, startingAtTick) {\n    var coordSys = this.coordinateSystem;\n    if (coordSys && coordSys.clampData) {\n      // PENDING if clamp ?\n      var clampData_1 = coordSys.clampData(value);\n      var pt_1 = coordSys.dataToPoint(clampData_1);\n      if (startingAtTick) {\n        each(coordSys.getAxes(), function (axis, idx) {\n          // If axis type is category, use tick coords instead\n          if (axis.type === 'category' && dims != null) {\n            var tickCoords = axis.getTicksCoords();\n            var alignTicksWithLabel = axis.getTickModel().get('alignWithLabel');\n            var targetTickId = clampData_1[idx];\n            // The index of rightmost tick of markArea is 1 larger than x1/y1 index\n            var isEnd = dims[idx] === 'x1' || dims[idx] === 'y1';\n            if (isEnd && !alignTicksWithLabel) {\n              targetTickId += 1;\n            }\n            // The only contains one tick, tickCoords is\n            // like [{coord: 0, tickValue: 0}, {coord: 0}]\n            // to the length should always be larger than 1\n            if (tickCoords.length < 2) {\n              return;\n            } else if (tickCoords.length === 2) {\n              // The left value and right value of the axis are\n              // the same. coord is 0 in both items. Use the max\n              // value of the axis as the coord\n              pt_1[idx] = axis.toGlobalCoord(axis.getExtent()[isEnd ? 1 : 0]);\n              return;\n            }\n            var leftCoord = void 0;\n            var coord = void 0;\n            var stepTickValue = 1;\n            for (var i = 0; i < tickCoords.length; i++) {\n              var tickCoord = tickCoords[i].coord;\n              // The last item of tickCoords doesn't contain\n              // tickValue\n              var tickValue = i === tickCoords.length - 1 ? tickCoords[i - 1].tickValue + stepTickValue : tickCoords[i].tickValue;\n              if (tickValue === targetTickId) {\n                coord = tickCoord;\n                break;\n              } else if (tickValue < targetTickId) {\n                leftCoord = tickCoord;\n              } else if (leftCoord != null && tickValue > targetTickId) {\n                coord = (tickCoord + leftCoord) / 2;\n                break;\n              }\n              if (i === 1) {\n                // Here we assume the step of category axes is\n                // the same\n                stepTickValue = tickValue - tickCoords[0].tickValue;\n              }\n            }\n            if (coord == null) {\n              if (!leftCoord) {\n                // targetTickId is smaller than all tick ids in the\n                // visible area, use the leftmost tick coord\n                coord = tickCoords[0].coord;\n              } else if (leftCoord) {\n                // targetTickId is larger than all tick ids in the\n                // visible area, use the rightmost tick coord\n                coord = tickCoords[tickCoords.length - 1].coord;\n              }\n            }\n            pt_1[idx] = axis.toGlobalCoord(coord);\n          }\n        });\n      } else {\n        var data = this.getData();\n        var offset = data.getLayout('offset');\n        var size = data.getLayout('size');\n        var offsetIndex = coordSys.getBaseAxis().isHorizontal() ? 0 : 1;\n        pt_1[offsetIndex] += offset + size / 2;\n      }\n      return pt_1;\n    }\n    return [NaN, NaN];\n  };\n  BaseBarSeriesModel.type = 'series.__base_bar__';\n  BaseBarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // stack: null\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    barMinHeight: 0,\n    barMinAngle: 0,\n    // cursor: null,\n    large: false,\n    largeThreshold: 400,\n    progressive: 3e3,\n    progressiveChunkMode: 'mod'\n  };\n  return BaseBarSeriesModel;\n}(SeriesModel);\nSeriesModel.registerClass(BaseBarSeriesModel);\nexport default BaseBarSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}