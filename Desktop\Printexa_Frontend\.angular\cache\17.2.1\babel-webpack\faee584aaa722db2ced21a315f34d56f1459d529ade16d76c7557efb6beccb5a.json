{"ast": null, "code": "import { RouterLink } from '@angular/router';\nimport { Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckbox } from \"@angular/material/checkbox\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../admin/services/admin.service\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/icon\";\nfunction SigninComponent_Conditional_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_Conditional_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Minimum 3 characters required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_Conditional_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_Conditional_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Minimum 6 characters required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_Conditional_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.error, \" \");\n  }\n}\nfunction SigninComponent_Conditional_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 25);\n  }\n}\nexport class SigninComponent extends UnsubscribeOnDestroyAdapter {\n  constructor(formBuilder, route, router, adminService) {\n    super();\n    this.formBuilder = formBuilder;\n    this.route = route;\n    this.router = router;\n    this.adminService = adminService;\n    this.submitted = false;\n    this.loading = false;\n    this.error = '';\n    this.hide = true;\n  }\n  ngOnInit() {\n    this.authForm = this.formBuilder.group({\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  get f() {\n    return this.authForm.controls;\n  }\n  onSubmit() {\n    this.submitted = true;\n    this.error = '';\n    this.loading = true;\n    if (this.authForm.invalid) {\n      this.error = 'Veuillez remplir correctement tous les champs';\n      this.loading = false;\n      return;\n    }\n    const credentials = {\n      username: this.authForm.value.username.trim(),\n      password: this.authForm.value.password\n    };\n    this.subs.sink = this.adminService.signIn(credentials).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.success) {\n          console.log(\"Login success\", response);\n          // Récupération du returnUrl ou redirection par défaut\n          const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '../';\n          console.log(\"Redirecting to:\", returnUrl);\n          // Navigation avec gestion d'erreur\n          this.router.navigateByUrl(returnUrl).catch(() => {\n            // Fallback si la navigation échoue\n            this.router.navigate(['/admin']);\n          });\n        } else {\n          this.error = response.error || 'Échec de la connexion';\n        }\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.message || 'Une erreur est survenue lors de la connexion';\n        console.error('Login error:', err);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function SigninComponent_Factory(t) {\n    return new (t || SigninComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AdminService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SigninComponent,\n    selectors: [[\"app-signin\"]],\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    decls: 45,\n    vars: 14,\n    consts: [[1, \"auth-container\"], [1, \"row\", \"auth-main\"], [1, \"col-sm-6\", \"px-0\", \"d-none\", \"d-sm-block\"], [1, \"left-img\", 2, \"background-image\", \"url(assets/images/pages/bg-01.png)\"], [1, \"col-sm-6\", \"auth-form-section\"], [1, \"form-section\"], [1, \"auth-wrapper\"], [1, \"welcome-msg\"], [1, \"auth-signup-text\", \"text-muted\"], [1, \"login-title\"], [1, \"validate-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-12\", \"mb-3\"], [\"appearance\", \"outline\", 1, \"w-100\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"required\", \"\"], [\"matSuffix\", \"\", 1, \"material-icons-outlined\", \"color-icon\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"required\", \"\", 3, \"type\"], [\"type\", \"button\", \"mat-icon-button\", \"\", \"matSuffix\", \"\", 3, \"click\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [\"routerLink\", \"/admin/forgot-password\", 1, \"text-primary\"], [\"class\", \"alert alert-danger mb-3\", \"role\", \"alert\"], [1, \"text-center\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"w-100\", \"py-2\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"mb-3\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function SigninComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"h2\", 7);\n        i0.ɵɵtext(8, \"Welcome to Printexa Admin\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\", 8);\n        i0.ɵɵtext(10, \"Admin access only\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"h2\", 9);\n        i0.ɵɵtext(12, \"Admin Sign in\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"form\", 10);\n        i0.ɵɵlistener(\"ngSubmit\", function SigninComponent_Template_form_ngSubmit_13_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12)(16, \"mat-form-field\", 13)(17, \"mat-label\");\n        i0.ɵɵtext(18, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(19, \"input\", 14);\n        i0.ɵɵelementStart(20, \"mat-icon\", 15);\n        i0.ɵɵtext(21, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(22, SigninComponent_Conditional_22_Template, 2, 0, \"mat-error\")(23, SigninComponent_Conditional_23_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(24, \"div\", 11)(25, \"div\", 12)(26, \"mat-form-field\", 13)(27, \"mat-label\");\n        i0.ɵɵtext(28, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(29, \"input\", 16);\n        i0.ɵɵelementStart(30, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function SigninComponent_Template_button_click_30_listener() {\n          return ctx.hide = !ctx.hide;\n        });\n        i0.ɵɵelementStart(31, \"mat-icon\");\n        i0.ɵɵtext(32);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(33, SigninComponent_Conditional_33_Template, 2, 0, \"mat-error\")(34, SigninComponent_Conditional_34_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(35, \"div\", 18)(36, \"mat-checkbox\");\n        i0.ɵɵtext(37, \"Remember me\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"a\", 19);\n        i0.ɵɵtext(39, \"Forgot Password?\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(40, SigninComponent_Conditional_40_Template, 2, 1, \"div\", 20);\n        i0.ɵɵelementStart(41, \"div\", 21)(42, \"button\", 22);\n        i0.ɵɵtemplate(43, SigninComponent_Conditional_43_Template, 1, 0, \"span\", 23);\n        i0.ɵɵtext(44, \" Login \");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"formGroup\", ctx.authForm);\n        i0.ɵɵadvance(9);\n        i0.ɵɵconditional(22, (ctx.f[\"username\"].errors == null ? null : ctx.f[\"username\"].errors[\"required\"]) && (ctx.f[\"username\"].dirty || ctx.f[\"username\"].touched || ctx.submitted) ? 22 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(23, (ctx.f[\"username\"].errors == null ? null : ctx.f[\"username\"].errors[\"minlength\"]) && (ctx.f[\"username\"].dirty || ctx.f[\"username\"].touched || ctx.submitted) ? 23 : -1);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"type\", ctx.hide ? \"password\" : \"text\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hide);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.hide ? \"visibility_off\" : \"visibility\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(33, (ctx.f[\"password\"].errors == null ? null : ctx.f[\"password\"].errors[\"required\"]) && (ctx.f[\"password\"].dirty || ctx.f[\"password\"].touched || ctx.submitted) ? 33 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(34, (ctx.f[\"password\"].errors == null ? null : ctx.f[\"password\"].errors[\"minlength\"]) && (ctx.f[\"password\"].dirty || ctx.f[\"password\"].touched || ctx.submitted) ? 34 : -1);\n        i0.ɵɵadvance(6);\n        i0.ɵɵconditional(40, ctx.error ? 40 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"spinner\", ctx.loading);\n        i0.ɵɵproperty(\"disabled\", ctx.authForm.invalid || ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(43, ctx.loading ? 43 : -1);\n      }\n    },\n    dependencies: [RouterLink, MatButtonModule, i4.MatButton, i4.MatIconButton, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, MatFormFieldModule, i5.MatFormField, i5.MatLabel, i5.MatError, i5.MatSuffix, MatInputModule, i6.MatInput, MatIconModule, i7.MatIcon, MatCheckbox],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["RouterLink", "Validators", "FormsModule", "ReactiveFormsModule", "UnsubscribeOnDestroyAdapter", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatButtonModule", "MatCheckbox", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r4", "error", "ɵɵelement", "SigninComponent", "constructor", "formBuilder", "route", "router", "adminService", "submitted", "loading", "hide", "ngOnInit", "authForm", "group", "username", "required", "<PERSON><PERSON><PERSON><PERSON>", "password", "f", "controls", "onSubmit", "invalid", "credentials", "value", "trim", "subs", "sink", "signIn", "subscribe", "next", "response", "success", "console", "log", "returnUrl", "snapshot", "queryParams", "navigateByUrl", "catch", "navigate", "err", "message", "_", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "i2", "ActivatedRoute", "Router", "i3", "AdminService", "_2", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SigninComponent_Template", "rf", "ctx", "ɵɵlistener", "SigninComponent_Template_form_ngSubmit_13_listener", "ɵɵtemplate", "SigninComponent_Conditional_22_Template", "SigninComponent_Conditional_23_Template", "SigninComponent_Template_button_click_30_listener", "SigninComponent_Conditional_33_Template", "SigninComponent_Conditional_34_Template", "SigninComponent_Conditional_40_Template", "SigninComponent_Conditional_43_Template", "ɵɵproperty", "ɵɵconditional", "errors", "dirty", "touched", "ɵɵattribute", "ɵɵtextInterpolate", "ɵɵclassProp", "i4", "MatButton", "MatIconButton", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "i5", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i6", "MatInput", "i7", "MatIcon", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\authentication\\signin\\signin.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\authentication\\signin\\signin.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router, ActivatedRoute, RouterLink } from '@angular/router';\r\nimport { UntypedFormBuilder, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { AdminService } from '../../admin/services/admin.service';\r\nimport { MatCheckbox } from \"@angular/material/checkbox\";\r\n\r\n@Component({\r\n    selector: 'app-signin',\r\n    templateUrl: './signin.component.html',\r\n    styleUrls: ['./signin.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        RouterLink,\r\n        MatButtonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        MatFormFieldModule,\r\n        MatInputModule,\r\n        MatIconModule,\r\n        MatCheckbox\r\n    ],\r\n})\r\nexport class SigninComponent extends UnsubscribeOnDestroyAdapter implements OnInit {\r\n  authForm!: UntypedFormGroup;\r\n  submitted = false;\r\n  loading = false;\r\n  error = '';\r\n  hide = true;\r\n\r\n  constructor(\r\n    private formBuilder: UntypedFormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private adminService: AdminService\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.authForm = this.formBuilder.group({\r\n      username: ['', [Validators.required, Validators.minLength(3)]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n    });\r\n  }\r\n\r\n  get f() {\r\n    return this.authForm.controls;\r\n  }\r\n\r\nonSubmit() {\r\n  this.submitted = true;\r\n  this.error = '';\r\n  this.loading = true;\r\n\r\n  if (this.authForm.invalid) {\r\n    this.error = 'Veuillez remplir correctement tous les champs';\r\n    this.loading = false;\r\n    return;\r\n  }\r\n\r\n  const credentials = {\r\n    username: this.authForm.value.username.trim(),\r\n    password: this.authForm.value.password\r\n  };\r\n\r\n  this.subs.sink = this.adminService.signIn(credentials).subscribe({\r\n    next: (response) => {\r\n      this.loading = false;\r\n      if (response.success) {\r\n        console.log(\"Login success\", response);\r\n        \r\n        // Récupération du returnUrl ou redirection par défaut\r\n        const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '../';\r\n        console.log(\"Redirecting to:\", returnUrl);\r\n        \r\n        // Navigation avec gestion d'erreur\r\n        this.router.navigateByUrl(returnUrl).catch(() => {\r\n          // Fallback si la navigation échoue\r\n          this.router.navigate(['/admin']);\r\n        });\r\n      } else {\r\n        this.error = response.error || 'Échec de la connexion';\r\n      }\r\n    },\r\n    error: (err) => {\r\n      this.loading = false;\r\n      this.error = err.message || 'Une erreur est survenue lors de la connexion';\r\n      console.error('Login error:', err);\r\n    }\r\n  });\r\n}}", "<div class=\"auth-container\">\r\n  <div class=\"row auth-main\">\r\n    <div class=\"col-sm-6 px-0 d-none d-sm-block\">\r\n      <div class=\"left-img\" style=\"background-image: url(assets/images/pages/bg-01.png);\"></div>\r\n    </div>\r\n    <div class=\"col-sm-6 auth-form-section\">\r\n      <div class=\"form-section\">\r\n        <div class=\"auth-wrapper\">\r\n          <h2 class=\"welcome-msg\">Welcome to Printexa Admin</h2>\r\n          <p class=\"auth-signup-text text-muted\">Admin access only</p>\r\n          \r\n          <h2 class=\"login-title\">Admin Sign in</h2>\r\n          \r\n          <form class=\"validate-form\" [formGroup]=\"authForm\" (ngSubmit)=\"onSubmit()\">\r\n            <!-- Username Field -->\r\n            <div class=\"row\">\r\n              <div class=\"col-12 mb-3\">\r\n                <mat-form-field appearance=\"outline\" class=\"w-100\">\r\n                  <mat-label>Username</mat-label>\r\n                  <input matInput formControlName=\"username\" required>\r\n                  <mat-icon matSuffix class=\"material-icons-outlined color-icon\">person</mat-icon>\r\n                  @if (f['username'].errors?.['required'] && (f['username'].dirty || f['username'].touched || submitted)) {\r\n                    <mat-error>Username is required</mat-error>\r\n                  }\r\n                  @if (f['username'].errors?.['minlength'] && (f['username'].dirty || f['username'].touched || submitted)) {\r\n                    <mat-error>Minimum 3 characters required</mat-error>\r\n                  }\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- Password Field -->\r\n            <div class=\"row\">\r\n              <div class=\"col-12 mb-3\">\r\n                <mat-form-field appearance=\"outline\" class=\"w-100\">\r\n                  <mat-label>Password</mat-label>\r\n                  <input matInput [type]=\"hide ? 'password' : 'text'\" formControlName=\"password\" required>\r\n                  <button type=\"button\" mat-icon-button matSuffix (click)=\"hide = !hide\" [attr.aria-label]=\"'Hide password'\" [attr.aria-pressed]=\"hide\">\r\n                    <mat-icon>{{ hide ? 'visibility_off' : 'visibility' }}</mat-icon>\r\n                  </button>\r\n                  @if (f['password'].errors?.['required'] && (f['password'].dirty || f['password'].touched || submitted)) {\r\n                    <mat-error>Password is required</mat-error>\r\n                  }\r\n                  @if (f['password'].errors?.['minlength'] && (f['password'].dirty || f['password'].touched || submitted)) {\r\n                    <mat-error>Minimum 6 characters required</mat-error>\r\n                  }\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- Remember Me & Forgot Password -->\r\n            <div class=\"d-flex justify-content-between align-items-center mb-4\">\r\n              <mat-checkbox>Remember me</mat-checkbox>\r\n              <a class=\"text-primary\" routerLink=\"/admin/forgot-password\">Forgot Password?</a>\r\n            </div>\r\n            \r\n            <!-- Error Message -->\r\n            @if (error) {\r\n              <div class=\"alert alert-danger mb-3\" role=\"alert\">\r\n                {{ error }}\r\n              </div>\r\n            }\r\n            \r\n            <!-- Submit Button -->\r\n            <div class=\"text-center\">\r\n              <button mat-raised-button \r\n                      color=\"primary\" \r\n                      type=\"submit\"\r\n                      class=\"w-100 py-2\"\r\n                      [disabled]=\"authForm.invalid || loading\"\r\n                      [class.spinner]=\"loading\">\r\n                @if (loading) {\r\n                  <span class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                }\r\n                Login\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,iBAAiB;AACpE,SAA+CC,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACnH,SAASC,2BAA2B,QAAQ,SAAS;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,WAAW,QAAQ,4BAA4B;;;;;;;;;;;ICapCC,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAG3CH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,oCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAgBpDH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAG3CH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,oCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAc1DH,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAYIP,EAAA,CAAAQ,SAAA,eAA4F;;;AD7C9G,OAAM,MAAOC,eAAgB,SAAQf,2BAA2B;EAO9DgB,YACUC,WAA+B,EAC/BC,KAAqB,EACrBC,MAAc,EACdC,YAA0B;IAElC,KAAK,EAAE;IALC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IATtB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAT,KAAK,GAAG,EAAE;IACV,KAAAU,IAAI,GAAG,IAAI;EASX;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEA,IAAIE,CAACA,CAAA;IACH,OAAO,IAAI,CAACN,QAAQ,CAACO,QAAQ;EAC/B;EAEFC,QAAQA,CAAA;IACN,IAAI,CAACZ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,KAAK,GAAG,EAAE;IACf,IAAI,CAACS,OAAO,GAAG,IAAI;IAEnB,IAAI,IAAI,CAACG,QAAQ,CAACS,OAAO,EAAE;MACzB,IAAI,CAACrB,KAAK,GAAG,+CAA+C;MAC5D,IAAI,CAACS,OAAO,GAAG,KAAK;MACpB;;IAGF,MAAMa,WAAW,GAAG;MAClBR,QAAQ,EAAE,IAAI,CAACF,QAAQ,CAACW,KAAK,CAACT,QAAQ,CAACU,IAAI,EAAE;MAC7CP,QAAQ,EAAE,IAAI,CAACL,QAAQ,CAACW,KAAK,CAACN;KAC/B;IAED,IAAI,CAACQ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACnB,YAAY,CAACoB,MAAM,CAACL,WAAW,CAAC,CAACM,SAAS,CAAC;MAC/DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACrB,OAAO,GAAG,KAAK;QACpB,IAAIqB,QAAQ,CAACC,OAAO,EAAE;UACpBC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEH,QAAQ,CAAC;UAEtC;UACA,MAAMI,SAAS,GAAG,IAAI,CAAC7B,KAAK,CAAC8B,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,KAAK;UACvEJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,SAAS,CAAC;UAEzC;UACA,IAAI,CAAC5B,MAAM,CAAC+B,aAAa,CAACH,SAAS,CAAC,CAACI,KAAK,CAAC,MAAK;YAC9C;YACA,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;UAClC,CAAC,CAAC;SACH,MAAM;UACL,IAAI,CAACvC,KAAK,GAAG8B,QAAQ,CAAC9B,KAAK,IAAI,uBAAuB;;MAE1D,CAAC;MACDA,KAAK,EAAGwC,GAAG,IAAI;QACb,IAAI,CAAC/B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACT,KAAK,GAAGwC,GAAG,CAACC,OAAO,IAAI,8CAA8C;QAC1ET,OAAO,CAAChC,KAAK,CAAC,cAAc,EAAEwC,GAAG,CAAC;MACpC;KACD,CAAC;EACJ;EAAC,QAAAE,CAAA,G;qBApEYxC,eAAe,EAAAT,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtD,EAAA,CAAAkD,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAAvD,EAAA,CAAAkD,iBAAA,CAAAM,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAfjD,eAAe;IAAAkD,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA7D,EAAA,CAAA8D,0BAAA,EAAA9D,EAAA,CAAA+D,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3B5BrE,EAAA,CAAAC,cAAA,aAA4B;QAGtBD,EAAA,CAAAQ,SAAA,aAA0F;QAC5FR,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAAwC;QAGVD,EAAA,CAAAE,MAAA,gCAAyB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACtDH,EAAA,CAAAC,cAAA,WAAuC;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE5DH,EAAA,CAAAC,cAAA,aAAwB;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE1CH,EAAA,CAAAC,cAAA,gBAA2E;QAAxBD,EAAA,CAAAuE,UAAA,sBAAAC,mDAAA;UAAA,OAAYF,GAAA,CAAA3C,QAAA,EAAU;QAAA,EAAC;QAExE3B,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAQ,SAAA,iBAAoD;QACpDR,EAAA,CAAAC,cAAA,oBAA+D;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChFH,EAAA,CAAAyE,UAAA,KAAAC,uCAAA,oBAEC,KAAAC,uCAAA;QAIH3E,EAAA,CAAAG,YAAA,EAAiB;QAKrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAQ,SAAA,iBAAwF;QACxFR,EAAA,CAAAC,cAAA,kBAAsI;QAAtFD,EAAA,CAAAuE,UAAA,mBAAAK,kDAAA;UAAA,OAAAN,GAAA,CAAArD,IAAA,IAAAqD,GAAA,CAAArD,IAAA;QAAA,EAAsB;QACpEjB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,IAA4C;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAEnEH,EAAA,CAAAyE,UAAA,KAAAI,uCAAA,oBAEC,KAAAC,uCAAA;QAIH9E,EAAA,CAAAG,YAAA,EAAiB;QAKrBH,EAAA,CAAAC,cAAA,eAAoE;QACpDD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAe;QACxCH,EAAA,CAAAC,cAAA,aAA4D;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAIlFH,EAAA,CAAAyE,UAAA,KAAAM,uCAAA,kBAIC;QAGD/E,EAAA,CAAAC,cAAA,eAAyB;QAOrBD,EAAA,CAAAyE,UAAA,KAAAO,uCAAA,mBAEC;QACDhF,EAAA,CAAAE,MAAA,eACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QA9DeH,EAAA,CAAAI,SAAA,IAAsB;QAAtBJ,EAAA,CAAAiF,UAAA,cAAAX,GAAA,CAAAnD,QAAA,CAAsB;QAQ1CnB,EAAA,CAAAI,SAAA,GAEC;QAFDJ,EAAA,CAAAkF,aAAA,MAAAZ,GAAA,CAAA7C,CAAA,aAAA0D,MAAA,kBAAAb,GAAA,CAAA7C,CAAA,aAAA0D,MAAA,kBAAAb,GAAA,CAAA7C,CAAA,aAAA2D,KAAA,IAAAd,GAAA,CAAA7C,CAAA,aAAA4D,OAAA,IAAAf,GAAA,CAAAvD,SAAA,YAEC;QACDf,EAAA,CAAAI,SAAA,EAEC;QAFDJ,EAAA,CAAAkF,aAAA,MAAAZ,GAAA,CAAA7C,CAAA,aAAA0D,MAAA,kBAAAb,GAAA,CAAA7C,CAAA,aAAA0D,MAAA,mBAAAb,GAAA,CAAA7C,CAAA,aAAA2D,KAAA,IAAAd,GAAA,CAAA7C,CAAA,aAAA4D,OAAA,IAAAf,GAAA,CAAAvD,SAAA,YAEC;QAUef,EAAA,CAAAI,SAAA,GAAmC;QAAnCJ,EAAA,CAAAiF,UAAA,SAAAX,GAAA,CAAArD,IAAA,uBAAmC;QACoBjB,EAAA,CAAAI,SAAA,EAAmC;QAAnCJ,EAAA,CAAAsF,WAAA,+BAAmC,iBAAAhB,GAAA,CAAArD,IAAA;QAC9FjB,EAAA,CAAAI,SAAA,GAA4C;QAA5CJ,EAAA,CAAAuF,iBAAA,CAAAjB,GAAA,CAAArD,IAAA,mCAA4C;QAExDjB,EAAA,CAAAI,SAAA,EAEC;QAFDJ,EAAA,CAAAkF,aAAA,MAAAZ,GAAA,CAAA7C,CAAA,aAAA0D,MAAA,kBAAAb,GAAA,CAAA7C,CAAA,aAAA0D,MAAA,kBAAAb,GAAA,CAAA7C,CAAA,aAAA2D,KAAA,IAAAd,GAAA,CAAA7C,CAAA,aAAA4D,OAAA,IAAAf,GAAA,CAAAvD,SAAA,YAEC;QACDf,EAAA,CAAAI,SAAA,EAEC;QAFDJ,EAAA,CAAAkF,aAAA,MAAAZ,GAAA,CAAA7C,CAAA,aAAA0D,MAAA,kBAAAb,GAAA,CAAA7C,CAAA,aAAA0D,MAAA,mBAAAb,GAAA,CAAA7C,CAAA,aAAA2D,KAAA,IAAAd,GAAA,CAAA7C,CAAA,aAAA4D,OAAA,IAAAf,GAAA,CAAAvD,SAAA,YAEC;QAYPf,EAAA,CAAAI,SAAA,GAIC;QAJDJ,EAAA,CAAAkF,aAAA,KAAAZ,GAAA,CAAA/D,KAAA,WAIC;QASSP,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAwF,WAAA,YAAAlB,GAAA,CAAAtD,OAAA,CAAyB;QADzBhB,EAAA,CAAAiF,UAAA,aAAAX,GAAA,CAAAnD,QAAA,CAAAS,OAAA,IAAA0C,GAAA,CAAAtD,OAAA,CAAwC;QAE9ChB,EAAA,CAAAI,SAAA,EAEC;QAFDJ,EAAA,CAAAkF,aAAA,KAAAZ,GAAA,CAAAtD,OAAA,WAEC;;;mBDxDT1B,UAAU,EACVQ,eAAe,EAAA2F,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfnG,WAAW,EAAA2D,EAAA,CAAAyC,aAAA,EAAAzC,EAAA,CAAA0C,oBAAA,EAAA1C,EAAA,CAAA2C,eAAA,EAAA3C,EAAA,CAAA4C,oBAAA,EAAA5C,EAAA,CAAA6C,iBAAA,EACXvG,mBAAmB,EAAA0D,EAAA,CAAA8C,kBAAA,EAAA9C,EAAA,CAAA+C,eAAA,EACnBrG,kBAAkB,EAAAsG,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClB3G,cAAc,EAAA4G,EAAA,CAAAC,QAAA,EACd9G,aAAa,EAAA+G,EAAA,CAAAC,OAAA,EACb5G,WAAW;IAAA6G,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}