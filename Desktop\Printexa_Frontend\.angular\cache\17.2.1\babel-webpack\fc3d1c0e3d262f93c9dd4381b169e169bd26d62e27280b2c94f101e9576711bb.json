{"ast": null, "code": "const d = new Date();\nconst day = d.getDate();\nconst month = d.getMonth();\nconst year = d.getFullYear();\nexport const INITIAL_EVENTS = [{\n  id: \"event1\",\n  title: \"All Day Event\",\n  start: new Date(year, month, 1, 0, 0),\n  end: new Date(year, month, 1, 23, 59),\n  className: \"fc-event-success\",\n  groupId: \"work\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see.\"\n}, {\n  id: \"event2\",\n  title: \"Break\",\n  start: new Date(year, month, day + 28, 16, 0),\n  end: new Date(year, month, day + 29, 20, 0),\n  allDay: false,\n  className: \"fc-event-primary\",\n  groupId: \"important\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see. \"\n}, {\n  id: \"event3\",\n  title: \"Shopping\",\n  start: new Date(year, month, day + 4, 12, 0),\n  end: new Date(year, month, day + 4, 20, 0),\n  allDay: false,\n  className: \"fc-event-warning\",\n  groupId: \"personal\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see. \"\n}, {\n  id: \"event4\",\n  title: \"Meeting\",\n  start: new Date(year, month, day + 14, 10, 30),\n  end: new Date(year, month, day + 16, 20, 0),\n  allDay: false,\n  className: \"fc-event-success\",\n  groupId: \"work\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see.\"\n}, {\n  id: \"event5\",\n  title: \"Lunch\",\n  start: new Date(year, month, day, 11, 0),\n  end: new Date(year, month, day, 14, 0),\n  allDay: false,\n  className: \"fc-event-primary\",\n  groupId: \"important\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see.\"\n}, {\n  id: \"event6\",\n  title: \"Meeting\",\n  start: new Date(year, month, day + 2, 12, 30),\n  end: new Date(year, month, day + 2, 14, 30),\n  allDay: false,\n  className: \"fc-event-success\",\n  groupId: \"work\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see.\"\n}, {\n  id: \"event7\",\n  title: \"Birthday Party\",\n  start: new Date(year, month, day + 17, 19, 0),\n  end: new Date(year, month, day + 17, 19, 30),\n  allDay: false,\n  className: \"fc-event-warning\",\n  groupId: \"personal\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see.\"\n}, {\n  id: \"event8\",\n  title: \"Go to Delhi\",\n  start: new Date(year, month, day + -5, 10, 0),\n  end: new Date(year, month, day + -4, 10, 30),\n  allDay: false,\n  className: \"fc-event-danger\",\n  groupId: \"travel\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see.\"\n}, {\n  id: \"event9\",\n  title: \"Get To Gather\",\n  start: new Date(year, month, day + 6, 10, 0),\n  end: new Date(year, month, day + 7, 10, 30),\n  allDay: false,\n  className: \"fc-event-info\",\n  groupId: \"friends\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see.\"\n}, {\n  id: \"event10\",\n  title: \"Collage Party\",\n  start: new Date(year, month, day + 20, 10, 0),\n  end: new Date(year, month, day + 20, 10, 30),\n  allDay: false,\n  className: \"fc-event-info\",\n  groupId: \"friends\",\n  details: \"Her extensive perceived may any sincerity extremity. Indeed add rather may pretty see.\"\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}