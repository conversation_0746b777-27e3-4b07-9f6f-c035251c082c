{"ast": null, "code": "import getTag from './_getTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar setTag = '[object Set]';\n\n/**\n * The base implementation of `_.isSet` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n */\nfunction baseIsSet(value) {\n  return isObjectLike(value) && getTag(value) == setTag;\n}\nexport default baseIsSet;", "map": {"version": 3, "names": ["getTag", "isObjectLike", "setTag", "baseIsSet", "value"], "sources": ["C:/Users/<USER>/mian/node_modules/lodash-es/_baseIsSet.js"], "sourcesContent": ["import getTag from './_getTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar setTag = '[object Set]';\n\n/**\n * The base implementation of `_.isSet` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n */\nfunction baseIsSet(value) {\n  return isObjectLike(value) && getTag(value) == setTag;\n}\n\nexport default baseIsSet;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,YAAY,MAAM,mBAAmB;;AAE5C;AACA,IAAIC,MAAM,GAAG,cAAc;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOH,YAAY,CAACG,KAAK,CAAC,IAAIJ,MAAM,CAACI,KAAK,CAAC,IAAIF,MAAM;AACvD;AAEA,eAAeC,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}