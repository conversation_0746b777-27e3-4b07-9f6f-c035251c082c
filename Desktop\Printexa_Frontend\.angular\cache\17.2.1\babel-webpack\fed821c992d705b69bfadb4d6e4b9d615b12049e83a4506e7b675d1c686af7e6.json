{"ast": null, "code": "export class MyLeaves {\n  constructor(myLeaves) {\n    {\n      this.id = myLeaves.id || this.getRandomID();\n      this.halfDay = myLeaves.halfDay || '';\n      this.applyDate = myLeaves.applyDate || '';\n      this.fromDate = myLeaves.fromDate || '';\n      this.toDate = myLeaves.toDate || '';\n      this.reason = myLeaves.reason || '';\n      this.type = myLeaves.type || '';\n      this.status = myLeaves.status || '';\n    }\n  }\n  getRandomID() {\n    const S4 = () => {\n      return (1 + Math.random()) * 0x10000 | 0;\n    };\n    return S4() + S4();\n  }\n}", "map": {"version": 3, "names": ["MyLeaves", "constructor", "myLeaves", "id", "getRandomID", "halfDay", "applyDate", "fromDate", "toDate", "reason", "type", "status", "S4", "Math", "random"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\employee\\my-leaves\\my-leaves.model.ts"], "sourcesContent": ["export class MyLeaves {\r\n  id: number;\r\n  halfDay: string;\r\n  applyDate: string;\r\n  fromDate: string;\r\n  toDate: string;\r\n  reason: string;\r\n  type: string;\r\n  status: string;\r\n  constructor(myLeaves: MyLeaves) {\r\n    {\r\n      this.id = myLeaves.id || this.getRandomID();\r\n      this.halfDay = myLeaves.halfDay || '';\r\n      this.applyDate = myLeaves.applyDate || '';\r\n      this.fromDate = myLeaves.fromDate || '';\r\n      this.toDate = myLeaves.toDate || '';\r\n      this.reason = myLeaves.reason || '';\r\n      this.type = myLeaves.type || '';\r\n      this.status = myLeaves.status || '';\r\n    }\r\n  }\r\n  public getRandomID(): number {\r\n    const S4 = () => {\r\n      return ((1 + Math.random()) * 0x10000) | 0;\r\n    };\r\n    return S4() + S4();\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAM,MAAOA,QAAQ;EASnBC,YAAYC,QAAkB;IAC5B;MACE,IAAI,CAACC,EAAE,GAAGD,QAAQ,CAACC,EAAE,IAAI,IAAI,CAACC,WAAW,EAAE;MAC3C,IAAI,CAACC,OAAO,GAAGH,QAAQ,CAACG,OAAO,IAAI,EAAE;MACrC,IAAI,CAACC,SAAS,GAAGJ,QAAQ,CAACI,SAAS,IAAI,EAAE;MACzC,IAAI,CAACC,QAAQ,GAAGL,QAAQ,CAACK,QAAQ,IAAI,EAAE;MACvC,IAAI,CAACC,MAAM,GAAGN,QAAQ,CAACM,MAAM,IAAI,EAAE;MACnC,IAAI,CAACC,MAAM,GAAGP,QAAQ,CAACO,MAAM,IAAI,EAAE;MACnC,IAAI,CAACC,IAAI,GAAGR,QAAQ,CAACQ,IAAI,IAAI,EAAE;MAC/B,IAAI,CAACC,MAAM,GAAGT,QAAQ,CAACS,MAAM,IAAI,EAAE;;EAEvC;EACOP,WAAWA,CAAA;IAChB,MAAMQ,EAAE,GAAGA,CAAA,KAAK;MACd,OAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,IAAI,OAAO,GAAI,CAAC;IAC5C,CAAC;IACD,OAAOF,EAAE,EAAE,GAAGA,EAAE,EAAE;EACpB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}