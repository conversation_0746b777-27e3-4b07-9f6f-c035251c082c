{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nlet AuthGuard = class AuthGuard {\n  constructor(adminService, router) {\n    this.adminService = adminService;\n    this.router = router;\n  }\n  canActivate(next, state) {\n    if (this.adminService.isAuthenticated()) {\n      return true;\n    }\n    // Redirige vers la page de login avec l'URL de retour\n    this.router.navigate(['/authentication/signin'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n};\nAuthGuard = __decorate([Injectable({\n  providedIn: 'root'\n})], AuthGuard);\nexport { AuthGuard };", "map": {"version": 3, "names": ["Injectable", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "adminService", "router", "canActivate", "next", "state", "isAuthenticated", "navigate", "queryParams", "returnUrl", "url", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\core\\guard\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\r\nimport { AdminService } from '../s';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthGuard implements CanActivate {\r\n  constructor(private adminService: AdminService, private router: Router) {}\r\n\r\n  canActivate(\r\n    next: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot): boolean {\r\n    \r\n    if (this.adminService.isAuthenticated()) {\r\n      return true;\r\n    }\r\n\r\n    // Redirige vers la page de login avec l'URL de retour\r\n    this.router.navigate(['/authentication/signin'], { \r\n      queryParams: { returnUrl: state.url } \r\n    });\r\n    return false;\r\n  }\r\n}"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAOnC,IAAMC,SAAS,GAAf,MAAMA,SAAS;EACpBC,YAAoBC,YAA0B,EAAUC,MAAc;IAAlD,KAAAD,YAAY,GAAZA,YAAY;IAAwB,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEzEC,WAAWA,CACTC,IAA4B,EAC5BC,KAA0B;IAE1B,IAAI,IAAI,CAACJ,YAAY,CAACK,eAAe,EAAE,EAAE;MACvC,OAAO,IAAI;;IAGb;IACA,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,wBAAwB,CAAC,EAAE;MAC/CC,WAAW,EAAE;QAAEC,SAAS,EAAEJ,KAAK,CAACK;MAAG;KACpC,CAAC;IACF,OAAO,KAAK;EACd;CACD;AAjBYX,SAAS,GAAAY,UAAA,EAHrBb,UAAU,CAAC;EACVc,UAAU,EAAE;CACb,CAAC,C,EACWb,SAAS,CAiBrB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}