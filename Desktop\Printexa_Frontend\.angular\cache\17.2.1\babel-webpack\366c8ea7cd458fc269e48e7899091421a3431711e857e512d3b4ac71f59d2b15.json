{"ast": null, "code": "import { ProduitComponent } from \"./produit.component\";\nimport { FormProduitComponent } from \"./form-produit/form-produit.component\";\nimport { ProduitDeleteComponent } from \"./produit-delete/produit-delete.component\";\nexport const PRODUIT_ROUTES = [{\n  path: '',\n  children: [{\n    path: 'list',\n    component: ProduitComponent\n  }, {\n    path: 'add',\n    component: FormProduitComponent\n  }, {\n    path: 'edit/:id',\n    component: FormProduitComponent\n  }, {\n    path: 'delete/:id',\n    component: ProduitDeleteComponent\n  }, {\n    path: '**',\n    redirectTo: 'list'\n  }]\n}];", "map": {"version": 3, "names": ["ProduitComponent", "FormProduitComponent", "ProduitDeleteComponent", "PRODUIT_ROUTES", "path", "children", "component", "redirectTo"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit.routes.ts"], "sourcesContent": ["import { Routes } from \"@angular/router\";\r\nimport { ProduitComponent } from \"./produit.component\";\r\nimport { FormProduitComponent } from \"./form-produit/form-produit.component\";\r\nimport { ProduitDeleteComponent } from \"./produit-delete/produit-delete.component\";\r\n\r\nexport const PRODUIT_ROUTES: Routes = [\r\n  { \r\n    path: '', \r\n    children: [\r\n      { path: 'list', component: ProduitComponent },\r\n      { path: 'add', component: FormProduitComponent },\r\n      { path: 'edit/:id', component: FormProduitComponent },\r\n      { path: 'delete/:id', component: ProduitDeleteComponent },\r\n      { path: '**', redirectTo: 'list' }\r\n    ]\r\n  }\r\n];"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAElF,OAAO,MAAMC,cAAc,GAAW,CACpC;EACEC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CACR;IAAED,IAAI,EAAE,MAAM;IAAEE,SAAS,EAAEN;EAAgB,CAAE,EAC7C;IAAEI,IAAI,EAAE,KAAK;IAAEE,SAAS,EAAEL;EAAoB,CAAE,EAChD;IAAEG,IAAI,EAAE,UAAU;IAAEE,SAAS,EAAEL;EAAoB,CAAE,EACrD;IAAEG,IAAI,EAAE,YAAY;IAAEE,SAAS,EAAEJ;EAAsB,CAAE,EACzD;IAAEE,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE;EAAM,CAAE;CAErC,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}