{"ast": null, "code": "import { NgApexchartsModule } from 'ng-apexcharts';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ng-apexcharts\";\nconst _c0 = [\"chart\"];\nconst _c1 = () => [\"Home\", \"Charts\"];\nexport class ApexchartComponent {\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  constructor() {\n    // Bar chart chart 1\n    this.barChartOptions = {\n      series: [{\n        name: 'Net Profit',\n        data: [44, 55, 57, 56, 61, 58, 63, 60, 66]\n      }, {\n        name: 'Revenue',\n        data: [76, 85, 101, 98, 87, 105, 91, 114, 94]\n      }, {\n        name: 'Free Cash Flow',\n        data: [35, 41, 36, 26, 45, 48, 52, 53, 41]\n      }],\n      chart: {\n        type: 'bar',\n        height: 350,\n        foreColor: '#9aa0ac'\n      },\n      plotOptions: {\n        bar: {\n          horizontal: false,\n          columnWidth: '55%',\n          borderRadius: 5\n        }\n      },\n      dataLabels: {\n        enabled: false\n      },\n      stroke: {\n        show: true,\n        width: 2,\n        colors: ['transparent']\n      },\n      xaxis: {\n        categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct'],\n        labels: {\n          style: {\n            colors: '#9aa0ac'\n          }\n        }\n      },\n      yaxis: {\n        title: {\n          text: '$ (thousands)'\n        }\n      },\n      fill: {\n        opacity: 1\n      },\n      tooltip: {\n        theme: 'dark',\n        marker: {\n          show: true\n        },\n        x: {\n          show: true\n        }\n      }\n    };\n    // Bar chart chart 2\n    this.barChart2Options = {\n      series: [{\n        name: 'Inflation',\n        data: [2.3, 3.1, 4.0, 10.1, 4.0, 3.6, 3.2, 2.3, 1.4, 0.8, 0.5, 0.2]\n      }],\n      chart: {\n        height: 350,\n        type: 'bar',\n        foreColor: '#9aa0ac'\n      },\n      plotOptions: {\n        bar: {\n          dataLabels: {\n            position: 'top' // top, center, bottom\n          }\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return val + '%';\n        },\n        offsetY: -20,\n        style: {\n          fontSize: '12px',\n          colors: ['#9aa0ac']\n        }\n      },\n      xaxis: {\n        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n        position: 'top',\n        labels: {\n          offsetY: -18,\n          style: {\n            colors: '#9aa0ac'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        },\n        crosshairs: {\n          fill: {\n            type: 'gradient',\n            gradient: {\n              colorFrom: '#D8E3F0',\n              colorTo: '#BED1E6',\n              stops: [0, 100],\n              opacityFrom: 0.4,\n              opacityTo: 0.5\n            }\n          }\n        },\n        tooltip: {\n          enabled: true,\n          offsetY: -35\n        }\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shade: 'light',\n          type: 'horizontal',\n          shadeIntensity: 0.25,\n          gradientToColors: undefined,\n          inverseColors: true,\n          opacityFrom: 1,\n          opacityTo: 1\n          //stops: [50, 0, 100, 100],\n        }\n      },\n      yaxis: {\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        },\n        labels: {\n          show: false,\n          formatter: function (val) {\n            return val + '%';\n          }\n        }\n      },\n      title: {\n        text: 'Monthly Inflation in Argentina, 2002',\n        offsetY: 320,\n        align: 'center',\n        style: {\n          color: '#9aa0ac'\n        }\n      },\n      tooltip: {\n        theme: 'dark',\n        marker: {\n          show: true\n        },\n        x: {\n          show: true\n        }\n      }\n    };\n    // line chart 1\n    this.lineChartOptions = {\n      chart: {\n        height: 350,\n        type: 'line',\n        dropShadow: {\n          enabled: true,\n          color: '#000',\n          top: 18,\n          left: 7,\n          blur: 10,\n          opacity: 1\n        },\n        toolbar: {\n          show: false\n        },\n        foreColor: '#9aa0ac'\n      },\n      colors: ['#77B6EA', '#545454'],\n      dataLabels: {\n        enabled: true\n      },\n      stroke: {\n        curve: 'smooth'\n      },\n      series: [{\n        name: 'High - 2013',\n        data: [28, 29, 33, 36, 32, 32, 33]\n      }, {\n        name: 'Low - 2013',\n        data: [12, 11, 14, 18, 17, 13, 13]\n      }],\n      title: {\n        text: 'Average High & Low Temperature',\n        align: 'left'\n      },\n      grid: {\n        borderColor: '#e7e7e7',\n        row: {\n          colors: ['#f3f3f3', 'transparent'],\n          opacity: 0.5\n        }\n      },\n      markers: {\n        size: 6\n      },\n      xaxis: {\n        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\n        title: {\n          text: 'Month'\n        },\n        labels: {\n          style: {\n            colors: '#9aa0ac'\n          }\n        }\n      },\n      yaxis: {\n        title: {\n          text: 'Temperature'\n        },\n        labels: {\n          style: {\n            colors: ['#9aa0ac']\n          }\n        },\n        min: 5,\n        max: 40\n      },\n      legend: {\n        position: 'top',\n        horizontalAlign: 'right',\n        floating: true,\n        offsetY: -25,\n        offsetX: -5\n      },\n      tooltip: {\n        theme: 'dark',\n        marker: {\n          show: true\n        },\n        x: {\n          show: true\n        }\n      }\n    };\n    // line chart 2\n    this.lineChart2Options = {\n      chart: {\n        height: 350,\n        type: 'line',\n        dropShadow: {\n          enabled: false,\n          color: '#bbb',\n          top: 3,\n          left: 2,\n          blur: 3,\n          opacity: 1\n        },\n        foreColor: '#9aa0ac'\n      },\n      stroke: {\n        width: 7,\n        curve: 'smooth'\n      },\n      series: [{\n        name: 'Likes',\n        data: [4, 3, 10, 9, 29, 19, 22, 9, 12, 7, 19, 5, 13, 9, 17, 2, 7, 5]\n      }],\n      xaxis: {\n        type: 'datetime',\n        categories: ['1/11/2000', '2/11/2000', '3/11/2000', '4/11/2000', '5/11/2000', '6/11/2000', '7/11/2000', '8/11/2000', '9/11/2000', '10/11/2000', '11/11/2000', '12/11/2000', '1/11/2001', '2/11/2001', '3/11/2001', '4/11/2001', '5/11/2001', '6/11/2001'],\n        labels: {\n          style: {\n            colors: '#9aa0ac'\n          }\n        }\n      },\n      title: {\n        text: 'Social Media',\n        align: 'left',\n        style: {\n          fontSize: '16px',\n          color: '#666'\n        }\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shade: 'dark',\n          gradientToColors: ['#FDD835'],\n          shadeIntensity: 1,\n          type: 'horizontal',\n          opacityFrom: 1,\n          opacityTo: 1\n          //stops: [0, 100, 100, 100],\n        }\n      },\n      markers: {\n        size: 4,\n        colors: ['#FFA41B'],\n        strokeWidth: 2,\n        hover: {\n          size: 7\n        }\n      },\n      yaxis: {\n        min: -10,\n        max: 40,\n        title: {\n          text: 'Engagement'\n        },\n        labels: {\n          style: {\n            colors: ['#9aa0ac']\n          }\n        }\n      }\n    };\n    // scatter chart\n    this.scatterChartOptions = {\n      chart: {\n        height: 350,\n        type: 'scatter',\n        zoom: {\n          enabled: true,\n          type: 'xy'\n        },\n        foreColor: '#9aa0ac'\n      },\n      series: [{\n        name: 'SAMPLE A',\n        data: [[16.4, 5.4], [21.7, 2], [25.4, 3], [19, 2], [10.9, 1], [13.6, 3.2], [10.9, 7.4], [10.9, 0], [10.9, 8.2], [16.4, 0], [16.4, 1.8], [13.6, 0.3], [13.6, 0], [29.9, 0], [27.1, 2.3], [16.4, 0], [13.6, 3.7], [10.9, 5.2], [16.4, 6.5], [10.9, 0], [24.5, 7.1], [10.9, 0], [8.1, 4.7], [19, 0], [21.7, 1.8], [27.1, 0], [24.5, 0], [27.1, 0], [29.9, 1.5], [27.1, 0.8], [22.1, 2]]\n      }, {\n        name: 'SAMPLE B',\n        data: [[36.4, 13.4], [1.7, 11], [5.4, 8], [9, 17], [1.9, 4], [3.6, 12.2], [1.9, 14.4], [1.9, 9], [1.9, 13.2], [1.4, 7], [6.4, 8.8], [3.6, 4.3], [1.6, 10], [9.9, 2], [7.1, 15], [1.4, 0], [3.6, 13.7], [1.9, 15.2], [6.4, 16.5], [0.9, 10], [4.5, 17.1], [10.9, 10], [0.1, 14.7], [9, 10], [12.7, 11.8], [2.1, 10], [2.5, 10], [27.1, 10], [2.9, 11.5], [7.1, 10.8], [2.1, 12]]\n      }, {\n        name: 'SAMPLE C',\n        data: [[21.7, 3], [23.6, 3.5], [24.6, 3], [29.9, 3], [21.7, 20], [23, 2], [10.9, 3], [28, 4], [27.1, 0.3], [16.4, 4], [13.6, 0], [19, 5], [22.4, 3], [24.5, 3], [32.6, 3], [27.1, 4], [29.6, 6], [31.6, 8], [21.6, 5], [20.9, 4], [22.4, 0], [32.6, 10.3], [29.7, 20.8], [24.5, 0.8], [21.4, 0], [21.7, 6.9], [28.6, 7.7], [15.4, 0], [18.1, 0], [33.4, 0], [16.4, 0]]\n      }],\n      xaxis: {\n        tickAmount: 10,\n        labels: {\n          formatter: function (val) {\n            return parseFloat(val).toFixed(1);\n          },\n          style: {\n            colors: '#9aa0ac'\n          }\n        }\n      },\n      yaxis: {\n        tickAmount: 7,\n        labels: {\n          style: {\n            colors: ['#9aa0ac']\n          }\n        }\n      }\n    };\n    // area chart\n    this.areaChartOptions = {\n      chart: {\n        height: 350,\n        type: 'area',\n        foreColor: '#9aa0ac'\n      },\n      dataLabels: {\n        enabled: false\n      },\n      stroke: {\n        curve: 'smooth'\n      },\n      series: [{\n        name: 'series1',\n        data: [31, 40, 28, 51, 42, 109, 100]\n      }, {\n        name: 'series2',\n        data: [11, 32, 45, 32, 34, 52, 41]\n      }],\n      xaxis: {\n        type: 'datetime',\n        categories: ['2018-09-19T00:00:00', '2018-09-19T01:30:00', '2018-09-19T02:30:00', '2018-09-19T03:30:00', '2018-09-19T04:30:00', '2018-09-19T05:30:00', '2018-09-19T06:30:00'],\n        labels: {\n          style: {\n            colors: '#9aa0ac'\n          }\n        }\n      },\n      yaxis: {\n        labels: {\n          style: {\n            colors: ['#9aa0ac']\n          }\n        }\n      },\n      tooltip: {\n        x: {\n          format: 'dd/MM/yy HH:mm'\n        }\n      }\n    };\n    // pie chart\n    this.pieChartOptions = {\n      chart: {\n        width: 360,\n        type: 'pie',\n        foreColor: '#9aa0ac'\n      },\n      labels: ['Team A', 'Team B', 'Team C', 'Team D', 'Team E'],\n      series2: [44, 55, 13, 43, 22],\n      responsive: [{\n        breakpoint: 480,\n        options: {\n          chart: {\n            width: 200\n          },\n          legend: {\n            position: 'bottom'\n          }\n        }\n      }]\n    };\n    // radar chart\n    this.columnChartOptions = {\n      chart: {\n        height: 350,\n        type: 'bar',\n        stacked: true,\n        toolbar: {\n          show: true\n        },\n        zoom: {\n          enabled: true\n        },\n        foreColor: '#9aa0ac'\n      },\n      responsive: [{\n        breakpoint: 480,\n        options: {\n          legend: {\n            position: 'bottom',\n            offsetX: -5,\n            offsetY: 0\n          }\n        }\n      }],\n      plotOptions: {\n        bar: {\n          horizontal: false,\n          // endingShape: 'rounded',\n          columnWidth: '180%'\n        }\n      },\n      series: [{\n        name: 'PRODUCT A',\n        data: [44, 55, 41, 67, 22, 43]\n      }, {\n        name: 'PRODUCT B',\n        data: [13, 23, 20, 8, 13, 27]\n      }, {\n        name: 'PRODUCT C',\n        data: [11, 17, 15, 15, 21, 14]\n      }, {\n        name: 'PRODUCT D',\n        data: [21, 7, 25, 13, 22, 8]\n      }],\n      xaxis: {\n        type: 'datetime',\n        categories: ['01/01/2011 GMT', '01/02/2011 GMT', '01/03/2011 GMT', '01/04/2011 GMT', '01/05/2011 GMT', '01/06/2011 GMT'],\n        labels: {\n          style: {\n            colors: '#9aa0ac'\n          }\n        }\n      },\n      yaxis: {\n        labels: {\n          style: {\n            colors: ['#9aa0ac']\n          }\n        }\n      },\n      legend: {\n        position: 'bottom',\n        offsetY: 0\n      },\n      fill: {\n        opacity: 1\n      }\n    };\n  }\n  static #_ = this.ɵfac = function ApexchartComponent_Factory(t) {\n    return new (t || ApexchartComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ApexchartComponent,\n    selectors: [[\"app-apexchart\"]],\n    viewQuery: function ApexchartComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chart = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 72,\n    vars: 44,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\", \"clearfix\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\"], [1, \"card\"], [1, \"header\"], [1, \"body\"], [2, \"text-align\", \"center\"], [3, \"series\", \"chart\", \"dataLabels\", \"plotOptions\", \"yaxis\", \"legend\", \"fill\", \"stroke\", \"tooltip\", \"xaxis\"], [3, \"series\", \"chart\", \"dataLabels\", \"plotOptions\", \"yaxis\", \"xaxis\", \"fill\", \"title\"], [3, \"series\", \"chart\", \"xaxis\", \"title\"], [3, \"series\", \"chart\", \"title\"]],\n    template: function ApexchartComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Bar chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9);\n        i0.ɵɵelement(12, \"apx-chart\", 10);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(13, \"div\", 5)(14, \"div\", 6)(15, \"div\", 7)(16, \"h2\");\n        i0.ɵɵtext(17, \"Bar Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 8)(19, \"div\", 9);\n        i0.ɵɵelement(20, \"apx-chart\", 11);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(21, \"div\", 4)(22, \"div\", 5)(23, \"div\", 6)(24, \"div\", 7)(25, \"h2\");\n        i0.ɵɵtext(26, \"Line Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 8)(28, \"div\", 9);\n        i0.ɵɵelement(29, \"apx-chart\", 12);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(30, \"div\", 5)(31, \"div\", 6)(32, \"div\", 7)(33, \"h2\");\n        i0.ɵɵtext(34, \"Line Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(35, \"div\", 8)(36, \"div\", 9);\n        i0.ɵɵelement(37, \"apx-chart\", 12);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(38, \"div\", 4)(39, \"div\", 5)(40, \"div\", 6)(41, \"div\", 7)(42, \"h2\");\n        i0.ɵɵtext(43, \"Scatter Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(44, \"div\", 8)(45, \"div\", 9);\n        i0.ɵɵelement(46, \"apx-chart\", 12);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(47, \"div\", 5)(48, \"div\", 6)(49, \"div\", 7)(50, \"h2\");\n        i0.ɵɵtext(51, \"Area Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(52, \"div\", 8)(53, \"div\", 9);\n        i0.ɵɵelement(54, \"apx-chart\", 12);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(55, \"div\", 4)(56, \"div\", 5)(57, \"div\", 6)(58, \"div\", 7)(59, \"h2\");\n        i0.ɵɵtext(60, \"Pie Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(61, \"div\", 8)(62, \"div\", 9);\n        i0.ɵɵelement(63, \"apx-chart\", 13);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(64, \"div\", 5)(65, \"div\", 6)(66, \"div\", 7)(67, \"h2\");\n        i0.ɵɵtext(68, \"Column Chart\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(69, \"div\", 8)(70, \"div\", 9);\n        i0.ɵɵelement(71, \"apx-chart\", 13);\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Apexchart\")(\"items\", i0.ɵɵpureFunction0(43, _c1))(\"active_item\", \"Apexchart\");\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"series\", ctx.barChartOptions.series)(\"chart\", ctx.barChartOptions.chart)(\"dataLabels\", ctx.barChartOptions.dataLabels)(\"plotOptions\", ctx.barChartOptions.plotOptions)(\"yaxis\", ctx.barChartOptions.yaxis)(\"legend\", ctx.barChartOptions.legend)(\"fill\", ctx.barChartOptions.fill)(\"stroke\", ctx.barChartOptions.stroke)(\"tooltip\", ctx.barChartOptions.tooltip)(\"xaxis\", ctx.barChartOptions.xaxis);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"series\", ctx.barChart2Options.series)(\"chart\", ctx.barChart2Options.chart)(\"dataLabels\", ctx.barChart2Options.dataLabels)(\"plotOptions\", ctx.barChart2Options.plotOptions)(\"yaxis\", ctx.barChart2Options.yaxis)(\"xaxis\", ctx.barChart2Options.xaxis)(\"fill\", ctx.barChart2Options.fill)(\"title\", ctx.barChart2Options.title);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"series\", ctx.lineChartOptions.series)(\"chart\", ctx.lineChartOptions.chart)(\"xaxis\", ctx.lineChartOptions.xaxis)(\"title\", ctx.lineChartOptions.title);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"series\", ctx.lineChart2Options.series)(\"chart\", ctx.lineChart2Options.chart)(\"xaxis\", ctx.lineChart2Options.xaxis)(\"title\", ctx.lineChart2Options.title);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"series\", ctx.scatterChartOptions.series)(\"chart\", ctx.scatterChartOptions.chart)(\"xaxis\", ctx.scatterChartOptions.xaxis)(\"title\", ctx.scatterChartOptions.title);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"series\", ctx.areaChartOptions.series)(\"chart\", ctx.areaChartOptions.chart)(\"xaxis\", ctx.areaChartOptions.xaxis)(\"title\", ctx.areaChartOptions.title);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"series\", ctx.pieChartOptions.series2)(\"chart\", ctx.pieChartOptions.chart)(\"title\", ctx.pieChartOptions.title);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"series\", ctx.columnChartOptions.series)(\"chart\", ctx.columnChartOptions.chart)(\"title\", ctx.columnChartOptions.title);\n      }\n    },\n    dependencies: [BreadcrumbComponent, NgApexchartsModule, i1.ChartComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["NgApexchartsModule", "BreadcrumbComponent", "ApexchartComponent", "constructor", "barChartOptions", "series", "name", "data", "chart", "type", "height", "foreColor", "plotOptions", "bar", "horizontal", "columnWidth", "borderRadius", "dataLabels", "enabled", "stroke", "show", "width", "colors", "xaxis", "categories", "labels", "style", "yaxis", "title", "text", "fill", "opacity", "tooltip", "theme", "marker", "x", "barChart2Options", "position", "formatter", "val", "offsetY", "fontSize", "axisBorder", "axisTicks", "crosshairs", "gradient", "colorFrom", "colorTo", "stops", "opacityFrom", "opacityTo", "shade", "shadeIntensity", "gradientToColors", "undefined", "inverseColors", "align", "color", "lineChartOptions", "dropShadow", "top", "left", "blur", "toolbar", "curve", "grid", "borderColor", "row", "markers", "size", "min", "max", "legend", "horizontalAlign", "floating", "offsetX", "lineChart2Options", "strokeWidth", "hover", "scatterChartOptions", "zoom", "tickAmount", "parseFloat", "toFixed", "areaChartOptions", "format", "pieChartOptions", "series2", "responsive", "breakpoint", "options", "columnChartOptions", "stacked", "_", "_2", "selectors", "viewQuery", "ApexchartComponent_Query", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "i1", "ChartComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\mian\\src\\app\\charts\\apexchart\\apexchart.component.ts", "C:\\Users\\<USER>\\Desktop\\mian\\src\\app\\charts\\apexchart\\apexchart.component.html"], "sourcesContent": ["import { Component, ViewChild } from '@angular/core';\r\nimport {\r\n  ChartComponent,\r\n  ApexAxisChartSeries,\r\n  ApexChart,\r\n  ApexXAxis,\r\n  ApexDataLabels,\r\n  ApexPlotOptions,\r\n  ApexYAxis,\r\n  ApexLegend,\r\n  ApexStroke,\r\n  ApexFill,\r\n  ApexTooltip,\r\n  ApexTitleSubtitle,\r\n  ApexGrid,\r\n  ApexMarkers,\r\n  ApexNonAxisChartSeries,\r\n  ApexResponsive,\r\n  NgApexchartsModule,\r\n} from 'ng-apexcharts';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n\r\nexport type ChartOptions = {\r\n  series?: ApexAxisChartSeries;\r\n  series2?: ApexNonAxisChartSeries;\r\n  chart?: ApexChart;\r\n  dataLabels?: ApexDataLabels;\r\n  plotOptions?: ApexPlotOptions;\r\n  yaxis?: ApexYAxis;\r\n  xaxis?: ApexXAxis;\r\n  fill?: ApexFill;\r\n  tooltip?: ApexTooltip;\r\n  stroke?: ApexStroke;\r\n  legend?: ApexLegend;\r\n  title?: ApexTitleSubtitle;\r\n  colors?: string[];\r\n  grid?: ApexGrid;\r\n  markers?: ApexMarkers;\r\n  labels: string[];\r\n  responsive: ApexResponsive[];\r\n};\r\n\r\n@Component({\r\n  selector: 'app-apexchart',\r\n  templateUrl: './apexchart.component.html',\r\n  styleUrls: ['./apexchart.component.scss'],\r\n  standalone: true,\r\n  imports: [BreadcrumbComponent, NgApexchartsModule],\r\n})\r\nexport class ApexchartComponent {\r\n  @ViewChild('chart', { static: true }) chart!: ChartComponent;\r\n\r\n  public barChartOptions: Partial<ChartOptions>;\r\n  public barChart2Options: Partial<ChartOptions>;\r\n  public lineChartOptions: Partial<ChartOptions>;\r\n  public lineChart2Options: Partial<ChartOptions>;\r\n  public scatterChartOptions: Partial<ChartOptions>;\r\n  public areaChartOptions: Partial<ChartOptions>;\r\n  public pieChartOptions: Partial<ChartOptions>;\r\n  public columnChartOptions: Partial<ChartOptions>;\r\n\r\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\r\n  constructor() {\r\n    // Bar chart chart 1\r\n    this.barChartOptions = {\r\n      series: [\r\n        {\r\n          name: 'Net Profit',\r\n          data: [44, 55, 57, 56, 61, 58, 63, 60, 66],\r\n        },\r\n        {\r\n          name: 'Revenue',\r\n          data: [76, 85, 101, 98, 87, 105, 91, 114, 94],\r\n        },\r\n        {\r\n          name: 'Free Cash Flow',\r\n          data: [35, 41, 36, 26, 45, 48, 52, 53, 41],\r\n        },\r\n      ],\r\n      chart: {\r\n        type: 'bar',\r\n        height: 350,\r\n        foreColor: '#9aa0ac',\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          horizontal: false,\r\n          columnWidth: '55%',\r\n          borderRadius: 5,\r\n        },\r\n      },\r\n      dataLabels: {\r\n        enabled: false,\r\n      },\r\n      stroke: {\r\n        show: true,\r\n        width: 2,\r\n        colors: ['transparent'],\r\n      },\r\n      xaxis: {\r\n        categories: [\r\n          'Feb',\r\n          'Mar',\r\n          'Apr',\r\n          'May',\r\n          'Jun',\r\n          'Jul',\r\n          'Aug',\r\n          'Sep',\r\n          'Oct',\r\n        ],\r\n        labels: {\r\n          style: {\r\n            colors: '#9aa0ac',\r\n          },\r\n        },\r\n      },\r\n      yaxis: {\r\n        title: {\r\n          text: '$ (thousands)',\r\n        },\r\n      },\r\n      fill: {\r\n        opacity: 1,\r\n      },\r\n      tooltip: {\r\n        theme: 'dark',\r\n        marker: {\r\n          show: true,\r\n        },\r\n        x: {\r\n          show: true,\r\n        },\r\n      },\r\n    };\r\n\r\n    // Bar chart chart 2\r\n\r\n    this.barChart2Options = {\r\n      series: [\r\n        {\r\n          name: 'Inflation',\r\n          data: [2.3, 3.1, 4.0, 10.1, 4.0, 3.6, 3.2, 2.3, 1.4, 0.8, 0.5, 0.2],\r\n        },\r\n      ],\r\n      chart: {\r\n        height: 350,\r\n        type: 'bar',\r\n        foreColor: '#9aa0ac',\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          dataLabels: {\r\n            position: 'top', // top, center, bottom\r\n          },\r\n        },\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val: number) {\r\n          return val + '%';\r\n        },\r\n        offsetY: -20,\r\n        style: {\r\n          fontSize: '12px',\r\n          colors: ['#9aa0ac'],\r\n        },\r\n      },\r\n\r\n      xaxis: {\r\n        categories: [\r\n          'Jan',\r\n          'Feb',\r\n          'Mar',\r\n          'Apr',\r\n          'May',\r\n          'Jun',\r\n          'Jul',\r\n          'Aug',\r\n          'Sep',\r\n          'Oct',\r\n          'Nov',\r\n          'Dec',\r\n        ],\r\n        position: 'top',\r\n        labels: {\r\n          offsetY: -18,\r\n          style: {\r\n            colors: '#9aa0ac',\r\n          },\r\n        },\r\n        axisBorder: {\r\n          show: false,\r\n        },\r\n        axisTicks: {\r\n          show: false,\r\n        },\r\n        crosshairs: {\r\n          fill: {\r\n            type: 'gradient',\r\n            gradient: {\r\n              colorFrom: '#D8E3F0',\r\n              colorTo: '#BED1E6',\r\n              stops: [0, 100],\r\n              opacityFrom: 0.4,\r\n              opacityTo: 0.5,\r\n            },\r\n          },\r\n        },\r\n        tooltip: {\r\n          enabled: true,\r\n          offsetY: -35,\r\n        },\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shade: 'light',\r\n          type: 'horizontal',\r\n          shadeIntensity: 0.25,\r\n          gradientToColors: undefined,\r\n          inverseColors: true,\r\n          opacityFrom: 1,\r\n          opacityTo: 1,\r\n          //stops: [50, 0, 100, 100],\r\n        },\r\n      },\r\n      yaxis: {\r\n        axisBorder: {\r\n          show: false,\r\n        },\r\n        axisTicks: {\r\n          show: false,\r\n        },\r\n        labels: {\r\n          show: false,\r\n          formatter: function (val: number) {\r\n            return val + '%';\r\n          },\r\n        },\r\n      },\r\n      title: {\r\n        text: 'Monthly Inflation in Argentina, 2002',\r\n        offsetY: 320,\r\n        align: 'center',\r\n        style: {\r\n          color: '#9aa0ac',\r\n        },\r\n      },\r\n      tooltip: {\r\n        theme: 'dark',\r\n        marker: {\r\n          show: true,\r\n        },\r\n        x: {\r\n          show: true,\r\n        },\r\n      },\r\n    };\r\n\r\n    // line chart 1\r\n\r\n    this.lineChartOptions = {\r\n      chart: {\r\n        height: 350,\r\n        type: 'line',\r\n        dropShadow: {\r\n          enabled: true,\r\n          color: '#000',\r\n          top: 18,\r\n          left: 7,\r\n          blur: 10,\r\n          opacity: 1,\r\n        },\r\n        toolbar: {\r\n          show: false,\r\n        },\r\n        foreColor: '#9aa0ac',\r\n      },\r\n      colors: ['#77B6EA', '#545454'],\r\n      dataLabels: {\r\n        enabled: true,\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n      },\r\n      series: [\r\n        {\r\n          name: 'High - 2013',\r\n          data: [28, 29, 33, 36, 32, 32, 33],\r\n        },\r\n        {\r\n          name: 'Low - 2013',\r\n          data: [12, 11, 14, 18, 17, 13, 13],\r\n        },\r\n      ],\r\n      title: {\r\n        text: 'Average High & Low Temperature',\r\n        align: 'left',\r\n      },\r\n      grid: {\r\n        borderColor: '#e7e7e7',\r\n        row: {\r\n          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns\r\n          opacity: 0.5,\r\n        },\r\n      },\r\n      markers: {\r\n        size: 6,\r\n      },\r\n      xaxis: {\r\n        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n        title: {\r\n          text: 'Month',\r\n        },\r\n        labels: {\r\n          style: {\r\n            colors: '#9aa0ac',\r\n          },\r\n        },\r\n      },\r\n      yaxis: {\r\n        title: {\r\n          text: 'Temperature',\r\n        },\r\n        labels: {\r\n          style: {\r\n            colors: ['#9aa0ac'],\r\n          },\r\n        },\r\n        min: 5,\r\n        max: 40,\r\n      },\r\n      legend: {\r\n        position: 'top',\r\n        horizontalAlign: 'right',\r\n        floating: true,\r\n        offsetY: -25,\r\n        offsetX: -5,\r\n      },\r\n      tooltip: {\r\n        theme: 'dark',\r\n        marker: {\r\n          show: true,\r\n        },\r\n        x: {\r\n          show: true,\r\n        },\r\n      },\r\n    };\r\n\r\n    // line chart 2\r\n\r\n    this.lineChart2Options = {\r\n      chart: {\r\n        height: 350,\r\n        type: 'line',\r\n        dropShadow: {\r\n          enabled: false,\r\n          color: '#bbb',\r\n          top: 3,\r\n          left: 2,\r\n          blur: 3,\r\n          opacity: 1,\r\n        },\r\n        foreColor: '#9aa0ac',\r\n      },\r\n      stroke: {\r\n        width: 7,\r\n        curve: 'smooth',\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Likes',\r\n          data: [4, 3, 10, 9, 29, 19, 22, 9, 12, 7, 19, 5, 13, 9, 17, 2, 7, 5],\r\n        },\r\n      ],\r\n      xaxis: {\r\n        type: 'datetime',\r\n        categories: [\r\n          '1/11/2000',\r\n          '2/11/2000',\r\n          '3/11/2000',\r\n          '4/11/2000',\r\n          '5/11/2000',\r\n          '6/11/2000',\r\n          '7/11/2000',\r\n          '8/11/2000',\r\n          '9/11/2000',\r\n          '10/11/2000',\r\n          '11/11/2000',\r\n          '12/11/2000',\r\n          '1/11/2001',\r\n          '2/11/2001',\r\n          '3/11/2001',\r\n          '4/11/2001',\r\n          '5/11/2001',\r\n          '6/11/2001',\r\n        ],\r\n        labels: {\r\n          style: {\r\n            colors: '#9aa0ac',\r\n          },\r\n        },\r\n      },\r\n      title: {\r\n        text: 'Social Media',\r\n        align: 'left',\r\n        style: {\r\n          fontSize: '16px',\r\n          color: '#666',\r\n        },\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shade: 'dark',\r\n          gradientToColors: ['#FDD835'],\r\n          shadeIntensity: 1,\r\n          type: 'horizontal',\r\n          opacityFrom: 1,\r\n          opacityTo: 1,\r\n          //stops: [0, 100, 100, 100],\r\n        },\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: ['#FFA41B'],\r\n        strokeWidth: 2,\r\n\r\n        hover: {\r\n          size: 7,\r\n        },\r\n      },\r\n      yaxis: {\r\n        min: -10,\r\n        max: 40,\r\n        title: {\r\n          text: 'Engagement',\r\n        },\r\n        labels: {\r\n          style: {\r\n            colors: ['#9aa0ac'],\r\n          },\r\n        },\r\n      },\r\n    };\r\n\r\n    // scatter chart\r\n\r\n    this.scatterChartOptions = {\r\n      chart: {\r\n        height: 350,\r\n        type: 'scatter',\r\n        zoom: {\r\n          enabled: true,\r\n          type: 'xy',\r\n        },\r\n        foreColor: '#9aa0ac',\r\n      },\r\n\r\n      series: [\r\n        {\r\n          name: 'SAMPLE A',\r\n          data: [\r\n            [16.4, 5.4],\r\n            [21.7, 2],\r\n            [25.4, 3],\r\n            [19, 2],\r\n            [10.9, 1],\r\n            [13.6, 3.2],\r\n            [10.9, 7.4],\r\n            [10.9, 0],\r\n            [10.9, 8.2],\r\n            [16.4, 0],\r\n            [16.4, 1.8],\r\n            [13.6, 0.3],\r\n            [13.6, 0],\r\n            [29.9, 0],\r\n            [27.1, 2.3],\r\n            [16.4, 0],\r\n            [13.6, 3.7],\r\n            [10.9, 5.2],\r\n            [16.4, 6.5],\r\n            [10.9, 0],\r\n            [24.5, 7.1],\r\n            [10.9, 0],\r\n            [8.1, 4.7],\r\n            [19, 0],\r\n            [21.7, 1.8],\r\n            [27.1, 0],\r\n            [24.5, 0],\r\n            [27.1, 0],\r\n            [29.9, 1.5],\r\n            [27.1, 0.8],\r\n            [22.1, 2],\r\n          ],\r\n        },\r\n        {\r\n          name: 'SAMPLE B',\r\n          data: [\r\n            [36.4, 13.4],\r\n            [1.7, 11],\r\n            [5.4, 8],\r\n            [9, 17],\r\n            [1.9, 4],\r\n            [3.6, 12.2],\r\n            [1.9, 14.4],\r\n            [1.9, 9],\r\n            [1.9, 13.2],\r\n            [1.4, 7],\r\n            [6.4, 8.8],\r\n            [3.6, 4.3],\r\n            [1.6, 10],\r\n            [9.9, 2],\r\n            [7.1, 15],\r\n            [1.4, 0],\r\n            [3.6, 13.7],\r\n            [1.9, 15.2],\r\n            [6.4, 16.5],\r\n            [0.9, 10],\r\n            [4.5, 17.1],\r\n            [10.9, 10],\r\n            [0.1, 14.7],\r\n            [9, 10],\r\n            [12.7, 11.8],\r\n            [2.1, 10],\r\n            [2.5, 10],\r\n            [27.1, 10],\r\n            [2.9, 11.5],\r\n            [7.1, 10.8],\r\n            [2.1, 12],\r\n          ],\r\n        },\r\n        {\r\n          name: 'SAMPLE C',\r\n          data: [\r\n            [21.7, 3],\r\n            [23.6, 3.5],\r\n            [24.6, 3],\r\n            [29.9, 3],\r\n            [21.7, 20],\r\n            [23, 2],\r\n            [10.9, 3],\r\n            [28, 4],\r\n            [27.1, 0.3],\r\n            [16.4, 4],\r\n            [13.6, 0],\r\n            [19, 5],\r\n            [22.4, 3],\r\n            [24.5, 3],\r\n            [32.6, 3],\r\n            [27.1, 4],\r\n            [29.6, 6],\r\n            [31.6, 8],\r\n            [21.6, 5],\r\n            [20.9, 4],\r\n            [22.4, 0],\r\n            [32.6, 10.3],\r\n            [29.7, 20.8],\r\n            [24.5, 0.8],\r\n            [21.4, 0],\r\n            [21.7, 6.9],\r\n            [28.6, 7.7],\r\n            [15.4, 0],\r\n            [18.1, 0],\r\n            [33.4, 0],\r\n            [16.4, 0],\r\n          ],\r\n        },\r\n      ],\r\n      xaxis: {\r\n        tickAmount: 10,\r\n        labels: {\r\n          formatter: function (val: string) {\r\n            return parseFloat(val).toFixed(1);\r\n          },\r\n          style: {\r\n            colors: '#9aa0ac',\r\n          },\r\n        },\r\n      },\r\n      yaxis: {\r\n        tickAmount: 7,\r\n        labels: {\r\n          style: {\r\n            colors: ['#9aa0ac'],\r\n          },\r\n        },\r\n      },\r\n    };\r\n\r\n    // area chart\r\n    this.areaChartOptions = {\r\n      chart: {\r\n        height: 350,\r\n        type: 'area',\r\n        foreColor: '#9aa0ac',\r\n      },\r\n      dataLabels: {\r\n        enabled: false,\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n      },\r\n      series: [\r\n        {\r\n          name: 'series1',\r\n          data: [31, 40, 28, 51, 42, 109, 100],\r\n        },\r\n        {\r\n          name: 'series2',\r\n          data: [11, 32, 45, 32, 34, 52, 41],\r\n        },\r\n      ],\r\n\r\n      xaxis: {\r\n        type: 'datetime',\r\n        categories: [\r\n          '2018-09-19T00:00:00',\r\n          '2018-09-19T01:30:00',\r\n          '2018-09-19T02:30:00',\r\n          '2018-09-19T03:30:00',\r\n          '2018-09-19T04:30:00',\r\n          '2018-09-19T05:30:00',\r\n          '2018-09-19T06:30:00',\r\n        ],\r\n        labels: {\r\n          style: {\r\n            colors: '#9aa0ac',\r\n          },\r\n        },\r\n      },\r\n      yaxis: {\r\n        labels: {\r\n          style: {\r\n            colors: ['#9aa0ac'],\r\n          },\r\n        },\r\n      },\r\n      tooltip: {\r\n        x: {\r\n          format: 'dd/MM/yy HH:mm',\r\n        },\r\n      },\r\n    };\r\n\r\n    // pie chart\r\n    this.pieChartOptions = {\r\n      chart: {\r\n        width: 360,\r\n        type: 'pie',\r\n        foreColor: '#9aa0ac',\r\n      },\r\n      labels: ['Team A', 'Team B', 'Team C', 'Team D', 'Team E'],\r\n      series2: [44, 55, 13, 43, 22],\r\n      responsive: [\r\n        {\r\n          breakpoint: 480,\r\n          options: {\r\n            chart: {\r\n              width: 200,\r\n            },\r\n            legend: {\r\n              position: 'bottom',\r\n            },\r\n          },\r\n        },\r\n      ],\r\n    };\r\n    // radar chart\r\n\r\n    this.columnChartOptions = {\r\n      chart: {\r\n        height: 350,\r\n        type: 'bar',\r\n        stacked: true,\r\n        toolbar: {\r\n          show: true,\r\n        },\r\n        zoom: {\r\n          enabled: true,\r\n        },\r\n        foreColor: '#9aa0ac',\r\n      },\r\n      responsive: [\r\n        {\r\n          breakpoint: 480,\r\n          options: {\r\n            legend: {\r\n              position: 'bottom',\r\n              offsetX: -5,\r\n              offsetY: 0,\r\n            },\r\n          },\r\n        },\r\n      ],\r\n      plotOptions: {\r\n        bar: {\r\n          horizontal: false,\r\n          // endingShape: 'rounded',\r\n          columnWidth: '180%',\r\n        },\r\n      },\r\n      series: [\r\n        {\r\n          name: 'PRODUCT A',\r\n          data: [44, 55, 41, 67, 22, 43],\r\n        },\r\n        {\r\n          name: 'PRODUCT B',\r\n          data: [13, 23, 20, 8, 13, 27],\r\n        },\r\n        {\r\n          name: 'PRODUCT C',\r\n          data: [11, 17, 15, 15, 21, 14],\r\n        },\r\n        {\r\n          name: 'PRODUCT D',\r\n          data: [21, 7, 25, 13, 22, 8],\r\n        },\r\n      ],\r\n      xaxis: {\r\n        type: 'datetime',\r\n        categories: [\r\n          '01/01/2011 GMT',\r\n          '01/02/2011 GMT',\r\n          '01/03/2011 GMT',\r\n          '01/04/2011 GMT',\r\n          '01/05/2011 GMT',\r\n          '01/06/2011 GMT',\r\n        ],\r\n        labels: {\r\n          style: {\r\n            colors: '#9aa0ac',\r\n          },\r\n        },\r\n      },\r\n      yaxis: {\r\n        labels: {\r\n          style: {\r\n            colors: ['#9aa0ac'],\r\n          },\r\n        },\r\n      },\r\n      legend: {\r\n        position: 'bottom',\r\n        offsetY: 0,\r\n      },\r\n      fill: {\r\n        opacity: 1,\r\n      },\r\n    };\r\n  }\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Apexchart'\" [items]=\"['Home','Charts']\" [active_item]=\"'Apexchart'\">\r\n      </app-breadcrumb>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Bar chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div style=\"text-align:center\">\r\n              <apx-chart [series]=\"barChartOptions.series!\" [chart]=\"barChartOptions.chart!\"\r\n                [dataLabels]=\"barChartOptions.dataLabels!\" [plotOptions]=\"barChartOptions.plotOptions!\"\r\n                [yaxis]=\"barChartOptions.yaxis!\" [legend]=\"barChartOptions.legend!\" [fill]=\"barChartOptions.fill!\"\r\n                [stroke]=\"barChartOptions.stroke!\" [tooltip]=\"barChartOptions.tooltip!\"\r\n                [xaxis]=\"barChartOptions.xaxis!\">\r\n              </apx-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Bar Chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div style=\"text-align:center\">\r\n              <apx-chart [series]=\"barChart2Options.series!\" [chart]=\"barChart2Options.chart!\"\r\n                [dataLabels]=\"barChart2Options.dataLabels!\" [plotOptions]=\"barChart2Options.plotOptions!\"\r\n                [yaxis]=\"barChart2Options.yaxis!\" [xaxis]=\"barChart2Options.xaxis!\" [fill]=\"barChart2Options.fill!\"\r\n                [title]=\"barChart2Options.title!\"></apx-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Line Chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div style=\"text-align:center\">\r\n              <apx-chart [series]=\"lineChartOptions.series!\" [chart]=\"lineChartOptions.chart!\"\r\n                [xaxis]=\"lineChartOptions.xaxis!\" [title]=\"lineChartOptions.title!\"></apx-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Line Chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div style=\"text-align:center\">\r\n              <apx-chart [series]=\"lineChart2Options.series!\" [chart]=\"lineChart2Options.chart!\"\r\n                [xaxis]=\"lineChart2Options.xaxis!\" [title]=\"lineChart2Options.title!\"></apx-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Scatter Chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div style=\"text-align:center\">\r\n              <apx-chart [series]=\"scatterChartOptions.series!\" [chart]=\"scatterChartOptions.chart!\"\r\n                [xaxis]=\"scatterChartOptions.xaxis!\" [title]=\"scatterChartOptions.title!\"></apx-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Area Chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div style=\"text-align:center\">\r\n              <apx-chart [series]=\"areaChartOptions.series!\" [chart]=\"areaChartOptions.chart!\"\r\n                [xaxis]=\"areaChartOptions.xaxis!\" [title]=\"areaChartOptions.title!\"></apx-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Pie Chart</h2>\r\n\r\n          </div>\r\n          <div class=\"body\">\r\n            <div style=\"text-align:center\">\r\n              <apx-chart [series]=\"pieChartOptions.series2!\" [chart]=\"pieChartOptions.chart!\"\r\n                [title]=\"pieChartOptions.title!\"></apx-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Column Chart</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div style=\"text-align:center\">\r\n              <apx-chart [series]=\"columnChartOptions.series!\" [chart]=\"columnChartOptions.chart!\"\r\n                [title]=\"columnChartOptions.title!\"></apx-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n"], "mappings": "AACA,SAiBEA,kBAAkB,QACb,eAAe;AACtB,SAASC,mBAAmB,QAAQ,oDAAoD;;;;;AA6BxF,OAAM,MAAOC,kBAAkB;EAY7B;EACAC,YAAA;IACE;IACA,IAAI,CAACC,eAAe,GAAG;MACrBC,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAC1C,EACD;QACED,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE;OAC7C,EACD;QACED,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAC1C,CACF;MACDC,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,GAAG;QACXC,SAAS,EAAE;OACZ;MACDC,WAAW,EAAE;QACXC,GAAG,EAAE;UACHC,UAAU,EAAE,KAAK;UACjBC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE;;OAEjB;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE;OACV;MACDC,MAAM,EAAE;QACNC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC,aAAa;OACvB;MACDC,KAAK,EAAE;QACLC,UAAU,EAAE,CACV,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;QACDC,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE;;;OAGb;MACDK,KAAK,EAAE;QACLC,KAAK,EAAE;UACLC,IAAI,EAAE;;OAET;MACDC,IAAI,EAAE;QACJC,OAAO,EAAE;OACV;MACDC,OAAO,EAAE;QACPC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;UACNd,IAAI,EAAE;SACP;QACDe,CAAC,EAAE;UACDf,IAAI,EAAE;;;KAGX;IAED;IAEA,IAAI,CAACgB,gBAAgB,GAAG;MACtB/B,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;OACnE,CACF;MACDC,KAAK,EAAE;QACLE,MAAM,EAAE,GAAG;QACXD,IAAI,EAAE,KAAK;QACXE,SAAS,EAAE;OACZ;MACDC,WAAW,EAAE;QACXC,GAAG,EAAE;UACHI,UAAU,EAAE;YACVoB,QAAQ,EAAE,KAAK,CAAE;;;OAGtB;MACDpB,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACboB,SAAS,EAAE,SAAAA,CAAUC,GAAW;UAC9B,OAAOA,GAAG,GAAG,GAAG;QAClB,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,KAAK,EAAE;UACLe,QAAQ,EAAE,MAAM;UAChBnB,MAAM,EAAE,CAAC,SAAS;;OAErB;MAEDC,KAAK,EAAE;QACLC,UAAU,EAAE,CACV,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;QACDa,QAAQ,EAAE,KAAK;QACfZ,MAAM,EAAE;UACNe,OAAO,EAAE,CAAC,EAAE;UACZd,KAAK,EAAE;YACLJ,MAAM,EAAE;;SAEX;QACDoB,UAAU,EAAE;UACVtB,IAAI,EAAE;SACP;QACDuB,SAAS,EAAE;UACTvB,IAAI,EAAE;SACP;QACDwB,UAAU,EAAE;UACVd,IAAI,EAAE;YACJrB,IAAI,EAAE,UAAU;YAChBoC,QAAQ,EAAE;cACRC,SAAS,EAAE,SAAS;cACpBC,OAAO,EAAE,SAAS;cAClBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;cACfC,WAAW,EAAE,GAAG;cAChBC,SAAS,EAAE;;;SAGhB;QACDlB,OAAO,EAAE;UACPd,OAAO,EAAE,IAAI;UACbsB,OAAO,EAAE,CAAC;;OAEb;MACDV,IAAI,EAAE;QACJrB,IAAI,EAAE,UAAU;QAChBoC,QAAQ,EAAE;UACRM,KAAK,EAAE,OAAO;UACd1C,IAAI,EAAE,YAAY;UAClB2C,cAAc,EAAE,IAAI;UACpBC,gBAAgB,EAAEC,SAAS;UAC3BC,aAAa,EAAE,IAAI;UACnBN,WAAW,EAAE,CAAC;UACdC,SAAS,EAAE;UACX;;OAEH;MACDvB,KAAK,EAAE;QACLe,UAAU,EAAE;UACVtB,IAAI,EAAE;SACP;QACDuB,SAAS,EAAE;UACTvB,IAAI,EAAE;SACP;QACDK,MAAM,EAAE;UACNL,IAAI,EAAE,KAAK;UACXkB,SAAS,EAAE,SAAAA,CAAUC,GAAW;YAC9B,OAAOA,GAAG,GAAG,GAAG;UAClB;;OAEH;MACDX,KAAK,EAAE;QACLC,IAAI,EAAE,sCAAsC;QAC5CW,OAAO,EAAE,GAAG;QACZgB,KAAK,EAAE,QAAQ;QACf9B,KAAK,EAAE;UACL+B,KAAK,EAAE;;OAEV;MACDzB,OAAO,EAAE;QACPC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;UACNd,IAAI,EAAE;SACP;QACDe,CAAC,EAAE;UACDf,IAAI,EAAE;;;KAGX;IAED;IAEA,IAAI,CAACsC,gBAAgB,GAAG;MACtBlD,KAAK,EAAE;QACLE,MAAM,EAAE,GAAG;QACXD,IAAI,EAAE,MAAM;QACZkD,UAAU,EAAE;UACVzC,OAAO,EAAE,IAAI;UACbuC,KAAK,EAAE,MAAM;UACbG,GAAG,EAAE,EAAE;UACPC,IAAI,EAAE,CAAC;UACPC,IAAI,EAAE,EAAE;UACR/B,OAAO,EAAE;SACV;QACDgC,OAAO,EAAE;UACP3C,IAAI,EAAE;SACP;QACDT,SAAS,EAAE;OACZ;MACDW,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAC9BL,UAAU,EAAE;QACVC,OAAO,EAAE;OACV;MACDC,MAAM,EAAE;QACN6C,KAAK,EAAE;OACR;MACD3D,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAClC,EACD;QACED,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAClC,CACF;MACDqB,KAAK,EAAE;QACLC,IAAI,EAAE,gCAAgC;QACtC2B,KAAK,EAAE;OACR;MACDS,IAAI,EAAE;QACJC,WAAW,EAAE,SAAS;QACtBC,GAAG,EAAE;UACH7C,MAAM,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;UAClCS,OAAO,EAAE;;OAEZ;MACDqC,OAAO,EAAE;QACPC,IAAI,EAAE;OACP;MACD9C,KAAK,EAAE;QACLC,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QAC7DI,KAAK,EAAE;UACLC,IAAI,EAAE;SACP;QACDJ,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE;;;OAGb;MACDK,KAAK,EAAE;QACLC,KAAK,EAAE;UACLC,IAAI,EAAE;SACP;QACDJ,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE,CAAC,SAAS;;SAErB;QACDgD,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE;OACN;MACDC,MAAM,EAAE;QACNnC,QAAQ,EAAE,KAAK;QACfoC,eAAe,EAAE,OAAO;QACxBC,QAAQ,EAAE,IAAI;QACdlC,OAAO,EAAE,CAAC,EAAE;QACZmC,OAAO,EAAE,CAAC;OACX;MACD3C,OAAO,EAAE;QACPC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;UACNd,IAAI,EAAE;SACP;QACDe,CAAC,EAAE;UACDf,IAAI,EAAE;;;KAGX;IAED;IAEA,IAAI,CAACwD,iBAAiB,GAAG;MACvBpE,KAAK,EAAE;QACLE,MAAM,EAAE,GAAG;QACXD,IAAI,EAAE,MAAM;QACZkD,UAAU,EAAE;UACVzC,OAAO,EAAE,KAAK;UACduC,KAAK,EAAE,MAAM;UACbG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,IAAI,EAAE,CAAC;UACP/B,OAAO,EAAE;SACV;QACDpB,SAAS,EAAE;OACZ;MACDQ,MAAM,EAAE;QACNE,KAAK,EAAE,CAAC;QACR2C,KAAK,EAAE;OACR;MACD3D,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;OACpE,CACF;MACDgB,KAAK,EAAE;QACLd,IAAI,EAAE,UAAU;QAChBe,UAAU,EAAE,CACV,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACZ;QACDC,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE;;;OAGb;MACDM,KAAK,EAAE;QACLC,IAAI,EAAE,cAAc;QACpB2B,KAAK,EAAE,MAAM;QACb9B,KAAK,EAAE;UACLe,QAAQ,EAAE,MAAM;UAChBgB,KAAK,EAAE;;OAEV;MACD3B,IAAI,EAAE;QACJrB,IAAI,EAAE,UAAU;QAChBoC,QAAQ,EAAE;UACRM,KAAK,EAAE,MAAM;UACbE,gBAAgB,EAAE,CAAC,SAAS,CAAC;UAC7BD,cAAc,EAAE,CAAC;UACjB3C,IAAI,EAAE,YAAY;UAClBwC,WAAW,EAAE,CAAC;UACdC,SAAS,EAAE;UACX;;OAEH;MACDkB,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACP/C,MAAM,EAAE,CAAC,SAAS,CAAC;QACnBuD,WAAW,EAAE,CAAC;QAEdC,KAAK,EAAE;UACLT,IAAI,EAAE;;OAET;MACD1C,KAAK,EAAE;QACL2C,GAAG,EAAE,CAAC,EAAE;QACRC,GAAG,EAAE,EAAE;QACP3C,KAAK,EAAE;UACLC,IAAI,EAAE;SACP;QACDJ,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE,CAAC,SAAS;;;;KAIzB;IAED;IAEA,IAAI,CAACyD,mBAAmB,GAAG;MACzBvE,KAAK,EAAE;QACLE,MAAM,EAAE,GAAG;QACXD,IAAI,EAAE,SAAS;QACfuE,IAAI,EAAE;UACJ9D,OAAO,EAAE,IAAI;UACbT,IAAI,EAAE;SACP;QACDE,SAAS,EAAE;OACZ;MAEDN,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE,CACJ,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,GAAG,EAAE,GAAG,CAAC,EACV,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC;OAEZ,EACD;QACED,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE,CACJ,CAAC,IAAI,EAAE,IAAI,CAAC,EACZ,CAAC,GAAG,EAAE,EAAE,CAAC,EACT,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,CAAC,EAAE,EAAE,CAAC,EACP,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,GAAG,CAAC,EACV,CAAC,GAAG,EAAE,GAAG,CAAC,EACV,CAAC,GAAG,EAAE,EAAE,CAAC,EACT,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,EAAE,CAAC,EACT,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,EAAE,CAAC,EACT,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,IAAI,EAAE,EAAE,CAAC,EACV,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,CAAC,EAAE,EAAE,CAAC,EACP,CAAC,IAAI,EAAE,IAAI,CAAC,EACZ,CAAC,GAAG,EAAE,EAAE,CAAC,EACT,CAAC,GAAG,EAAE,EAAE,CAAC,EACT,CAAC,IAAI,EAAE,EAAE,CAAC,EACV,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,IAAI,CAAC,EACX,CAAC,GAAG,EAAE,EAAE,CAAC;OAEZ,EACD;QACED,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE,CACJ,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,EAAE,CAAC,EACV,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,IAAI,CAAC,EACZ,CAAC,IAAI,EAAE,IAAI,CAAC,EACZ,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,GAAG,CAAC,EACX,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC,EACT,CAAC,IAAI,EAAE,CAAC,CAAC;OAEZ,CACF;MACDgB,KAAK,EAAE;QACL0D,UAAU,EAAE,EAAE;QACdxD,MAAM,EAAE;UACNa,SAAS,EAAE,SAAAA,CAAUC,GAAW;YAC9B,OAAO2C,UAAU,CAAC3C,GAAG,CAAC,CAAC4C,OAAO,CAAC,CAAC,CAAC;UACnC,CAAC;UACDzD,KAAK,EAAE;YACLJ,MAAM,EAAE;;;OAGb;MACDK,KAAK,EAAE;QACLsD,UAAU,EAAE,CAAC;QACbxD,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE,CAAC,SAAS;;;;KAIzB;IAED;IACA,IAAI,CAAC8D,gBAAgB,GAAG;MACtB5E,KAAK,EAAE;QACLE,MAAM,EAAE,GAAG;QACXD,IAAI,EAAE,MAAM;QACZE,SAAS,EAAE;OACZ;MACDM,UAAU,EAAE;QACVC,OAAO,EAAE;OACV;MACDC,MAAM,EAAE;QACN6C,KAAK,EAAE;OACR;MACD3D,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;OACpC,EACD;QACED,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAClC,CACF;MAEDgB,KAAK,EAAE;QACLd,IAAI,EAAE,UAAU;QAChBe,UAAU,EAAE,CACV,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,CACtB;QACDC,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE;;;OAGb;MACDK,KAAK,EAAE;QACLF,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE,CAAC,SAAS;;;OAGvB;MACDU,OAAO,EAAE;QACPG,CAAC,EAAE;UACDkD,MAAM,EAAE;;;KAGb;IAED;IACA,IAAI,CAACC,eAAe,GAAG;MACrB9E,KAAK,EAAE;QACLa,KAAK,EAAE,GAAG;QACVZ,IAAI,EAAE,KAAK;QACXE,SAAS,EAAE;OACZ;MACDc,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MAC1D8D,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC7BC,UAAU,EAAE,CACV;QACEC,UAAU,EAAE,GAAG;QACfC,OAAO,EAAE;UACPlF,KAAK,EAAE;YACLa,KAAK,EAAE;WACR;UACDmD,MAAM,EAAE;YACNnC,QAAQ,EAAE;;;OAGf;KAEJ;IACD;IAEA,IAAI,CAACsD,kBAAkB,GAAG;MACxBnF,KAAK,EAAE;QACLE,MAAM,EAAE,GAAG;QACXD,IAAI,EAAE,KAAK;QACXmF,OAAO,EAAE,IAAI;QACb7B,OAAO,EAAE;UACP3C,IAAI,EAAE;SACP;QACD4D,IAAI,EAAE;UACJ9D,OAAO,EAAE;SACV;QACDP,SAAS,EAAE;OACZ;MACD6E,UAAU,EAAE,CACV;QACEC,UAAU,EAAE,GAAG;QACfC,OAAO,EAAE;UACPlB,MAAM,EAAE;YACNnC,QAAQ,EAAE,QAAQ;YAClBsC,OAAO,EAAE,CAAC,CAAC;YACXnC,OAAO,EAAE;;;OAGd,CACF;MACD5B,WAAW,EAAE;QACXC,GAAG,EAAE;UACHC,UAAU,EAAE,KAAK;UACjB;UACAC,WAAW,EAAE;;OAEhB;MACDV,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAC9B,EACD;QACED,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;OAC7B,EACD;QACED,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAC9B,EACD;QACED,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;OAC5B,CACF;MACDgB,KAAK,EAAE;QACLd,IAAI,EAAE,UAAU;QAChBe,UAAU,EAAE,CACV,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,CACjB;QACDC,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE;;;OAGb;MACDK,KAAK,EAAE;QACLF,MAAM,EAAE;UACNC,KAAK,EAAE;YACLJ,MAAM,EAAE,CAAC,SAAS;;;OAGvB;MACDkD,MAAM,EAAE;QACNnC,QAAQ,EAAE,QAAQ;QAClBG,OAAO,EAAE;OACV;MACDV,IAAI,EAAE;QACJC,OAAO,EAAE;;KAEZ;EACH;EAAC,QAAA8D,CAAA,G;qBAhsBU3F,kBAAkB;EAAA;EAAA,QAAA4F,EAAA,G;UAAlB5F,kBAAkB;IAAA6F,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCjD/BE,EAAA,CAAAC,cAAA,iBAAyB;QAInBD,EAAA,CAAAE,SAAA,wBACiB;QACnBF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAA0B;QAIdD,EAAA,CAAAI,MAAA,gBAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAEpBH,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,SAAA,qBAKY;QACdF,EAAA,CAAAG,YAAA,EAAM;QAIZH,EAAA,CAAAC,cAAA,cAAmD;QAGzCD,EAAA,CAAAI,MAAA,iBAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAEpBH,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,SAAA,qBAGgD;QAClDF,EAAA,CAAAG,YAAA,EAAM;QAKdH,EAAA,CAAAC,cAAA,cAA0B;QAIdD,EAAA,CAAAI,MAAA,kBAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAErBH,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,SAAA,qBACkF;QACpFF,EAAA,CAAAG,YAAA,EAAM;QAIZH,EAAA,CAAAC,cAAA,cAAmD;QAGzCD,EAAA,CAAAI,MAAA,kBAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAErBH,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,SAAA,qBACoF;QACtFF,EAAA,CAAAG,YAAA,EAAM;QAKdH,EAAA,CAAAC,cAAA,cAA0B;QAIdD,EAAA,CAAAI,MAAA,qBAAa;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAExBH,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,SAAA,qBACwF;QAC1FF,EAAA,CAAAG,YAAA,EAAM;QAIZH,EAAA,CAAAC,cAAA,cAAmD;QAGzCD,EAAA,CAAAI,MAAA,kBAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAErBH,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,SAAA,qBACkF;QACpFF,EAAA,CAAAG,YAAA,EAAM;QAKdH,EAAA,CAAAC,cAAA,cAA0B;QAIdD,EAAA,CAAAI,MAAA,iBAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAGpBH,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,SAAA,qBAC+C;QACjDF,EAAA,CAAAG,YAAA,EAAM;QAIZH,EAAA,CAAAC,cAAA,cAAmD;QAGzCD,EAAA,CAAAI,MAAA,oBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAEvBH,EAAA,CAAAC,cAAA,cAAkB;QAEdD,EAAA,CAAAE,SAAA,qBACkD;QACpDF,EAAA,CAAAG,YAAA,EAAM;;;QArHIH,EAAA,CAAAK,SAAA,GAAqB;QAArBL,EAAA,CAAAM,UAAA,sBAAqB,UAAAN,EAAA,CAAAO,eAAA,KAAAC,GAAA;QAWlBR,EAAA,CAAAK,SAAA,GAAkC;QAAlCL,EAAA,CAAAM,UAAA,WAAAP,GAAA,CAAA/F,eAAA,CAAAC,MAAA,CAAkC,UAAA8F,GAAA,CAAA/F,eAAA,CAAAI,KAAA,gBAAA2F,GAAA,CAAA/F,eAAA,CAAAa,UAAA,iBAAAkF,GAAA,CAAA/F,eAAA,CAAAQ,WAAA,WAAAuF,GAAA,CAAA/F,eAAA,CAAAuB,KAAA,YAAAwE,GAAA,CAAA/F,eAAA,CAAAoE,MAAA,UAAA2B,GAAA,CAAA/F,eAAA,CAAA0B,IAAA,YAAAqE,GAAA,CAAA/F,eAAA,CAAAe,MAAA,aAAAgF,GAAA,CAAA/F,eAAA,CAAA4B,OAAA,WAAAmE,GAAA,CAAA/F,eAAA,CAAAmB,KAAA;QAiBlC6E,EAAA,CAAAK,SAAA,GAAmC;QAAnCL,EAAA,CAAAM,UAAA,WAAAP,GAAA,CAAA/D,gBAAA,CAAA/B,MAAA,CAAmC,UAAA8F,GAAA,CAAA/D,gBAAA,CAAA5B,KAAA,gBAAA2F,GAAA,CAAA/D,gBAAA,CAAAnB,UAAA,iBAAAkF,GAAA,CAAA/D,gBAAA,CAAAxB,WAAA,WAAAuF,GAAA,CAAA/D,gBAAA,CAAAT,KAAA,WAAAwE,GAAA,CAAA/D,gBAAA,CAAAb,KAAA,UAAA4E,GAAA,CAAA/D,gBAAA,CAAAN,IAAA,WAAAqE,GAAA,CAAA/D,gBAAA,CAAAR,KAAA;QAiBnCwE,EAAA,CAAAK,SAAA,GAAmC;QAAnCL,EAAA,CAAAM,UAAA,WAAAP,GAAA,CAAAzC,gBAAA,CAAArD,MAAA,CAAmC,UAAA8F,GAAA,CAAAzC,gBAAA,CAAAlD,KAAA,WAAA2F,GAAA,CAAAzC,gBAAA,CAAAnC,KAAA,WAAA4E,GAAA,CAAAzC,gBAAA,CAAA9B,KAAA;QAanCwE,EAAA,CAAAK,SAAA,GAAoC;QAApCL,EAAA,CAAAM,UAAA,WAAAP,GAAA,CAAAvB,iBAAA,CAAAvE,MAAA,CAAoC,UAAA8F,GAAA,CAAAvB,iBAAA,CAAApE,KAAA,WAAA2F,GAAA,CAAAvB,iBAAA,CAAArD,KAAA,WAAA4E,GAAA,CAAAvB,iBAAA,CAAAhD,KAAA;QAepCwE,EAAA,CAAAK,SAAA,GAAsC;QAAtCL,EAAA,CAAAM,UAAA,WAAAP,GAAA,CAAApB,mBAAA,CAAA1E,MAAA,CAAsC,UAAA8F,GAAA,CAAApB,mBAAA,CAAAvE,KAAA,WAAA2F,GAAA,CAAApB,mBAAA,CAAAxD,KAAA,WAAA4E,GAAA,CAAApB,mBAAA,CAAAnD,KAAA;QAatCwE,EAAA,CAAAK,SAAA,GAAmC;QAAnCL,EAAA,CAAAM,UAAA,WAAAP,GAAA,CAAAf,gBAAA,CAAA/E,MAAA,CAAmC,UAAA8F,GAAA,CAAAf,gBAAA,CAAA5E,KAAA,WAAA2F,GAAA,CAAAf,gBAAA,CAAA7D,KAAA,WAAA4E,GAAA,CAAAf,gBAAA,CAAAxD,KAAA;QAgBnCwE,EAAA,CAAAK,SAAA,GAAmC;QAAnCL,EAAA,CAAAM,UAAA,WAAAP,GAAA,CAAAb,eAAA,CAAAC,OAAA,CAAmC,UAAAY,GAAA,CAAAb,eAAA,CAAA9E,KAAA,WAAA2F,GAAA,CAAAb,eAAA,CAAA1D,KAAA;QAanCwE,EAAA,CAAAK,SAAA,GAAqC;QAArCL,EAAA,CAAAM,UAAA,WAAAP,GAAA,CAAAR,kBAAA,CAAAtF,MAAA,CAAqC,UAAA8F,GAAA,CAAAR,kBAAA,CAAAnF,KAAA,WAAA2F,GAAA,CAAAR,kBAAA,CAAA/D,KAAA;;;mBDxElD3B,mBAAmB,EAAED,kBAAkB,EAAA6G,EAAA,CAAAC,cAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}