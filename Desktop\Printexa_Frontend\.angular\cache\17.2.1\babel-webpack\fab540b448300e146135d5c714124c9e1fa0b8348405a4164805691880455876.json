{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport ThemeRiverView from './ThemeRiverView.js';\nimport ThemeRiverSeriesModel from './ThemeRiverSeries.js';\nimport themeRiverLayout from './themeRiverLayout.js';\nimport dataFilter from '../../processor/dataFilter.js';\nexport function install(registers) {\n  registers.registerChartView(ThemeRiverView);\n  registers.registerSeriesModel(ThemeRiverSeriesModel);\n  registers.registerLayout(themeRiverLayout);\n  registers.registerProcessor(dataFilter('themeRiver'));\n}", "map": {"version": 3, "names": ["ThemeRiverView", "ThemeRiverSeriesModel", "themeRiverLayout", "dataFilter", "install", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "registerProcessor"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/echarts/lib/chart/themeRiver/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport ThemeRiverView from './ThemeRiverView.js';\nimport ThemeRiverSeriesModel from './ThemeRiverSeries.js';\nimport themeRiverLayout from './themeRiverLayout.js';\nimport dataFilter from '../../processor/dataFilter.js';\nexport function install(registers) {\n  registers.registerChartView(ThemeRiverView);\n  registers.registerSeriesModel(ThemeRiverSeriesModel);\n  registers.registerLayout(themeRiverLayout);\n  registers.registerProcessor(dataFilter('themeRiver'));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,OAAOC,qBAAqB,MAAM,uBAAuB;AACzD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACN,cAAc,CAAC;EAC3CK,SAAS,CAACE,mBAAmB,CAACN,qBAAqB,CAAC;EACpDI,SAAS,CAACG,cAAc,CAACN,gBAAgB,CAAC;EAC1CG,SAAS,CAACI,iBAAiB,CAACN,UAAU,CAAC,YAAY,CAAC,CAAC;AACvD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}