{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { UnsubscribeOnDestroyAdapter } from '@shared/UnsubscribeOnDestroyAdapter';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ContactsService extends UnsubscribeOnDestroyAdapter {\n  constructor(httpClient) {\n    super();\n    this.httpClient = httpClient;\n    this.API_URL = 'assets/data/contacts.json';\n    this.isTblLoading = true;\n    this.dataChange = new BehaviorSubject([]);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  /** CRUD METHODS */\n  getAllContactss() {\n    this.subs.sink = this.httpClient.get(this.API_URL).subscribe({\n      next: data => {\n        this.isTblLoading = false;\n        this.dataChange.next(data);\n      },\n      error: error => {\n        this.isTblLoading = false;\n        console.log(error.name + ' ' + error.message);\n      }\n    });\n  }\n  addContacts(contacts) {\n    this.dialogData = contacts;\n    // this.httpClient.post(this.API_URL, contacts)\n    //   .subscribe({\n    //     next: (data) => {\n    //       this.dialogData = contacts;\n    //     },\n    //     error: (error: HttpErrorResponse) => {\n    //        // error code here\n    //     },\n    //   });\n  }\n  updateContacts(contacts) {\n    this.dialogData = contacts;\n    // this.httpClient.put(this.API_URL + contacts.id, contacts)\n    //     .subscribe({\n    //       next: (data) => {\n    //         this.dialogData = contacts;\n    //       },\n    //       error: (error: HttpErrorResponse) => {\n    //          // error code here\n    //       },\n    //     });\n  }\n  deleteContacts(id) {\n    console.log(id);\n    // this.httpClient.delete(this.API_URL + id)\n    //     .subscribe({\n    //       next: (data) => {\n    //         console.log(id);\n    //       },\n    //       error: (error: HttpErrorResponse) => {\n    //          // error code here\n    //       },\n    //     });\n  }\n  static #_ = this.ɵfac = function ContactsService_Factory(t) {\n    return new (t || ContactsService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ContactsService,\n    factory: ContactsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "UnsubscribeOnDestroyAdapter", "ContactsService", "constructor", "httpClient", "API_URL", "isTblLoading", "dataChange", "data", "value", "getDialogData", "dialogData", "getAllContactss", "subs", "sink", "get", "subscribe", "next", "error", "console", "log", "name", "message", "addContacts", "contacts", "updateContacts", "deleteContacts", "id", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\mian\\src\\app\\contacts\\contacts.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { Contacts } from './contacts.model';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { UnsubscribeOnDestroyAdapter } from '@shared/UnsubscribeOnDestroyAdapter';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\n\r\nexport class ContactsService extends UnsubscribeOnDestroyAdapter {\r\n  private readonly API_URL = 'assets/data/contacts.json';\r\n  isTblLoading = true;\r\n  dataChange: BehaviorSubject<Contacts[]> = new BehaviorSubject<Contacts[]>([]);\r\n  // Temporarily stores data from dialogs\r\n  dialogData!: Contacts;\r\n  constructor(private httpClient: HttpClient) {\r\n    super();\r\n  }\r\n  get data(): Contacts[] {\r\n    return this.dataChange.value;\r\n  }\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n  /** CRUD METHODS */\r\n  getAllContactss(): void {\r\n    this.subs.sink = this.httpClient.get<Contacts[]>(this.API_URL).subscribe({\r\n      next: (data) => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(data);\r\n      },\r\n      error: (error: HttpErrorResponse) => {\r\n        this.isTblLoading = false;\r\n        console.log(error.name + ' ' + error.message);\r\n      },\r\n    });\r\n  }\r\n  addContacts(contacts: Contacts): void {\r\n    this.dialogData = contacts;\r\n\r\n    // this.httpClient.post(this.API_URL, contacts)\r\n    //   .subscribe({\r\n    //     next: (data) => {\r\n    //       this.dialogData = contacts;\r\n    //     },\r\n    //     error: (error: HttpErrorResponse) => {\r\n    //        // error code here\r\n    //     },\r\n    //   });\r\n  }\r\n  updateContacts(contacts: Contacts): void {\r\n    this.dialogData = contacts;\r\n\r\n    // this.httpClient.put(this.API_URL + contacts.id, contacts)\r\n    //     .subscribe({\r\n    //       next: (data) => {\r\n    //         this.dialogData = contacts;\r\n    //       },\r\n    //       error: (error: HttpErrorResponse) => {\r\n    //          // error code here\r\n    //       },\r\n    //     });\r\n  }\r\n  deleteContacts(id: number): void {\r\n    console.log(id);\r\n\r\n    // this.httpClient.delete(this.API_URL + id)\r\n    //     .subscribe({\r\n    //       next: (data) => {\r\n    //         console.log(id);\r\n    //       },\r\n    //       error: (error: HttpErrorResponse) => {\r\n    //          // error code here\r\n    //       },\r\n    //     });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;AAGtC,SAASC,2BAA2B,QAAQ,qCAAqC;;;AAMjF,OAAM,MAAOC,eAAgB,SAAQD,2BAA2B;EAM9DE,YAAoBC,UAAsB;IACxC,KAAK,EAAE;IADW,KAAAA,UAAU,GAAVA,UAAU;IALb,KAAAC,OAAO,GAAG,2BAA2B;IACtD,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,UAAU,GAAgC,IAAIP,eAAe,CAAa,EAAE,CAAC;EAK7E;EACA,IAAIQ,IAAIA,CAAA;IACN,OAAO,IAAI,CAACD,UAAU,CAACE,KAAK;EAC9B;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EACA;EACAC,eAAeA,CAAA;IACb,IAAI,CAACC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACV,UAAU,CAACW,GAAG,CAAa,IAAI,CAACV,OAAO,CAAC,CAACW,SAAS,CAAC;MACvEC,IAAI,EAAGT,IAAI,IAAI;QACb,IAAI,CAACF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,UAAU,CAACU,IAAI,CAACT,IAAI,CAAC;MAC5B,CAAC;MACDU,KAAK,EAAGA,KAAwB,IAAI;QAClC,IAAI,CAACZ,YAAY,GAAG,KAAK;QACzBa,OAAO,CAACC,GAAG,CAACF,KAAK,CAACG,IAAI,GAAG,GAAG,GAAGH,KAAK,CAACI,OAAO,CAAC;MAC/C;KACD,CAAC;EACJ;EACAC,WAAWA,CAACC,QAAkB;IAC5B,IAAI,CAACb,UAAU,GAAGa,QAAQ;IAE1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EACAC,cAAcA,CAACD,QAAkB;IAC/B,IAAI,CAACb,UAAU,GAAGa,QAAQ;IAE1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EACAE,cAAcA,CAACC,EAAU;IACvBR,OAAO,CAACC,GAAG,CAACO,EAAE,CAAC;IAEf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAAC,QAAAC,CAAA,G;qBAlEU1B,eAAe,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAf/B,eAAe;IAAAgC,OAAA,EAAfhC,eAAe,CAAAiC,IAAA;IAAAC,UAAA,EAHd;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}