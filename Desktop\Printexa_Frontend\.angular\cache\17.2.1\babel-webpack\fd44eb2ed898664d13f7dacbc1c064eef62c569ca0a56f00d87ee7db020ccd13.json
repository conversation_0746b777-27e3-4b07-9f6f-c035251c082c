{"ast": null, "code": "import { bisector, tickStep } from \"d3-array\";\nimport { durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear } from \"./duration.js\";\nimport millisecond from \"./millisecond.js\";\nimport second from \"./second.js\";\nimport minute from \"./minute.js\";\nimport hour from \"./hour.js\";\nimport day from \"./day.js\";\nimport { sunday as week } from \"./week.js\";\nimport month from \"./month.js\";\nimport year from \"./year.js\";\nimport utcMinute from \"./utcMinute.js\";\nimport utcHour from \"./utcHour.js\";\nimport utcDay from \"./utcDay.js\";\nimport { utcSunday as utcWeek } from \"./utcWeek.js\";\nimport utcMonth from \"./utcMonth.js\";\nimport utcYear from \"./utcYear.js\";\nfunction ticker(year, month, week, day, hour, minute) {\n  const tickIntervals = [[second, 1, durationSecond], [second, 5, 5 * durationSecond], [second, 15, 15 * durationSecond], [second, 30, 30 * durationSecond], [minute, 1, durationMinute], [minute, 5, 5 * durationMinute], [minute, 15, 15 * durationMinute], [minute, 30, 30 * durationMinute], [hour, 1, durationHour], [hour, 3, 3 * durationHour], [hour, 6, 6 * durationHour], [hour, 12, 12 * durationHour], [day, 1, durationDay], [day, 2, 2 * durationDay], [week, 1, durationWeek], [month, 1, durationMonth], [month, 3, 3 * durationMonth], [year, 1, durationYear]];\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n  return [ticks, tickInterval];\n}\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(year, month, week, day, hour, minute);\nexport { utcTicks, utcTickInterval, timeTicks, timeTickInterval };", "map": {"version": 3, "names": ["bisector", "tickStep", "durationDay", "durationHour", "durationMinute", "durationMonth", "durationSecond", "durationWeek", "durationYear", "millisecond", "second", "minute", "hour", "day", "sunday", "week", "month", "year", "utcMinute", "utcHour", "utcDay", "utcSunday", "utcWeek", "utcMonth", "utcYear", "ticker", "tickIntervals", "ticks", "start", "stop", "count", "reverse", "interval", "range", "tickInterval", "target", "Math", "abs", "i", "step", "right", "length", "every", "max", "t", "utcTicks", "utcTickInterval", "timeTicks", "timeTickInterval"], "sources": ["C:/Users/<USER>/Desktop/Rapport_PFE/kuber/source/mian/node_modules/d3-time-format/node_modules/d3-time/src/ticks.js"], "sourcesContent": ["import {bisector, tickStep} from \"d3-array\";\nimport {durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear} from \"./duration.js\";\nimport millisecond from \"./millisecond.js\";\nimport second from \"./second.js\";\nimport minute from \"./minute.js\";\nimport hour from \"./hour.js\";\nimport day from \"./day.js\";\nimport {sunday as week} from \"./week.js\";\nimport month from \"./month.js\";\nimport year from \"./year.js\";\nimport utcMinute from \"./utcMinute.js\";\nimport utcHour from \"./utcHour.js\";\nimport utcDay from \"./utcDay.js\";\nimport {utcSunday as utcWeek} from \"./utcWeek.js\";\nimport utcMonth from \"./utcMonth.js\";\nimport utcYear from \"./utcYear.js\";\n\nfunction ticker(year, month, week, day, hour, minute) {\n\n  const tickIntervals = [\n    [second,  1,      durationSecond],\n    [second,  5,  5 * durationSecond],\n    [second, 15, 15 * durationSecond],\n    [second, 30, 30 * durationSecond],\n    [minute,  1,      durationMinute],\n    [minute,  5,  5 * durationMinute],\n    [minute, 15, 15 * durationMinute],\n    [minute, 30, 30 * durationMinute],\n    [  hour,  1,      durationHour  ],\n    [  hour,  3,  3 * durationHour  ],\n    [  hour,  6,  6 * durationHour  ],\n    [  hour, 12, 12 * durationHour  ],\n    [   day,  1,      durationDay   ],\n    [   day,  2,  2 * durationDay   ],\n    [  week,  1,      durationWeek  ],\n    [ month,  1,      durationMonth ],\n    [ month,  3,  3 * durationMonth ],\n    [  year,  1,      durationYear  ]\n  ];\n\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n\n  return [ticks, tickInterval];\n}\n\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(year, month, week, day, hour, minute);\n\nexport {utcTicks, utcTickInterval, timeTicks, timeTickInterval};\n"], "mappings": "AAAA,SAAQA,QAAQ,EAAEC,QAAQ,QAAO,UAAU;AAC3C,SAAQC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,QAAO,eAAe;AAClI,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,GAAG,MAAM,UAAU;AAC1B,SAAQC,MAAM,IAAIC,IAAI,QAAO,WAAW;AACxC,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,MAAM,MAAM,aAAa;AAChC,SAAQC,SAAS,IAAIC,OAAO,QAAO,cAAc;AACjD,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,OAAO,MAAM,cAAc;AAElC,SAASC,MAAMA,CAACR,IAAI,EAAED,KAAK,EAAED,IAAI,EAAEF,GAAG,EAAED,IAAI,EAAED,MAAM,EAAE;EAEpD,MAAMe,aAAa,GAAG,CACpB,CAAChB,MAAM,EAAG,CAAC,EAAOJ,cAAc,CAAC,EACjC,CAACI,MAAM,EAAG,CAAC,EAAG,CAAC,GAAGJ,cAAc,CAAC,EACjC,CAACI,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGJ,cAAc,CAAC,EACjC,CAACI,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGJ,cAAc,CAAC,EACjC,CAACK,MAAM,EAAG,CAAC,EAAOP,cAAc,CAAC,EACjC,CAACO,MAAM,EAAG,CAAC,EAAG,CAAC,GAAGP,cAAc,CAAC,EACjC,CAACO,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGP,cAAc,CAAC,EACjC,CAACO,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGP,cAAc,CAAC,EACjC,CAAGQ,IAAI,EAAG,CAAC,EAAOT,YAAY,CAAG,EACjC,CAAGS,IAAI,EAAG,CAAC,EAAG,CAAC,GAAGT,YAAY,CAAG,EACjC,CAAGS,IAAI,EAAG,CAAC,EAAG,CAAC,GAAGT,YAAY,CAAG,EACjC,CAAGS,IAAI,EAAE,EAAE,EAAE,EAAE,GAAGT,YAAY,CAAG,EACjC,CAAIU,GAAG,EAAG,CAAC,EAAOX,WAAW,CAAI,EACjC,CAAIW,GAAG,EAAG,CAAC,EAAG,CAAC,GAAGX,WAAW,CAAI,EACjC,CAAGa,IAAI,EAAG,CAAC,EAAOR,YAAY,CAAG,EACjC,CAAES,KAAK,EAAG,CAAC,EAAOX,aAAa,CAAE,EACjC,CAAEW,KAAK,EAAG,CAAC,EAAG,CAAC,GAAGX,aAAa,CAAE,EACjC,CAAGY,IAAI,EAAG,CAAC,EAAOT,YAAY,CAAG,CAClC;EAED,SAASmB,KAAKA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACjC,MAAMC,OAAO,GAAGF,IAAI,GAAGD,KAAK;IAC5B,IAAIG,OAAO,EAAE,CAACH,KAAK,EAAEC,IAAI,CAAC,GAAG,CAACA,IAAI,EAAED,KAAK,CAAC;IAC1C,MAAMI,QAAQ,GAAGF,KAAK,IAAI,OAAOA,KAAK,CAACG,KAAK,KAAK,UAAU,GAAGH,KAAK,GAAGI,YAAY,CAACN,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;IACtG,MAAMH,KAAK,GAAGK,QAAQ,GAAGA,QAAQ,CAACC,KAAK,CAACL,KAAK,EAAE,CAACC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAChE,OAAOE,OAAO,GAAGJ,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGJ,KAAK;EAC1C;EAEA,SAASO,YAAYA,CAACN,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACxC,MAAMK,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACR,IAAI,GAAGD,KAAK,CAAC,GAAGE,KAAK;IAC7C,MAAMQ,CAAC,GAAGtC,QAAQ,CAAC,CAAC,IAAIuC,IAAI,CAAC,KAAKA,IAAI,CAAC,CAACC,KAAK,CAACd,aAAa,EAAES,MAAM,CAAC;IACpE,IAAIG,CAAC,KAAKZ,aAAa,CAACe,MAAM,EAAE,OAAOxB,IAAI,CAACyB,KAAK,CAACzC,QAAQ,CAAC2B,KAAK,GAAGpB,YAAY,EAAEqB,IAAI,GAAGrB,YAAY,EAAEsB,KAAK,CAAC,CAAC;IAC7G,IAAIQ,CAAC,KAAK,CAAC,EAAE,OAAO7B,WAAW,CAACiC,KAAK,CAACN,IAAI,CAACO,GAAG,CAAC1C,QAAQ,CAAC2B,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IAChF,MAAM,CAACc,CAAC,EAAEL,IAAI,CAAC,GAAGb,aAAa,CAACS,MAAM,GAAGT,aAAa,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGZ,aAAa,CAACY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,MAAM,GAAGG,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;IAC5G,OAAOM,CAAC,CAACF,KAAK,CAACH,IAAI,CAAC;EACtB;EAEA,OAAO,CAACZ,KAAK,EAAEO,YAAY,CAAC;AAC9B;AAEA,MAAM,CAACW,QAAQ,EAAEC,eAAe,CAAC,GAAGrB,MAAM,CAACD,OAAO,EAAED,QAAQ,EAAED,OAAO,EAAEF,MAAM,EAAED,OAAO,EAAED,SAAS,CAAC;AAClG,MAAM,CAAC6B,SAAS,EAAEC,gBAAgB,CAAC,GAAGvB,MAAM,CAACR,IAAI,EAAED,KAAK,EAAED,IAAI,EAAEF,GAAG,EAAED,IAAI,EAAED,MAAM,CAAC;AAElF,SAAQkC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}