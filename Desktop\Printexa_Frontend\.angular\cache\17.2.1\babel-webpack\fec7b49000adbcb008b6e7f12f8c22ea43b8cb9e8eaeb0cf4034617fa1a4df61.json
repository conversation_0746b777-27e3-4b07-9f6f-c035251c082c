{"ast": null, "code": "import { RouterLink } from '@angular/router';\nimport { Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { Role } from '@core';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@core\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/icon\";\nfunction SigninComponent_Conditional_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_Conditional_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_Conditional_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nexport class SigninComponent extends UnsubscribeOnDestroyAdapter {\n  constructor(formBuilder, route, router, authService) {\n    super();\n    this.formBuilder = formBuilder;\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.submitted = false;\n    this.loading = false;\n    this.error = '';\n    this.hide = true;\n  }\n  ngOnInit() {\n    this.authForm = this.formBuilder.group({\n      username: ['<EMAIL>', Validators.required],\n      password: ['admin@123', Validators.required]\n    });\n  }\n  get f() {\n    return this.authForm.controls;\n  }\n  adminSet() {\n    this.authForm.get('username')?.setValue('<EMAIL>');\n    this.authForm.get('password')?.setValue('admin@123');\n  }\n  employeeSet() {\n    this.authForm.get('username')?.setValue('<EMAIL>');\n    this.authForm.get('password')?.setValue('employee@123');\n  }\n  clientSet() {\n    this.authForm.get('username')?.setValue('<EMAIL>');\n    this.authForm.get('password')?.setValue('client@123');\n  }\n  onSubmit() {\n    this.submitted = true;\n    this.loading = true;\n    this.error = '';\n    if (this.authForm.invalid) {\n      this.error = 'Username and Password not valid !';\n      return;\n    } else {\n      this.subs.sink = this.authService.login(this.f['username'].value, this.f['password'].value).subscribe(res => {\n        if (res) {\n          setTimeout(() => {\n            const role = this.authService.currentUserValue.role;\n            if (role === Role.All || role === Role.Admin) {\n              this.router.navigate(['/admin/dashboard/main']);\n            } else if (role === Role.Employee) {\n              this.router.navigate(['/employee/dashboard']);\n            } else if (role === Role.Client) {\n              this.router.navigate(['/client/dashboard']);\n            } else {\n              this.router.navigate(['/authentication/signin']);\n            }\n            this.loading = false;\n          }, 1000);\n        } else {\n          this.error = 'Invalid Login';\n        }\n      }, error => {\n        this.error = error;\n        this.submitted = false;\n        this.loading = false;\n      });\n    }\n  }\n  static #_ = this.ɵfac = function SigninComponent_Factory(t) {\n    return new (t || SigninComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SigninComponent,\n    selectors: [[\"app-signin\"]],\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    decls: 73,\n    vars: 10,\n    consts: [[1, \"auth-container\"], [1, \"row\", \"auth-main\"], [1, \"col-sm-6\", \"px-0\", \"d-none\", \"d-sm-block\"], [1, \"left-img\", 2, \"background-image\", \"url(assets/images/pages/bg-01.png)\"], [1, \"col-sm-6\", \"auth-form-section\"], [1, \"form-section\"], [1, \"auth-wrapper\"], [1, \"welcome-msg\"], [1, \"auth-signup-text\", \"text-muted\"], [\"routerLink\", \"/authentication/signup\", 1, \"sign-up-link\"], [1, \"d-flex\", \"justify-content-between\", \"lbl-alert\", \"mb-3\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [1, \"login-title\"], [1, \"validate-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"appearance\", \"outline\", 1, \"example-full-width\"], [\"matInput\", \"\", \"formControlName\", \"username\"], [\"matSuffix\", \"\", 1, \"material-icons-outlined\", \"color-icon\", \"p-3\"], [1, \"col-xl-12col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"matInput\", \"\", \"formControlName\", \"password\", 3, \"type\"], [\"matSuffix\", \"\", 1, \"material-icons-outlined\", \"pwd-toggle\", \"p-3\", 3, \"click\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-5\"], [1, \"form-check\"], [1, \"form-check-label\"], [\"type\", \"checkbox\", \"value\", \"\", 1, \"form-check-input\"], [1, \"form-check-sign\"], [1, \"check\"], [\"routerLink\", \"/authentication/forgot-password\", 1, \"txt1\"], [\"class\", \"alert alert-danger mt-3 mb-0\"], [1, \"container-auth-form-btn\"], [2, \"text-align\", \"center\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"auth-form-btn\", 3, \"disabled\"], [1, \"social-login-title\"], [1, \"list-unstyled\", \"social-icon\", \"mb-0\", \"mt-3\"], [1, \"list-inline-item\"], [\"href\", \"javascript:void(0)\", 1, \"rounded\"], [1, \"fab\", \"fa-google\"], [\"href\", \"javascript:void(0)\", 1, \"rounded\", \"flex-c-m\"], [1, \"fab\", \"fa-facebook-f\"], [1, \"fab\", \"fa-twitter\"], [1, \"fab\", \"fa-linkedin-in\"], [1, \"alert\", \"alert-danger\", \"mt-3\", \"mb-0\"]],\n    template: function SigninComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"h2\", 7);\n        i0.ɵɵtext(8, \" Welcome to Kuber \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\", 8);\n        i0.ɵɵtext(10, \"Need an account?\");\n        i0.ɵɵelementStart(11, \"a\", 9);\n        i0.ɵɵtext(12, \"Sign Up \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\")(15, \"button\", 11);\n        i0.ɵɵlistener(\"click\", function SigninComponent_Template_button_click_15_listener() {\n          return ctx.adminSet();\n        });\n        i0.ɵɵtext(16, \"Admin\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\")(18, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function SigninComponent_Template_button_click_18_listener() {\n          return ctx.employeeSet();\n        });\n        i0.ɵɵtext(19, \"Employee\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\")(21, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function SigninComponent_Template_button_click_21_listener() {\n          return ctx.clientSet();\n        });\n        i0.ɵɵtext(22, \"Client\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(23, \"h2\", 14);\n        i0.ɵɵtext(24, \"Sign in\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"form\", 15);\n        i0.ɵɵlistener(\"ngSubmit\", function SigninComponent_Template_form_ngSubmit_25_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(26, \"div\", 16)(27, \"div\", 17)(28, \"mat-form-field\", 18)(29, \"mat-label\");\n        i0.ɵɵtext(30, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(31, \"input\", 19);\n        i0.ɵɵelementStart(32, \"mat-icon\", 20);\n        i0.ɵɵtext(33, \"face\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(34, SigninComponent_Conditional_34_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(35, \"div\", 16)(36, \"div\", 21)(37, \"mat-form-field\", 18)(38, \"mat-label\");\n        i0.ɵɵtext(39, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(40, \"input\", 22);\n        i0.ɵɵelementStart(41, \"mat-icon\", 23);\n        i0.ɵɵlistener(\"click\", function SigninComponent_Template_mat_icon_click_41_listener() {\n          return ctx.hide = !ctx.hide;\n        });\n        i0.ɵɵtext(42);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(43, SigninComponent_Conditional_43_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(44, \"div\", 24)(45, \"div\", 25)(46, \"label\", 26);\n        i0.ɵɵelement(47, \"input\", 27);\n        i0.ɵɵtext(48, \" Remember me \");\n        i0.ɵɵelementStart(49, \"span\", 28);\n        i0.ɵɵelement(50, \"span\", 29);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(51, \"a\", 30);\n        i0.ɵɵtext(52, \"Forgot Password?\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(53, SigninComponent_Conditional_53_Template, 2, 1, \"div\", 31);\n        i0.ɵɵelementStart(54, \"div\", 32)(55, \"div\", 33)(56, \"button\", 34);\n        i0.ɵɵtext(57, \"Login\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(58, \"h6\", 35);\n        i0.ɵɵtext(59, \"OR\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"ul\", 36)(61, \"li\", 37)(62, \"a\", 38);\n        i0.ɵɵelement(63, \"i\", 39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(64, \"li\", 37)(65, \"a\", 40);\n        i0.ɵɵelement(66, \"i\", 41);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(67, \"li\", 37)(68, \"a\", 38);\n        i0.ɵɵelement(69, \"i\", 42);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(70, \"li\", 37)(71, \"a\", 38);\n        i0.ɵɵelement(72, \"i\", 43);\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_4_0;\n        i0.ɵɵadvance(25);\n        i0.ɵɵproperty(\"formGroup\", ctx.authForm);\n        i0.ɵɵadvance(9);\n        i0.ɵɵconditional(34, ((tmp_1_0 = ctx.authForm.get(\"username\")) == null ? null : tmp_1_0.hasError(\"required\")) ? 34 : -1);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"type\", ctx.hide ? \"password\" : \"text\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.hide ? \"visibility_off\" : \"visibility\", \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(43, ((tmp_4_0 = ctx.authForm.get(\"password\")) == null ? null : tmp_4_0.hasError(\"required\")) ? 43 : -1);\n        i0.ɵɵadvance(10);\n        i0.ɵɵconditional(53, ctx.error ? 53 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"auth-spinner\", ctx.loading);\n        i0.ɵɵproperty(\"disabled\", ctx.loading)(\"disabled\", !ctx.authForm.valid);\n      }\n    },\n    dependencies: [RouterLink, MatButtonModule, i4.MatButton, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, MatFormFieldModule, i5.MatFormField, i5.MatLabel, i5.MatError, i5.MatSuffix, MatInputModule, i6.MatInput, MatIconModule, i7.MatIcon],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["RouterLink", "Validators", "FormsModule", "ReactiveFormsModule", "Role", "UnsubscribeOnDestroyAdapter", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatButtonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r2", "error", "SigninComponent", "constructor", "formBuilder", "route", "router", "authService", "submitted", "loading", "hide", "ngOnInit", "authForm", "group", "username", "required", "password", "f", "controls", "adminSet", "get", "setValue", "employeeSet", "clientSet", "onSubmit", "invalid", "subs", "sink", "login", "value", "subscribe", "res", "setTimeout", "role", "currentUserValue", "All", "Admin", "navigate", "Employee", "Client", "_", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "i2", "ActivatedRoute", "Router", "i3", "AuthService", "_2", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SigninComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "SigninComponent_Template_button_click_15_listener", "SigninComponent_Template_button_click_18_listener", "SigninComponent_Template_button_click_21_listener", "SigninComponent_Template_form_ngSubmit_25_listener", "ɵɵtemplate", "SigninComponent_Conditional_34_Template", "SigninComponent_Template_mat_icon_click_41_listener", "SigninComponent_Conditional_43_Template", "SigninComponent_Conditional_53_Template", "ɵɵproperty", "ɵɵconditional", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "tmp_4_0", "ɵɵclassProp", "valid", "i4", "MatButton", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i5", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i6", "MatInput", "i7", "MatIcon", "styles"], "sources": ["C:\\Users\\<USER>\\mian\\src\\app\\authentication\\signin\\signin.component.ts", "C:\\Users\\<USER>\\mian\\src\\app\\authentication\\signin\\signin.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router, ActivatedRoute, RouterLink } from '@angular/router';\r\nimport { UntypedFormBuilder, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { Role, AuthService } from '@core';\r\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatButtonModule } from '@angular/material/button';\r\n@Component({\r\n    selector: 'app-signin',\r\n    templateUrl: './signin.component.html',\r\n    styleUrls: ['./signin.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        RouterLink,\r\n        MatButtonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        MatFormFieldModule,\r\n        MatInputModule,\r\n        MatIconModule,\r\n    ],\r\n})\r\nexport class SigninComponent\r\n  extends UnsubscribeOnDestroyAdapter\r\n  implements OnInit\r\n{\r\n  authForm!: UntypedFormGroup;\r\n  submitted = false;\r\n  loading = false;\r\n  error = '';\r\n  hide = true;\r\n  constructor(\r\n    private formBuilder: UntypedFormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.authForm = this.formBuilder.group({\r\n      username: ['<EMAIL>', Validators.required],\r\n      password: ['admin@123', Validators.required],\r\n    });\r\n  }\r\n  get f() {\r\n    return this.authForm.controls;\r\n  }\r\n  adminSet() {\r\n    this.authForm.get('username')?.setValue('<EMAIL>');\r\n    this.authForm.get('password')?.setValue('admin@123');\r\n  }\r\n  employeeSet() {\r\n    this.authForm.get('username')?.setValue('<EMAIL>');\r\n    this.authForm.get('password')?.setValue('employee@123');\r\n  }\r\n  clientSet() {\r\n    this.authForm.get('username')?.setValue('<EMAIL>');\r\n    this.authForm.get('password')?.setValue('client@123');\r\n  }\r\n  onSubmit() {\r\n    this.submitted = true;\r\n    this.loading = true;\r\n    this.error = '';\r\n    if (this.authForm.invalid) {\r\n      this.error = 'Username and Password not valid !';\r\n      return;\r\n    } else {\r\n      this.subs.sink = this.authService\r\n        .login(this.f['username'].value, this.f['password'].value)\r\n        .subscribe(\r\n          (res) => {\r\n            if (res) {\r\n              setTimeout(() => {\r\n                const role = this.authService.currentUserValue.role;\r\n                if (role === Role.All || role === Role.Admin) {\r\n                  this.router.navigate(['/admin/dashboard/main']);\r\n                } else if (role === Role.Employee) {\r\n                  this.router.navigate(['/employee/dashboard']);\r\n                } else if (role === Role.Client) {\r\n                  this.router.navigate(['/client/dashboard']);\r\n                } else {\r\n                  this.router.navigate(['/authentication/signin']);\r\n                }\r\n                this.loading = false;\r\n              }, 1000);\r\n            } else {\r\n              this.error = 'Invalid Login';\r\n            }\r\n          },\r\n          (error) => {\r\n            this.error = error;\r\n            this.submitted = false;\r\n            this.loading = false;\r\n          }\r\n        );\r\n    }\r\n  }\r\n}\r\n", "<div class=\"auth-container\">\r\n  <div class=\"row auth-main\">\r\n    <div class=\"col-sm-6 px-0 d-none d-sm-block\">\r\n      <div class=\"left-img\" style=\"background-image: url(assets/images/pages/bg-01.png);\">\r\n      </div>\r\n    </div>\r\n    <div class=\"col-sm-6 auth-form-section\">\r\n      <div class=\"form-section\">\r\n        <div class=\"auth-wrapper\">\r\n          <h2 class=\"welcome-msg\"> Welcome to Kuber </h2>\r\n          <p class=\"auth-signup-text text-muted\">Need an account?<a routerLink=\"/authentication/signup\"\r\n              class=\"sign-up-link\">Sign Up\r\n            </a></p>\r\n          <div class=\"d-flex justify-content-between lbl-alert mb-3\">\r\n            <div>\r\n              <button mat-raised-button color=\"primary\" type=\"button\" (click)=\"adminSet();\">Admin</button>\r\n            </div>\r\n            <div>\r\n              <button mat-raised-button color=\"accent\" type=\"button\" (click)=\"employeeSet();\">Employee</button>\r\n            </div>\r\n            <div>\r\n              <button mat-raised-button color=\"warn\" type=\"button\" (click)=\"clientSet();\">Client</button>\r\n            </div>\r\n          </div>\r\n          <h2 class=\"login-title\">Sign in</h2>\r\n          <form class=\"validate-form\" [formGroup]=\"authForm\" (ngSubmit)=\"onSubmit()\">\r\n            <div class=\"row\">\r\n              <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                  <mat-label>Username</mat-label>\r\n                  <input matInput formControlName=\"username\" />\r\n                  <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>face</mat-icon>\r\n                  @if (authForm.get('username')?.hasError('required')) {\r\n                  <mat-error>\r\n                    Username is required\r\n                  </mat-error>\r\n                  }\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n            <div class=\"row\">\r\n              <div class=\"col-xl-12col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                  <mat-label>Password</mat-label>\r\n                  <input matInput [type]=\"hide ? 'password' : 'text'\" formControlName=\"password\">\r\n                  <mat-icon class=\"material-icons-outlined pwd-toggle p-3\" matSuffix (click)=\"hide = !hide\">\r\n                    {{hide ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n                  @if (authForm.get('password')?.hasError('required')) {\r\n                  <mat-error>\r\n                    Password is required\r\n                  </mat-error>\r\n                  }\r\n                </mat-form-field>\r\n              </div>\r\n            </div>\r\n            <div class=\"d-flex justify-content-between align-items-center mb-5\">\r\n              <div class=\"form-check\">\r\n                <label class=\"form-check-label\">\r\n                  <input class=\"form-check-input\" type=\"checkbox\" value=\"\"> Remember me\r\n                  <span class=\"form-check-sign\">\r\n                    <span class=\"check\"></span>\r\n                  </span>\r\n                </label>\r\n              </div>\r\n              <a class=\"txt1\" routerLink=\"/authentication/forgot-password\">Forgot Password?</a>\r\n            </div>\r\n            @if (error) {\r\n            <div class=\"alert alert-danger mt-3 mb-0\">{{error}}</div>\r\n            }\r\n            <div class=\"container-auth-form-btn\">\r\n              <div style=\"text-align:center\">\r\n                <button mat-raised-button color=\"primary\" [class.auth-spinner]=\"loading\" [disabled]=\"loading\"\r\n                  class=\"auth-form-btn\" [disabled]=\"!authForm.valid \" type=\"submit\">Login</button>\r\n              </div>\r\n            </div>\r\n          </form>\r\n          <h6 class=\"social-login-title\">OR</h6>\r\n          <ul class=\"list-unstyled social-icon mb-0 mt-3\">\r\n            <li class=\"list-inline-item\"><a href=\"javascript:void(0)\" class=\"rounded\">\r\n                <i class=\"fab fa-google\"></i>\r\n              </a></li>\r\n            <li class=\"list-inline-item\"><a href=\"javascript:void(0)\" class=\"rounded flex-c-m\">\r\n                <i class=\"fab fa-facebook-f\"></i>\r\n              </a></li>\r\n            <li class=\"list-inline-item\"><a href=\"javascript:void(0)\" class=\"rounded\">\r\n                <i class=\"fab fa-twitter\"></i>\r\n              </a></li>\r\n            <li class=\"list-inline-item\"><a href=\"javascript:void(0)\" class=\"rounded\">\r\n                <i class=\"fab fa-linkedin-in\"></i>\r\n              </a></li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,iBAAiB;AACpE,SAA+CC,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACnH,SAASC,IAAI,QAAqB,OAAO;AACzC,SAASC,2BAA2B,QAAQ,SAAS;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;;;;;;;;;;;ICyBxCC,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAaZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAiBlBH,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAfH,EAAA,CAAAI,SAAA,EAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAS;;;AD3C/D,OAAM,MAAOC,eACX,SAAQb,2BAA2B;EAQnCc,YACUC,WAA+B,EAC/BC,KAAqB,EACrBC,MAAc,EACdC,WAAwB;IAEhC,KAAK,EAAE;IALC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IARrB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAR,KAAK,GAAG,EAAE;IACV,KAAAS,IAAI,GAAG,IAAI;EAQX;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACR,WAAW,CAACS,KAAK,CAAC;MACrCC,QAAQ,EAAE,CAAC,oBAAoB,EAAE7B,UAAU,CAAC8B,QAAQ,CAAC;MACrDC,QAAQ,EAAE,CAAC,WAAW,EAAE/B,UAAU,CAAC8B,QAAQ;KAC5C,CAAC;EACJ;EACA,IAAIE,CAACA,CAAA;IACH,OAAO,IAAI,CAACL,QAAQ,CAACM,QAAQ;EAC/B;EACAC,QAAQA,CAAA;IACN,IAAI,CAACP,QAAQ,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAEC,QAAQ,CAAC,oBAAoB,CAAC;IAC7D,IAAI,CAACT,QAAQ,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAEC,QAAQ,CAAC,WAAW,CAAC;EACtD;EACAC,WAAWA,CAAA;IACT,IAAI,CAACV,QAAQ,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAEC,QAAQ,CAAC,uBAAuB,CAAC;IAChE,IAAI,CAACT,QAAQ,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAEC,QAAQ,CAAC,cAAc,CAAC;EACzD;EACAE,SAASA,CAAA;IACP,IAAI,CAACX,QAAQ,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAEC,QAAQ,CAAC,qBAAqB,CAAC;IAC9D,IAAI,CAACT,QAAQ,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAEC,QAAQ,CAAC,YAAY,CAAC;EACvD;EACAG,QAAQA,CAAA;IACN,IAAI,CAAChB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACR,KAAK,GAAG,EAAE;IACf,IAAI,IAAI,CAACW,QAAQ,CAACa,OAAO,EAAE;MACzB,IAAI,CAACxB,KAAK,GAAG,mCAAmC;MAChD;KACD,MAAM;MACL,IAAI,CAACyB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACpB,WAAW,CAC9BqB,KAAK,CAAC,IAAI,CAACX,CAAC,CAAC,UAAU,CAAC,CAACY,KAAK,EAAE,IAAI,CAACZ,CAAC,CAAC,UAAU,CAAC,CAACY,KAAK,CAAC,CACzDC,SAAS,CACPC,GAAG,IAAI;QACN,IAAIA,GAAG,EAAE;UACPC,UAAU,CAAC,MAAK;YACd,MAAMC,IAAI,GAAG,IAAI,CAAC1B,WAAW,CAAC2B,gBAAgB,CAACD,IAAI;YACnD,IAAIA,IAAI,KAAK7C,IAAI,CAAC+C,GAAG,IAAIF,IAAI,KAAK7C,IAAI,CAACgD,KAAK,EAAE;cAC5C,IAAI,CAAC9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;aAChD,MAAM,IAAIJ,IAAI,KAAK7C,IAAI,CAACkD,QAAQ,EAAE;cACjC,IAAI,CAAChC,MAAM,CAAC+B,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;aAC9C,MAAM,IAAIJ,IAAI,KAAK7C,IAAI,CAACmD,MAAM,EAAE;cAC/B,IAAI,CAACjC,MAAM,CAAC+B,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;aAC5C,MAAM;cACL,IAAI,CAAC/B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;;YAElD,IAAI,CAAC5B,OAAO,GAAG,KAAK;UACtB,CAAC,EAAE,IAAI,CAAC;SACT,MAAM;UACL,IAAI,CAACR,KAAK,GAAG,eAAe;;MAEhC,CAAC,EACAA,KAAK,IAAI;QACR,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACO,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,OAAO,GAAG,KAAK;MACtB,CAAC,CACF;;EAEP;EAAC,QAAA+B,CAAA,G;qBA5EUtC,eAAe,EAAAR,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAjD,EAAA,CAAA+C,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnD,EAAA,CAAA+C,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAApD,EAAA,CAAA+C,iBAAA,CAAAM,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAf/C,eAAe;IAAAgD,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA1D,EAAA,CAAA2D,0BAAA,EAAA3D,EAAA,CAAA4D,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCxB5BlE,EAAA,CAAAC,cAAA,aAA4B;QAGtBD,EAAA,CAAAoE,SAAA,aACM;QACRpE,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAAwC;QAGTD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC/CH,EAAA,CAAAC,cAAA,WAAuC;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAC,cAAA,YAC9B;QAAAD,EAAA,CAAAE,MAAA,gBACvB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACNH,EAAA,CAAAC,cAAA,eAA2D;QAECD,EAAA,CAAAqE,UAAA,mBAAAC,kDAAA;UAAA,OAASH,GAAA,CAAA1C,QAAA,EAAU;QAAA,EAAE;QAACzB,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAE9FH,EAAA,CAAAC,cAAA,WAAK;QACoDD,EAAA,CAAAqE,UAAA,mBAAAE,kDAAA;UAAA,OAASJ,GAAA,CAAAvC,WAAA,EAAa;QAAA,EAAE;QAAC5B,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEnGH,EAAA,CAAAC,cAAA,WAAK;QACkDD,EAAA,CAAAqE,UAAA,mBAAAG,kDAAA;UAAA,OAASL,GAAA,CAAAtC,SAAA,EAAW;QAAA,EAAE;QAAC7B,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAG/FH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpCH,EAAA,CAAAC,cAAA,gBAA2E;QAAxBD,EAAA,CAAAqE,UAAA,sBAAAI,mDAAA;UAAA,OAAYN,GAAA,CAAArC,QAAA,EAAU;QAAA,EAAC;QACxE9B,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAoE,SAAA,iBAA6C;QAC7CpE,EAAA,CAAAC,cAAA,oBAAmE;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClFH,EAAA,CAAA0E,UAAA,KAAAC,uCAAA,oBAIC;QACH3E,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAoE,SAAA,iBAA+E;QAC/EpE,EAAA,CAAAC,cAAA,oBAA0F;QAAvBD,EAAA,CAAAqE,UAAA,mBAAAO,oDAAA;UAAA,OAAAT,GAAA,CAAAnD,IAAA,IAAAmD,GAAA,CAAAnD,IAAA;QAAA,EAAsB;QACvFhB,EAAA,CAAAE,MAAA,IAA0C;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACvDH,EAAA,CAAA0E,UAAA,KAAAG,uCAAA,oBAIC;QACH7E,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAoE;QAG9DD,EAAA,CAAAoE,SAAA,iBAAyD;QAACpE,EAAA,CAAAE,MAAA,qBAC1D;QAAAF,EAAA,CAAAC,cAAA,gBAA8B;QAC5BD,EAAA,CAAAoE,SAAA,gBAA2B;QAC7BpE,EAAA,CAAAG,YAAA,EAAO;QAGXH,EAAA,CAAAC,cAAA,aAA6D;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEnFH,EAAA,CAAA0E,UAAA,KAAAI,uCAAA,kBAEC;QACD9E,EAAA,CAAAC,cAAA,eAAqC;QAGmCD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAIxFH,EAAA,CAAAC,cAAA,cAA+B;QAAAD,EAAA,CAAAE,MAAA,UAAE;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACtCH,EAAA,CAAAC,cAAA,cAAgD;QAE1CD,EAAA,CAAAoE,SAAA,aAA6B;QAC/BpE,EAAA,CAAAG,YAAA,EAAI;QACNH,EAAA,CAAAC,cAAA,cAA6B;QACzBD,EAAA,CAAAoE,SAAA,aAAiC;QACnCpE,EAAA,CAAAG,YAAA,EAAI;QACNH,EAAA,CAAAC,cAAA,cAA6B;QACzBD,EAAA,CAAAoE,SAAA,aAA8B;QAChCpE,EAAA,CAAAG,YAAA,EAAI;QACNH,EAAA,CAAAC,cAAA,cAA6B;QACzBD,EAAA,CAAAoE,SAAA,aAAkC;QACpCpE,EAAA,CAAAG,YAAA,EAAI;;;;;QAhEoBH,EAAA,CAAAI,SAAA,IAAsB;QAAtBJ,EAAA,CAAA+E,UAAA,cAAAZ,GAAA,CAAAjD,QAAA,CAAsB;QAO1ClB,EAAA,CAAAI,SAAA,GAIC;QAJDJ,EAAA,CAAAgF,aAAA,OAAAC,OAAA,GAAAd,GAAA,CAAAjD,QAAA,CAAAQ,GAAA,+BAAAuD,OAAA,CAAAC,QAAA,wBAIC;QAQelF,EAAA,CAAAI,SAAA,GAAmC;QAAnCJ,EAAA,CAAA+E,UAAA,SAAAZ,GAAA,CAAAnD,IAAA,uBAAmC;QAEjDhB,EAAA,CAAAI,SAAA,GAA0C;QAA1CJ,EAAA,CAAAmF,kBAAA,MAAAhB,GAAA,CAAAnD,IAAA,uCAA0C;QAC5ChB,EAAA,CAAAI,SAAA,EAIC;QAJDJ,EAAA,CAAAgF,aAAA,OAAAI,OAAA,GAAAjB,GAAA,CAAAjD,QAAA,CAAAQ,GAAA,+BAAA0D,OAAA,CAAAF,QAAA,wBAIC;QAePlF,EAAA,CAAAI,SAAA,IAEC;QAFDJ,EAAA,CAAAgF,aAAA,KAAAb,GAAA,CAAA5D,KAAA,WAEC;QAG6CP,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAqF,WAAA,iBAAAlB,GAAA,CAAApD,OAAA,CAA8B;QAACf,EAAA,CAAA+E,UAAA,aAAAZ,GAAA,CAAApD,OAAA,CAAoB,cAAAoD,GAAA,CAAAjD,QAAA,CAAAoE,KAAA;;;mBDxDrGhG,UAAU,EACVS,eAAe,EAAAwF,EAAA,CAAAC,SAAA,EACfhG,WAAW,EAAAwD,EAAA,CAAAyC,aAAA,EAAAzC,EAAA,CAAA0C,oBAAA,EAAA1C,EAAA,CAAA2C,eAAA,EAAA3C,EAAA,CAAA4C,oBAAA,EACXnG,mBAAmB,EAAAuD,EAAA,CAAA6C,kBAAA,EAAA7C,EAAA,CAAA8C,eAAA,EACnBhG,kBAAkB,EAAAiG,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBtG,cAAc,EAAAuG,EAAA,CAAAC,QAAA,EACdzG,aAAa,EAAA0G,EAAA,CAAAC,OAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}