{"ast": null, "code": "import { formatDate } from '@angular/common';\nexport class Tickets {\n  constructor(ticket) {\n    {\n      this.id = ticket.id || this.getRandomID();\n      this.ticket_id = ticket.ticket_id || '';\n      this.createdBy = ticket.createdBy || '';\n      this.subject = ticket.subject || '';\n      this.status = ticket.status || '';\n      this.assignTo = ticket.assignTo || '';\n      this.date = formatDate(new Date(), 'yyyy-MM-dd', 'en') || '';\n      this.details = ticket.details || '';\n    }\n  }\n  getRandomID() {\n    const S4 = () => {\n      return (1 + Math.random()) * 0x10000 | 0;\n    };\n    return S4() + S4();\n  }\n}", "map": {"version": 3, "names": ["formatDate", "Tickets", "constructor", "ticket", "id", "getRandomID", "ticket_id", "created<PERSON>y", "subject", "status", "assignTo", "date", "Date", "details", "S4", "Math", "random"], "sources": ["C:\\Users\\<USER>\\Desktop\\mian\\src\\app\\client\\supports\\tickets\\tickets.model.ts"], "sourcesContent": ["import { formatDate } from '@angular/common';\r\nexport class Tickets {\r\n  id: number;\r\n  ticket_id: string;\r\n  createdBy: string;\r\n  subject: string;\r\n  status: string;\r\n  assignTo: string;\r\n  date: string;\r\n  details: string;\r\n  constructor(ticket: Tickets) {\r\n    {\r\n      this.id = ticket.id || this.getRandomID();\r\n      this.ticket_id = ticket.ticket_id || '';\r\n      this.createdBy = ticket.createdBy || '';\r\n      this.subject = ticket.subject || '';\r\n      this.status = ticket.status || '';\r\n      this.assignTo = ticket.assignTo || '';\r\n      this.date = formatDate(new Date(), 'yyyy-MM-dd', 'en') || '';\r\n      this.details = ticket.details || '';\r\n    }\r\n  }\r\n  public getRandomID(): number {\r\n    const S4 = () => {\r\n      return ((1 + Math.random()) * 0x10000) | 0;\r\n    };\r\n    return S4() + S4();\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAM,MAAOC,OAAO;EASlBC,YAAYC,MAAe;IACzB;MACE,IAAI,CAACC,EAAE,GAAGD,MAAM,CAACC,EAAE,IAAI,IAAI,CAACC,WAAW,EAAE;MACzC,IAAI,CAACC,SAAS,GAAGH,MAAM,CAACG,SAAS,IAAI,EAAE;MACvC,IAAI,CAACC,SAAS,GAAGJ,MAAM,CAACI,SAAS,IAAI,EAAE;MACvC,IAAI,CAACC,OAAO,GAAGL,MAAM,CAACK,OAAO,IAAI,EAAE;MACnC,IAAI,CAACC,MAAM,GAAGN,MAAM,CAACM,MAAM,IAAI,EAAE;MACjC,IAAI,CAACC,QAAQ,GAAGP,MAAM,CAACO,QAAQ,IAAI,EAAE;MACrC,IAAI,CAACC,IAAI,GAAGX,UAAU,CAAC,IAAIY,IAAI,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC,IAAI,EAAE;MAC5D,IAAI,CAACC,OAAO,GAAGV,MAAM,CAACU,OAAO,IAAI,EAAE;;EAEvC;EACOR,WAAWA,CAAA;IAChB,MAAMS,EAAE,GAAGA,CAAA,KAAK;MACd,OAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,IAAI,OAAO,GAAI,CAAC;IAC5C,CAAC;IACD,OAAOF,EAAE,EAAE,GAAGA,EAAE,EAAE;EACpB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}