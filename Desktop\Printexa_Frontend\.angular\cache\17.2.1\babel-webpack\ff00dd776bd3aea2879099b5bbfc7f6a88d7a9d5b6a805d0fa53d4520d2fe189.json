{"ast": null, "code": "export function optional(f) {\n  return f == null ? null : required(f);\n}\nexport function required(f) {\n  if (typeof f !== \"function\") throw new Error();\n  return f;\n}", "map": {"version": 3, "names": ["optional", "f", "required", "Error"], "sources": ["C:/Users/<USER>/Desktop/mian/node_modules/d3-hierarchy/src/accessors.js"], "sourcesContent": ["export function optional(f) {\n  return f == null ? null : required(f);\n}\n\nexport function required(f) {\n  if (typeof f !== \"function\") throw new Error;\n  return f;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGC,QAAQ,CAACD,CAAC,CAAC;AACvC;AAEA,OAAO,SAASC,QAAQA,CAACD,CAAC,EAAE;EAC1B,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIE,KAAK,CAAD,CAAC;EAC5C,OAAOF,CAAC;AACV"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}