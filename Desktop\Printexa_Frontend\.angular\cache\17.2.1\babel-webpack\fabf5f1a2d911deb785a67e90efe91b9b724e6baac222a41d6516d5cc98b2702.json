{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../estimates.service\";\nimport * as i3 from \"@angular/material/button\";\nexport class DeleteDialogComponent {\n  constructor(dialogRef, data, estimatesService) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.estimatesService = estimatesService;\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmDelete() {\n    this.estimatesService.deleteEstimates(this.data.id);\n  }\n  static #_ = this.ɵfac = function DeleteDialogComponent_Factory(t) {\n    return new (t || DeleteDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.EstimatesService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DeleteDialogComponent,\n    selectors: [[\"app-delete\", 5, \"m\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 25,\n    vars: 4,\n    consts: [[1, \"container\"], [\"mat-dialog-title\", \"\"], [\"mat-dialog-content\", \"\"], [1, \"clearfix\"], [1, \"font-weight-bold\"], [\"mat-dialog-actions\", \"\", 1, \"mb-1\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", 3, \"mat-dialog-close\", \"click\"], [\"mat-flat-button\", \"\", \"tabindex\", \"-1\", 3, \"click\"]],\n    template: function DeleteDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1);\n        i0.ɵɵtext(2, \"Are you sure?\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 2)(4, \"ul\", 3)(5, \"li\")(6, \"p\")(7, \"span\", 4);\n        i0.ɵɵtext(8, \" Estimate ID: \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"li\")(11, \"p\")(12, \"span\", 4);\n        i0.ɵɵtext(13, \" Client Name: \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"li\")(16, \"p\")(17, \"span\", 4);\n        i0.ɵɵtext(18, \"Status: \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(20, \"div\", 5)(21, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function DeleteDialogComponent_Template_button_click_21_listener() {\n          return ctx.confirmDelete();\n        });\n        i0.ɵɵtext(22, \" Delete \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function DeleteDialogComponent_Template_button_click_23_listener() {\n          return ctx.onNoClick();\n        });\n        i0.ɵɵtext(24, \"Cancel\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.data.eNo);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.cName);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\"\", ctx.data.status, \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"mat-dialog-close\", 1);\n      }\n    },\n    dependencies: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, i3.MatButton, MatDialogClose],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "MatDialogClose", "MatButtonModule", "DeleteDialogComponent", "constructor", "dialogRef", "data", "estimatesService", "onNoClick", "close", "confirmDelete", "deleteEstimates", "id", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "EstimatesService", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DeleteDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DeleteDialogComponent_Template_button_click_21_listener", "DeleteDialogComponent_Template_button_click_23_listener", "ɵɵadvance", "ɵɵtextInterpolate", "eNo", "cName", "ɵɵtextInterpolate1", "status", "ɵɵproperty", "i3", "MatButton", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\projects\\estimates\\dialog\\delete\\delete.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\projects\\estimates\\dialog\\delete\\delete.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\r\nimport { Component, Inject } from '@angular/core';\r\nimport { EstimatesService } from '../../estimates.service';\r\nimport { MatButtonModule } from '@angular/material/button';\r\n\r\nexport interface DialogData {\r\n  id: number;\r\n  eNo: string;\r\n  cName: string;\r\n  status: string;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-delete:not(m)',\r\n    templateUrl: './delete.component.html',\r\n    styleUrls: ['./delete.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        MatDialogTitle,\r\n        MatDialogContent,\r\n        MatDialogActions,\r\n        MatButtonModule,\r\n        MatDialogClose,\r\n    ],\r\n})\r\nexport class DeleteDialogComponent {\r\n  constructor(\r\n    public dialogRef: MatDialogRef<DeleteDialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\r\n    public estimatesService: EstimatesService\r\n  ) {}\r\n  onNoClick(): void {\r\n    this.dialogRef.close();\r\n  }\r\n  confirmDelete(): void {\r\n    this.estimatesService.deleteEstimates(this.data.id);\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <h3 mat-dialog-title>Are you sure?</h3>\r\n  <div mat-dialog-content>\r\n    <ul class=\"clearfix\">\r\n      <li>\r\n        <p><span class=\"font-weight-bold\"> Estimate ID: </span>{{data.eNo}}</p>\r\n      </li>\r\n      <li>\r\n        <p><span class=\"font-weight-bold\"> Client Name: </span>{{ data.cName }}</p>\r\n      </li>\r\n      <li>\r\n        <p>\r\n          <span class=\"font-weight-bold\">Status: </span>{{data.status}}\r\n        </p>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n  <div mat-dialog-actions class=\"mb-1\">\r\n    <button mat-flat-button color=\"warn\" [mat-dialog-close]=\"1\" (click)=\"confirmDelete()\">\r\n      Delete\r\n    </button>\r\n    <button mat-flat-button (click)=\"onNoClick()\" tabindex=\"-1\">Cancel</button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,eAAe,EAAgBC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,0BAA0B;AAG5I,SAASC,eAAe,QAAQ,0BAA0B;;;;;AAsB1D,OAAM,MAAOC,qBAAqB;EAChCC,YACSC,SAA8C,EACrBC,IAAgB,EACzCC,gBAAkC;IAFlC,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC7B,KAAAC,gBAAgB,GAAhBA,gBAAgB;EACtB;EACHC,SAASA,CAAA;IACP,IAAI,CAACH,SAAS,CAACI,KAAK,EAAE;EACxB;EACAC,aAAaA,CAAA;IACX,IAAI,CAACH,gBAAgB,CAACI,eAAe,CAAC,IAAI,CAACL,IAAI,CAACM,EAAE,CAAC;EACrD;EAAC,QAAAC,CAAA,G;qBAXUV,qBAAqB,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAGtBlB,eAAe,GAAAiB,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAHdjB,qBAAqB;IAAAkB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAT,EAAA,CAAAU,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCzBlChB,EAAA,CAAAkB,cAAA,aAAuB;QACAlB,EAAA,CAAAmB,MAAA,oBAAa;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QACvCpB,EAAA,CAAAkB,cAAA,aAAwB;QAGiBlB,EAAA,CAAAmB,MAAA,qBAAa;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,GAAY;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAEzEpB,EAAA,CAAAkB,cAAA,UAAI;QACiClB,EAAA,CAAAmB,MAAA,sBAAa;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAgB;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAE7EpB,EAAA,CAAAkB,cAAA,UAAI;QAE+BlB,EAAA,CAAAmB,MAAA,gBAAQ;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAChD;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAIVpB,EAAA,CAAAkB,cAAA,cAAqC;QACyBlB,EAAA,CAAAqB,UAAA,mBAAAC,wDAAA;UAAA,OAASL,GAAA,CAAArB,aAAA,EAAe;QAAA,EAAC;QACnFI,EAAA,CAAAmB,MAAA,gBACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;QACTpB,EAAA,CAAAkB,cAAA,iBAA4D;QAApClB,EAAA,CAAAqB,UAAA,mBAAAE,wDAAA;UAAA,OAASN,GAAA,CAAAvB,SAAA,EAAW;QAAA,EAAC;QAAeM,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;;;QAhBhBpB,EAAA,CAAAwB,SAAA,GAAY;QAAZxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAAzB,IAAA,CAAAkC,GAAA,CAAY;QAGZ1B,EAAA,CAAAwB,SAAA,GAAgB;QAAhBxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAAzB,IAAA,CAAAmC,KAAA,CAAgB;QAIvB3B,EAAA,CAAAwB,SAAA,GAChD;QADgDxB,EAAA,CAAA4B,kBAAA,KAAAX,GAAA,CAAAzB,IAAA,CAAAqC,MAAA,MAChD;QAKiC7B,EAAA,CAAAwB,SAAA,GAAsB;QAAtBxB,EAAA,CAAA8B,UAAA,uBAAsB;;;mBDAvD9C,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBE,eAAe,EAAA2C,EAAA,CAAAC,SAAA,EACf7C,cAAc;IAAA8C,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}