{"ast": null, "code": "export default function () {\n  var leaves = [];\n  this.eachBefore(function (node) {\n    if (!node.children) {\n      leaves.push(node);\n    }\n  });\n  return leaves;\n}", "map": {"version": 3, "names": ["leaves", "eachBefore", "node", "children", "push"], "sources": ["C:/Users/<USER>/mian/node_modules/d3-hierarchy/src/hierarchy/leaves.js"], "sourcesContent": ["export default function() {\n  var leaves = [];\n  this.eachBefore(function(node) {\n    if (!node.children) {\n      leaves.push(node);\n    }\n  });\n  return leaves;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EACxB,IAAIA,MAAM,GAAG,EAAE;EACf,IAAI,CAACC,UAAU,CAAC,UAASC,IAAI,EAAE;IAC7B,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;MAClBH,MAAM,CAACI,IAAI,CAACF,IAAI,CAAC;IACnB;EACF,CAAC,CAAC;EACF,OAAOF,MAAM;AACf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}