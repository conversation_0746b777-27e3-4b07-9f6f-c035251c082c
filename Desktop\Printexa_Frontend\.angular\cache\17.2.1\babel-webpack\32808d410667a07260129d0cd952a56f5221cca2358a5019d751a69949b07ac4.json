{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FormProduitComponent {\n  static #_ = this.ɵfac = function FormProduitComponent_Factory(t) {\n    return new (t || FormProduitComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FormProduitComponent,\n    selectors: [[\"app-form-produit\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 2,\n    vars: 0,\n    template: function FormProduitComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \"form-produit works!\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["FormProduitComponent", "_", "_2", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "FormProduitComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\form-produit\\form-produit.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\form-produit\\form-produit.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-form-produit',\r\n  standalone: true,\r\n  imports: [],\r\n  templateUrl: './form-produit.component.html',\r\n  styleUrl: './form-produit.component.scss'\r\n})\r\nexport class FormProduitComponent {\r\n\r\n}\r\n", "<p>form-produit works!</p>\r\n"], "mappings": ";AASA,OAAM,MAAOA,oBAAoB;EAAA,QAAAC,CAAA,G;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA,G;UAApBF,oBAAoB;IAAAG,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTjCN,EAAA,CAAAQ,cAAA,QAAG;QAAAR,EAAA,CAAAS,MAAA,0BAAmB;QAAAT,EAAA,CAAAU,YAAA,EAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}