import { Routes } from "@angular/router";
import { ProduitComponent } from './produit/produit.component';
import { FormProduitComponent } from "./form-produit/form-produit.component";
import { ProduitDeleteComponent } from "./produit-delete/produit-delete.component";

export const PRODUIT_ROUTES: Routes = [
  { 
    path: '', 
    children: [
      { path: 'list', component: ProduitComponent },
      { path: 'add', component: FormProduitComponent },
      { path: 'edit/:id', component: FormProduitComponent },
      { path: 'delete/:id', component: ProduitDeleteComponent },
      { path: '**', redirectTo: 'list' }
    ]
  }
];