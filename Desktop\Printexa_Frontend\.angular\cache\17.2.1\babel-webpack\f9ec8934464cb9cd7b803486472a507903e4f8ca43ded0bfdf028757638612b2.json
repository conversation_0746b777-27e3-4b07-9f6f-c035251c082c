{"ast": null, "code": "import BoundingRect from '../core/BoundingRect.js';\nimport LRU from '../core/LRU.js';\nimport { DEFAULT_FONT, platformApi } from '../core/platform.js';\nvar textWidthCache = {};\nexport function getWidth(text, font) {\n  font = font || DEFAULT_FONT;\n  var cacheOfFont = textWidthCache[font];\n  if (!cacheOfFont) {\n    cacheOfFont = textWidthCache[font] = new LRU(500);\n  }\n  var width = cacheOfFont.get(text);\n  if (width == null) {\n    width = platformApi.measureText(text, font).width;\n    cacheOfFont.put(text, width);\n  }\n  return width;\n}\nexport function innerGetBoundingRect(text, font, textAlign, textBaseline) {\n  var width = getWidth(text, font);\n  var height = getLineHeight(font);\n  var x = adjustTextX(0, width, textAlign);\n  var y = adjustTextY(0, height, textBaseline);\n  var rect = new BoundingRect(x, y, width, height);\n  return rect;\n}\nexport function getBoundingRect(text, font, textAlign, textBaseline) {\n  var textLines = ((text || '') + '').split('\\n');\n  var len = textLines.length;\n  if (len === 1) {\n    return innerGetBoundingRect(textLines[0], font, textAlign, textBaseline);\n  } else {\n    var uniondRect = new BoundingRect(0, 0, 0, 0);\n    for (var i = 0; i < textLines.length; i++) {\n      var rect = innerGetBoundingRect(textLines[i], font, textAlign, textBaseline);\n      i === 0 ? uniondRect.copy(rect) : uniondRect.union(rect);\n    }\n    return uniondRect;\n  }\n}\nexport function adjustTextX(x, width, textAlign) {\n  if (textAlign === 'right') {\n    x -= width;\n  } else if (textAlign === 'center') {\n    x -= width / 2;\n  }\n  return x;\n}\nexport function adjustTextY(y, height, verticalAlign) {\n  if (verticalAlign === 'middle') {\n    y -= height / 2;\n  } else if (verticalAlign === 'bottom') {\n    y -= height;\n  }\n  return y;\n}\nexport function getLineHeight(font) {\n  return getWidth('国', font);\n}\nexport function measureText(text, font) {\n  return platformApi.measureText(text, font);\n}\nexport function parsePercent(value, maxValue) {\n  if (typeof value === 'string') {\n    if (value.lastIndexOf('%') >= 0) {\n      return parseFloat(value) / 100 * maxValue;\n    }\n    return parseFloat(value);\n  }\n  return value;\n}\nexport function calculateTextPosition(out, opts, rect) {\n  var textPosition = opts.position || 'inside';\n  var distance = opts.distance != null ? opts.distance : 5;\n  var height = rect.height;\n  var width = rect.width;\n  var halfHeight = height / 2;\n  var x = rect.x;\n  var y = rect.y;\n  var textAlign = 'left';\n  var textVerticalAlign = 'top';\n  if (textPosition instanceof Array) {\n    x += parsePercent(textPosition[0], rect.width);\n    y += parsePercent(textPosition[1], rect.height);\n    textAlign = null;\n    textVerticalAlign = null;\n  } else {\n    switch (textPosition) {\n      case 'left':\n        x -= distance;\n        y += halfHeight;\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'right':\n        x += distance + width;\n        y += halfHeight;\n        textVerticalAlign = 'middle';\n        break;\n      case 'top':\n        x += width / 2;\n        y -= distance;\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'bottom':\n        x += width / 2;\n        y += height + distance;\n        textAlign = 'center';\n        break;\n      case 'inside':\n        x += width / 2;\n        y += halfHeight;\n        textAlign = 'center';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideLeft':\n        x += distance;\n        y += halfHeight;\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideRight':\n        x += width - distance;\n        y += halfHeight;\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideTop':\n        x += width / 2;\n        y += distance;\n        textAlign = 'center';\n        break;\n      case 'insideBottom':\n        x += width / 2;\n        y += height - distance;\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideTopLeft':\n        x += distance;\n        y += distance;\n        break;\n      case 'insideTopRight':\n        x += width - distance;\n        y += distance;\n        textAlign = 'right';\n        break;\n      case 'insideBottomLeft':\n        x += distance;\n        y += height - distance;\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideBottomRight':\n        x += width - distance;\n        y += height - distance;\n        textAlign = 'right';\n        textVerticalAlign = 'bottom';\n        break;\n    }\n  }\n  out = out || {};\n  out.x = x;\n  out.y = y;\n  out.align = textAlign;\n  out.verticalAlign = textVerticalAlign;\n  return out;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}