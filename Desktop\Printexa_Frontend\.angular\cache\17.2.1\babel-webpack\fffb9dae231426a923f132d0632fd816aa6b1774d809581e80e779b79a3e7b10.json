{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { DatePipe } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../tickets.service\";\nimport * as i3 from \"@angular/material/button\";\nexport let DeleteDialogComponent = /*#__PURE__*/(() => {\n  class DeleteDialogComponent {\n    constructor(dialogRef, data, ticketsService) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.ticketsService = ticketsService;\n    }\n    onNoClick() {\n      this.dialogRef.close();\n    }\n    confirmDelete() {\n      this.ticketsService.deleteTicket(this.data.id);\n    }\n    static #_ = this.ɵfac = function DeleteDialogComponent_Factory(t) {\n      return new (t || DeleteDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.TicketsService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DeleteDialogComponent,\n      selectors: [[\"app-delete\", 5, \"o\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 26,\n      vars: 7,\n      consts: [[1, \"container\"], [\"mat-dialog-title\", \"\"], [\"mat-dialog-content\", \"\"], [1, \"clearfix\"], [1, \"font-weight-bold\"], [\"mat-dialog-actions\", \"\", 1, \"mb-1\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", 3, \"mat-dialog-close\", \"click\"], [\"mat-flat-button\", \"\", \"tabindex\", \"-1\", 3, \"click\"]],\n      template: function DeleteDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2, \"Are you sure?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"ul\", 3)(5, \"li\")(6, \"p\")(7, \"span\", 4);\n          i0.ɵɵtext(8, \" Ticket ID: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"li\")(11, \"p\")(12, \"span\", 4);\n          i0.ɵɵtext(13, \" Date: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\")(17, \"p\")(18, \"span\", 4);\n          i0.ɵɵtext(19, \"Created By: \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 5)(22, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function DeleteDialogComponent_Template_button_click_22_listener() {\n            return ctx.confirmDelete();\n          });\n          i0.ɵɵtext(23, \" Delete \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function DeleteDialogComponent_Template_button_click_24_listener() {\n            return ctx.onNoClick();\n          });\n          i0.ɵɵtext(25, \"Cancel\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.data.ticket_id);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 4, ctx.data.date, \"MM/dd/yyyy\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\"\", ctx.data.createdBy, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"mat-dialog-close\", 1);\n        }\n      },\n      dependencies: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, i3.MatButton, MatDialogClose, DatePipe]\n    });\n  }\n  return DeleteDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}