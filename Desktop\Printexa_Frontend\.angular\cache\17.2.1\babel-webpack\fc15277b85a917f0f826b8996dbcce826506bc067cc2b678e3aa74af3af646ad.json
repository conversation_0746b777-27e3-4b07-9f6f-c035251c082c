{"ast": null, "code": "/**\n * The function whose prototype chain sequence wrappers inherit from.\n *\n * @private\n */\nfunction baseLodash() {\n  // No operation performed.\n}\nexport default baseLodash;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/mian/node_modules/lodash-es/_baseLodash.js"], "sourcesContent": ["/**\n * The function whose prototype chain sequence wrappers inherit from.\n *\n * @private\n */\nfunction baseLodash() {\n  // No operation performed.\n}\n\nexport default baseLodash;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAAA,EAAG;EACpB;AAAA;AAGF,eAAeA,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}