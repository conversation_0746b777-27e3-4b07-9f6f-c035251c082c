{"ast": null, "code": "import interval from \"./interval.js\";\nvar utcYear = interval(function (date) {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, function (start, end) {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, function (date) {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = function (k) {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : interval(function (date) {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, function (date, step) {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\nexport default utcYear;\nexport var utcYears = utcYear.range;", "map": {"version": 3, "names": ["interval", "utcYear", "date", "setUTCMonth", "setUTCHours", "step", "setUTCFullYear", "getUTCFullYear", "start", "end", "every", "k", "isFinite", "Math", "floor", "utcYears", "range"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/d3-time-format/node_modules/d3-time/src/utcYear.js"], "sourcesContent": ["import interval from \"./interval.js\";\n\nvar utcYear = interval(function(date) {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, function(date, step) {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, function(start, end) {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, function(date) {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = function(k) {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : interval(function(date) {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, function(date, step) {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\n\nexport default utcYear;\nexport var utcYears = utcYear.range;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,IAAIC,OAAO,GAAGD,QAAQ,CAAC,UAASE,IAAI,EAAE;EACpCA,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;EACtBD,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC,EAAE,UAASF,IAAI,EAAEG,IAAI,EAAE;EACtBH,IAAI,CAACI,cAAc,CAACJ,IAAI,CAACK,cAAc,CAAC,CAAC,GAAGF,IAAI,CAAC;AACnD,CAAC,EAAE,UAASG,KAAK,EAAEC,GAAG,EAAE;EACtB,OAAOA,GAAG,CAACF,cAAc,CAAC,CAAC,GAAGC,KAAK,CAACD,cAAc,CAAC,CAAC;AACtD,CAAC,EAAE,UAASL,IAAI,EAAE;EAChB,OAAOA,IAAI,CAACK,cAAc,CAAC,CAAC;AAC9B,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACS,KAAK,GAAG,UAASC,CAAC,EAAE;EAC1B,OAAO,CAACC,QAAQ,CAACD,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,CAAC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAGX,QAAQ,CAAC,UAASE,IAAI,EAAE;IAC/EA,IAAI,CAACI,cAAc,CAACO,IAAI,CAACC,KAAK,CAACZ,IAAI,CAACK,cAAc,CAAC,CAAC,GAAGI,CAAC,CAAC,GAAGA,CAAC,CAAC;IAC9DT,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;IACtBD,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,CAAC,EAAE,UAASF,IAAI,EAAEG,IAAI,EAAE;IACtBH,IAAI,CAACI,cAAc,CAACJ,IAAI,CAACK,cAAc,CAAC,CAAC,GAAGF,IAAI,GAAGM,CAAC,CAAC;EACvD,CAAC,CAAC;AACJ,CAAC;AAED,eAAeV,OAAO;AACtB,OAAO,IAAIc,QAAQ,GAAGd,OAAO,CAACe,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}