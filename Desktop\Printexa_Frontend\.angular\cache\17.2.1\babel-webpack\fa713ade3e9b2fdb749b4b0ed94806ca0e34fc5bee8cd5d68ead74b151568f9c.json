{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class EstimatesService extends UnsubscribeOnDestroyAdapter {\n  constructor(httpClient) {\n    super();\n    this.httpClient = httpClient;\n    this.API_URL = 'assets/data/estimates.json';\n    this.isTblLoading = true;\n    this.dataChange = new BehaviorSubject([]);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  /** CRUD METHODS */\n  getAllEstimatess() {\n    this.subs.sink = this.httpClient.get(this.API_URL).subscribe({\n      next: data => {\n        this.isTblLoading = false;\n        this.dataChange.next(data);\n      },\n      error: error => {\n        this.isTblLoading = false;\n        console.log(error.name + ' ' + error.message);\n      }\n    });\n  }\n  addEstimates(estimates) {\n    this.dialogData = estimates;\n    // this.httpClient.post(this.API_URL, estimates)\n    //   .subscribe({\n    //     next: (data) => {\n    //       this.dialogData = estimates;\n    //     },\n    //     error: (error: HttpErrorResponse) => {\n    //        // error code here\n    //     },\n    //   });\n  }\n  updateEstimates(estimates) {\n    this.dialogData = estimates;\n    // this.httpClient.put(this.API_URL + estimates.id, estimates)\n    //     .subscribe({\n    //       next: (data) => {\n    //         this.dialogData = estimates;\n    //       },\n    //       error: (error: HttpErrorResponse) => {\n    //          // error code here\n    //       },\n    //     });\n  }\n  deleteEstimates(id) {\n    console.log(id);\n    // this.httpClient.delete(this.API_URL + id)\n    //     .subscribe({\n    //       next: (data) => {\n    //         console.log(id);\n    //       },\n    //       error: (error: HttpErrorResponse) => {\n    //          // error code here\n    //       },\n    //     });\n  }\n  static #_ = this.ɵfac = function EstimatesService_Factory(t) {\n    return new (t || EstimatesService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: EstimatesService,\n    factory: EstimatesService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "UnsubscribeOnDestroyAdapter", "EstimatesService", "constructor", "httpClient", "API_URL", "isTblLoading", "dataChange", "data", "value", "getDialogData", "dialogData", "getAllEstimatess", "subs", "sink", "get", "subscribe", "next", "error", "console", "log", "name", "message", "addEstimates", "estimates", "updateEstimates", "deleteEstimates", "id", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\projects\\estimates\\estimates.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { Estimates } from './estimates.model';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class EstimatesService extends UnsubscribeOnDestroyAdapter {\r\n  private readonly API_URL = 'assets/data/estimates.json';\r\n  isTblLoading = true;\r\n  dataChange: BehaviorSubject<Estimates[]> = new BehaviorSubject<Estimates[]>(\r\n    []\r\n  );\r\n  // Temporarily stores data from dialogs\r\n  dialogData!: Estimates;\r\n  constructor(private httpClient: HttpClient) {\r\n    super();\r\n  }\r\n  get data(): Estimates[] {\r\n    return this.dataChange.value;\r\n  }\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n  /** CRUD METHODS */\r\n  getAllEstimatess(): void {\r\n    this.subs.sink = this.httpClient.get<Estimates[]>(this.API_URL).subscribe({\r\n      next: (data) => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(data);\r\n      },\r\n      error: (error: HttpErrorResponse) => {\r\n        this.isTblLoading = false;\r\n        console.log(error.name + ' ' + error.message);\r\n      },\r\n    });\r\n  }\r\n  addEstimates(estimates: Estimates): void {\r\n    this.dialogData = estimates;\r\n\r\n    // this.httpClient.post(this.API_URL, estimates)\r\n    //   .subscribe({\r\n    //     next: (data) => {\r\n    //       this.dialogData = estimates;\r\n    //     },\r\n    //     error: (error: HttpErrorResponse) => {\r\n    //        // error code here\r\n    //     },\r\n    //   });\r\n  }\r\n  updateEstimates(estimates: Estimates): void {\r\n    this.dialogData = estimates;\r\n\r\n    // this.httpClient.put(this.API_URL + estimates.id, estimates)\r\n    //     .subscribe({\r\n    //       next: (data) => {\r\n    //         this.dialogData = estimates;\r\n    //       },\r\n    //       error: (error: HttpErrorResponse) => {\r\n    //          // error code here\r\n    //       },\r\n    //     });\r\n  }\r\n  deleteEstimates(id: number): void {\r\n    console.log(id);\r\n\r\n    // this.httpClient.delete(this.API_URL + id)\r\n    //     .subscribe({\r\n    //       next: (data) => {\r\n    //         console.log(id);\r\n    //       },\r\n    //       error: (error: HttpErrorResponse) => {\r\n    //          // error code here\r\n    //       },\r\n    //     });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;AAGtC,SAASC,2BAA2B,QAAQ,SAAS;;;AAKrD,OAAM,MAAOC,gBAAiB,SAAQD,2BAA2B;EAQ/DE,YAAoBC,UAAsB;IACxC,KAAK,EAAE;IADW,KAAAA,UAAU,GAAVA,UAAU;IAPb,KAAAC,OAAO,GAAG,4BAA4B;IACvD,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,UAAU,GAAiC,IAAIP,eAAe,CAC5D,EAAE,CACH;EAKD;EACA,IAAIQ,IAAIA,CAAA;IACN,OAAO,IAAI,CAACD,UAAU,CAACE,KAAK;EAC9B;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EACA;EACAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACV,UAAU,CAACW,GAAG,CAAc,IAAI,CAACV,OAAO,CAAC,CAACW,SAAS,CAAC;MACxEC,IAAI,EAAGT,IAAI,IAAI;QACb,IAAI,CAACF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,UAAU,CAACU,IAAI,CAACT,IAAI,CAAC;MAC5B,CAAC;MACDU,KAAK,EAAGA,KAAwB,IAAI;QAClC,IAAI,CAACZ,YAAY,GAAG,KAAK;QACzBa,OAAO,CAACC,GAAG,CAACF,KAAK,CAACG,IAAI,GAAG,GAAG,GAAGH,KAAK,CAACI,OAAO,CAAC;MAC/C;KACD,CAAC;EACJ;EACAC,YAAYA,CAACC,SAAoB;IAC/B,IAAI,CAACb,UAAU,GAAGa,SAAS;IAE3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EACAC,eAAeA,CAACD,SAAoB;IAClC,IAAI,CAACb,UAAU,GAAGa,SAAS;IAE3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EACAE,eAAeA,CAACC,EAAU;IACxBR,OAAO,CAACC,GAAG,CAACO,EAAE,CAAC;IAEf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAAC,QAAAC,CAAA,G;qBApEU1B,gBAAgB,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhB/B,gBAAgB;IAAAgC,OAAA,EAAhBhC,gBAAgB,CAAAiC,IAAA;IAAAC,UAAA,EAFf;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}