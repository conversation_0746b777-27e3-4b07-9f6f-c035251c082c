{"ast": null, "code": "export default function () {\n  var node = this,\n    nodes = [node];\n  while (node = node.parent) {\n    nodes.push(node);\n  }\n  return nodes;\n}", "map": {"version": 3, "names": ["node", "nodes", "parent", "push"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/d3-hierarchy/src/hierarchy/ancestors.js"], "sourcesContent": ["export default function() {\n  var node = this, nodes = [node];\n  while (node = node.parent) {\n    nodes.push(node);\n  }\n  return nodes;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EACxB,IAAIA,IAAI,GAAG,IAAI;IAAEC,KAAK,GAAG,CAACD,IAAI,CAAC;EAC/B,OAAOA,IAAI,GAAGA,IAAI,CAACE,MAAM,EAAE;IACzBD,KAAK,CAACE,IAAI,CAACH,IAAI,CAAC;EAClB;EACA,OAAOC,KAAK;AACd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}