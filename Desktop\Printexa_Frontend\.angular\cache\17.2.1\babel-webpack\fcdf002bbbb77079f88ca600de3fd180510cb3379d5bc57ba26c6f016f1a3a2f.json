{"ast": null, "code": "import { ProfileComponent } from \"./profile/profile.component\";\nimport { PricingComponent } from \"./pricing/pricing.component\";\nimport { InvoiceComponent } from \"./invoice/invoice.component\";\nimport { FaqsComponent } from \"./faqs/faqs.component\";\nimport { BlankComponent } from \"./blank/blank.component\";\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\nexport const EXTRA_PAGES_ROUTE = [{\n  path: \"profile\",\n  component: ProfileComponent\n}, {\n  path: \"pricing\",\n  component: PricingComponent\n}, {\n  path: \"invoice\",\n  component: InvoiceComponent\n}, {\n  path: \"faqs\",\n  component: FaqsComponent\n}, {\n  path: \"blank\",\n  component: BlankComponent\n}, {\n  path: '**',\n  component: Page404Component\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}