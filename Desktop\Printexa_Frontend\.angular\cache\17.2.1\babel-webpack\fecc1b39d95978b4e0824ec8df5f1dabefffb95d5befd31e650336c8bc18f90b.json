{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../util/model.js';\nimport { makeLabelFormatter, getOptionCategoryInterval, shouldShowAllLabels } from './axisHelper.js';\nvar inner = makeInner();\nexport function createAxisLabels(axis) {\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryLabels(axis) : makeRealNumberLabels(axis);\n}\n/**\n * @param {module:echats/coord/Axis} axis\n * @param {module:echarts/model/Model} tickModel For example, can be axisTick, splitLine, splitArea.\n * @return {Object} {\n *     ticks: Array.<number>\n *     tickCategoryInterval: number\n * }\n */\nexport function createAxisTicks(axis, tickModel) {\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryTicks(axis, tickModel) : {\n    ticks: zrUtil.map(axis.scale.getTicks(), function (tick) {\n      return tick.value;\n    })\n  };\n}\nfunction makeCategoryLabels(axis) {\n  var labelModel = axis.getLabelModel();\n  var result = makeCategoryLabelsActually(axis, labelModel);\n  return !labelModel.get('show') || axis.scale.isBlank() ? {\n    labels: [],\n    labelCategoryInterval: result.labelCategoryInterval\n  } : result;\n}\nfunction makeCategoryLabelsActually(axis, labelModel) {\n  var labelsCache = getListCache(axis, 'labels');\n  var optionLabelInterval = getOptionCategoryInterval(labelModel);\n  var result = listCacheGet(labelsCache, optionLabelInterval);\n  if (result) {\n    return result;\n  }\n  var labels;\n  var numericLabelInterval;\n  if (zrUtil.isFunction(optionLabelInterval)) {\n    labels = makeLabelsByCustomizedCategoryInterval(axis, optionLabelInterval);\n  } else {\n    numericLabelInterval = optionLabelInterval === 'auto' ? makeAutoCategoryInterval(axis) : optionLabelInterval;\n    labels = makeLabelsByNumericCategoryInterval(axis, numericLabelInterval);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(labelsCache, optionLabelInterval, {\n    labels: labels,\n    labelCategoryInterval: numericLabelInterval\n  });\n}\nfunction makeCategoryTicks(axis, tickModel) {\n  var ticksCache = getListCache(axis, 'ticks');\n  var optionTickInterval = getOptionCategoryInterval(tickModel);\n  var result = listCacheGet(ticksCache, optionTickInterval);\n  if (result) {\n    return result;\n  }\n  var ticks;\n  var tickCategoryInterval;\n  // Optimize for the case that large category data and no label displayed,\n  // we should not return all ticks.\n  if (!tickModel.get('show') || axis.scale.isBlank()) {\n    ticks = [];\n  }\n  if (zrUtil.isFunction(optionTickInterval)) {\n    ticks = makeLabelsByCustomizedCategoryInterval(axis, optionTickInterval, true);\n  }\n  // Always use label interval by default despite label show. Consider this\n  // scenario, Use multiple grid with the xAxis sync, and only one xAxis shows\n  // labels. `splitLine` and `axisTick` should be consistent in this case.\n  else if (optionTickInterval === 'auto') {\n    var labelsResult = makeCategoryLabelsActually(axis, axis.getLabelModel());\n    tickCategoryInterval = labelsResult.labelCategoryInterval;\n    ticks = zrUtil.map(labelsResult.labels, function (labelItem) {\n      return labelItem.tickValue;\n    });\n  } else {\n    tickCategoryInterval = optionTickInterval;\n    ticks = makeLabelsByNumericCategoryInterval(axis, tickCategoryInterval, true);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(ticksCache, optionTickInterval, {\n    ticks: ticks,\n    tickCategoryInterval: tickCategoryInterval\n  });\n}\nfunction makeRealNumberLabels(axis) {\n  var ticks = axis.scale.getTicks();\n  var labelFormatter = makeLabelFormatter(axis);\n  return {\n    labels: zrUtil.map(ticks, function (tick, idx) {\n      return {\n        level: tick.level,\n        formattedLabel: labelFormatter(tick, idx),\n        rawLabel: axis.scale.getLabel(tick),\n        tickValue: tick.value\n      };\n    })\n  };\n}\nfunction getListCache(axis, prop) {\n  // Because key can be a function, and cache size always is small, we use array cache.\n  return inner(axis)[prop] || (inner(axis)[prop] = []);\n}\nfunction listCacheGet(cache, key) {\n  for (var i = 0; i < cache.length; i++) {\n    if (cache[i].key === key) {\n      return cache[i].value;\n    }\n  }\n}\nfunction listCacheSet(cache, key, value) {\n  cache.push({\n    key: key,\n    value: value\n  });\n  return value;\n}\nfunction makeAutoCategoryInterval(axis) {\n  var result = inner(axis).autoInterval;\n  return result != null ? result : inner(axis).autoInterval = axis.calculateCategoryInterval();\n}\n/**\n * Calculate interval for category axis ticks and labels.\n * To get precise result, at least one of `getRotate` and `isHorizontal`\n * should be implemented in axis.\n */\nexport function calculateCategoryInterval(axis) {\n  var params = fetchAutoCategoryIntervalCalculationParams(axis);\n  var labelFormatter = makeLabelFormatter(axis);\n  var rotation = (params.axisRotate - params.labelRotate) / 180 * Math.PI;\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  // Providing this method is for optimization:\n  // avoid generating a long array by `getTicks`\n  // in large category data case.\n  var tickCount = ordinalScale.count();\n  if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n    return 0;\n  }\n  var step = 1;\n  // Simple optimization. Empirical value: tick count should less than 40.\n  if (tickCount > 40) {\n    step = Math.max(1, Math.floor(tickCount / 40));\n  }\n  var tickValue = ordinalExtent[0];\n  var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n  var unitW = Math.abs(unitSpan * Math.cos(rotation));\n  var unitH = Math.abs(unitSpan * Math.sin(rotation));\n  var maxW = 0;\n  var maxH = 0;\n  // Caution: Performance sensitive for large category data.\n  // Consider dataZoom, we should make appropriate step to avoid O(n) loop.\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    var width = 0;\n    var height = 0;\n    // Not precise, do not consider align and vertical align\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(labelFormatter({\n      value: tickValue\n    }), params.font, 'center', 'top');\n    // Magic number\n    width = rect.width * 1.3;\n    height = rect.height * 1.3;\n    // Min size, void long loop.\n    maxW = Math.max(maxW, width, 7);\n    maxH = Math.max(maxH, height, 7);\n  }\n  var dw = maxW / unitW;\n  var dh = maxH / unitH;\n  // 0/0 is NaN, 1/0 is Infinity.\n  isNaN(dw) && (dw = Infinity);\n  isNaN(dh) && (dh = Infinity);\n  var interval = Math.max(0, Math.floor(Math.min(dw, dh)));\n  var cache = inner(axis.model);\n  var axisExtent = axis.getExtent();\n  var lastAutoInterval = cache.lastAutoInterval;\n  var lastTickCount = cache.lastTickCount;\n  // Use cache to keep interval stable while moving zoom window,\n  // otherwise the calculated interval might jitter when the zoom\n  // window size is close to the interval-changing size.\n  // For example, if all of the axis labels are `a, b, c, d, e, f, g`.\n  // The jitter will cause that sometimes the displayed labels are\n  // `a, d, g` (interval: 2) sometimes `a, c, e`(interval: 1).\n  if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n  // Always choose the bigger one, otherwise the critical\n  // point is not the same when zooming in or zooming out.\n  && lastAutoInterval > interval\n  // If the axis change is caused by chart resize, the cache should not\n  // be used. Otherwise some hidden labels might not be shown again.\n  && cache.axisExtent0 === axisExtent[0] && cache.axisExtent1 === axisExtent[1]) {\n    interval = lastAutoInterval;\n  }\n  // Only update cache if cache not used, otherwise the\n  // changing of interval is too insensitive.\n  else {\n    cache.lastTickCount = tickCount;\n    cache.lastAutoInterval = interval;\n    cache.axisExtent0 = axisExtent[0];\n    cache.axisExtent1 = axisExtent[1];\n  }\n  return interval;\n}\nfunction fetchAutoCategoryIntervalCalculationParams(axis) {\n  var labelModel = axis.getLabelModel();\n  return {\n    axisRotate: axis.getRotate ? axis.getRotate() : axis.isHorizontal && !axis.isHorizontal() ? 90 : 0,\n    labelRotate: labelModel.get('rotate') || 0,\n    font: labelModel.getFont()\n  };\n}\nfunction makeLabelsByNumericCategoryInterval(axis, categoryInterval, onlyTick) {\n  var labelFormatter = makeLabelFormatter(axis);\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  var labelModel = axis.getLabelModel();\n  var result = [];\n  // TODO: axisType: ordinalTime, pick the tick from each month/day/year/...\n  var step = Math.max((categoryInterval || 0) + 1, 1);\n  var startTick = ordinalExtent[0];\n  var tickCount = ordinalScale.count();\n  // Calculate start tick based on zero if possible to keep label consistent\n  // while zooming and moving while interval > 0. Otherwise the selection\n  // of displayable ticks and symbols probably keep changing.\n  // 3 is empirical value.\n  if (startTick !== 0 && step > 1 && tickCount / step > 2) {\n    startTick = Math.round(Math.ceil(startTick / step) * step);\n  }\n  // (1) Only add min max label here but leave overlap checking\n  // to render stage, which also ensure the returned list\n  // suitable for splitLine and splitArea rendering.\n  // (2) Scales except category always contain min max label so\n  // do not need to perform this process.\n  var showAllLabel = shouldShowAllLabels(axis);\n  var includeMinLabel = labelModel.get('showMinLabel') || showAllLabel;\n  var includeMaxLabel = labelModel.get('showMaxLabel') || showAllLabel;\n  if (includeMinLabel && startTick !== ordinalExtent[0]) {\n    addItem(ordinalExtent[0]);\n  }\n  // Optimize: avoid generating large array by `ordinalScale.getTicks()`.\n  var tickValue = startTick;\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    addItem(tickValue);\n  }\n  if (includeMaxLabel && tickValue - step !== ordinalExtent[1]) {\n    addItem(ordinalExtent[1]);\n  }\n  function addItem(tickValue) {\n    var tickObj = {\n      value: tickValue\n    };\n    result.push(onlyTick ? tickValue : {\n      formattedLabel: labelFormatter(tickObj),\n      rawLabel: ordinalScale.getLabel(tickObj),\n      tickValue: tickValue\n    });\n  }\n  return result;\n}\nfunction makeLabelsByCustomizedCategoryInterval(axis, categoryInterval, onlyTick) {\n  var ordinalScale = axis.scale;\n  var labelFormatter = makeLabelFormatter(axis);\n  var result = [];\n  zrUtil.each(ordinalScale.getTicks(), function (tick) {\n    var rawLabel = ordinalScale.getLabel(tick);\n    var tickValue = tick.value;\n    if (categoryInterval(tick.value, rawLabel)) {\n      result.push(onlyTick ? tickValue : {\n        formattedLabel: labelFormatter(tick),\n        rawLabel: rawLabel,\n        tickValue: tickValue\n      });\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["zrUtil", "textContain", "makeInner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getOptionCategoryInterval", "shouldShowAllLabels", "inner", "createAxisLabels", "axis", "type", "makeCategoryLabels", "makeRealNumberLabels", "createAxisTicks", "tickModel", "makeCategoryTicks", "ticks", "map", "scale", "getTicks", "tick", "value", "labelModel", "getLabelModel", "result", "makeCategoryLabelsActually", "get", "isBlank", "labels", "labelCategoryInterval", "labelsCache", "getListCache", "optionLabelInterval", "listCacheGet", "numericLabelInterval", "isFunction", "makeLabelsByCustomizedCategoryInterval", "makeAutoCategoryInterval", "makeLabelsByNumericCategoryInterval", "listCacheSet", "ticksCache", "optionTickInterval", "tickCategoryInterval", "labelsResult", "labelItem", "tickValue", "labelFormatter", "idx", "level", "formattedLabel", "rawLabel", "get<PERSON><PERSON><PERSON>", "prop", "cache", "key", "i", "length", "push", "autoInterval", "calculateCategoryInterval", "params", "fetchAutoCategoryIntervalCalculationParams", "rotation", "axisRotate", "labelRotate", "Math", "PI", "ordinalScale", "ordinalExtent", "getExtent", "tickCount", "count", "step", "max", "floor", "unitSpan", "dataToCoord", "unitW", "abs", "cos", "unitH", "sin", "maxW", "maxH", "width", "height", "rect", "getBoundingRect", "font", "dw", "dh", "isNaN", "Infinity", "interval", "min", "model", "axisExtent", "lastAutoInterval", "lastTickCount", "axisExtent0", "axisExtent1", "getRotate", "isHorizontal", "getFont", "categoryInterval", "onlyTick", "startTick", "round", "ceil", "showAllLabel", "includeMinLabel", "includeMaxLabel", "addItem", "tickObj", "each"], "sources": ["C:/Users/<USER>/mian/node_modules/echarts/lib/coord/axisTickLabelBuilder.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../util/model.js';\nimport { makeLabelFormatter, getOptionCategoryInterval, shouldShowAllLabels } from './axisHelper.js';\nvar inner = makeInner();\nexport function createAxisLabels(axis) {\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryLabels(axis) : makeRealNumberLabels(axis);\n}\n/**\n * @param {module:echats/coord/Axis} axis\n * @param {module:echarts/model/Model} tickModel For example, can be axisTick, splitLine, splitArea.\n * @return {Object} {\n *     ticks: Array.<number>\n *     tickCategoryInterval: number\n * }\n */\nexport function createAxisTicks(axis, tickModel) {\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryTicks(axis, tickModel) : {\n    ticks: zrUtil.map(axis.scale.getTicks(), function (tick) {\n      return tick.value;\n    })\n  };\n}\nfunction makeCategoryLabels(axis) {\n  var labelModel = axis.getLabelModel();\n  var result = makeCategoryLabelsActually(axis, labelModel);\n  return !labelModel.get('show') || axis.scale.isBlank() ? {\n    labels: [],\n    labelCategoryInterval: result.labelCategoryInterval\n  } : result;\n}\nfunction makeCategoryLabelsActually(axis, labelModel) {\n  var labelsCache = getListCache(axis, 'labels');\n  var optionLabelInterval = getOptionCategoryInterval(labelModel);\n  var result = listCacheGet(labelsCache, optionLabelInterval);\n  if (result) {\n    return result;\n  }\n  var labels;\n  var numericLabelInterval;\n  if (zrUtil.isFunction(optionLabelInterval)) {\n    labels = makeLabelsByCustomizedCategoryInterval(axis, optionLabelInterval);\n  } else {\n    numericLabelInterval = optionLabelInterval === 'auto' ? makeAutoCategoryInterval(axis) : optionLabelInterval;\n    labels = makeLabelsByNumericCategoryInterval(axis, numericLabelInterval);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(labelsCache, optionLabelInterval, {\n    labels: labels,\n    labelCategoryInterval: numericLabelInterval\n  });\n}\nfunction makeCategoryTicks(axis, tickModel) {\n  var ticksCache = getListCache(axis, 'ticks');\n  var optionTickInterval = getOptionCategoryInterval(tickModel);\n  var result = listCacheGet(ticksCache, optionTickInterval);\n  if (result) {\n    return result;\n  }\n  var ticks;\n  var tickCategoryInterval;\n  // Optimize for the case that large category data and no label displayed,\n  // we should not return all ticks.\n  if (!tickModel.get('show') || axis.scale.isBlank()) {\n    ticks = [];\n  }\n  if (zrUtil.isFunction(optionTickInterval)) {\n    ticks = makeLabelsByCustomizedCategoryInterval(axis, optionTickInterval, true);\n  }\n  // Always use label interval by default despite label show. Consider this\n  // scenario, Use multiple grid with the xAxis sync, and only one xAxis shows\n  // labels. `splitLine` and `axisTick` should be consistent in this case.\n  else if (optionTickInterval === 'auto') {\n    var labelsResult = makeCategoryLabelsActually(axis, axis.getLabelModel());\n    tickCategoryInterval = labelsResult.labelCategoryInterval;\n    ticks = zrUtil.map(labelsResult.labels, function (labelItem) {\n      return labelItem.tickValue;\n    });\n  } else {\n    tickCategoryInterval = optionTickInterval;\n    ticks = makeLabelsByNumericCategoryInterval(axis, tickCategoryInterval, true);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(ticksCache, optionTickInterval, {\n    ticks: ticks,\n    tickCategoryInterval: tickCategoryInterval\n  });\n}\nfunction makeRealNumberLabels(axis) {\n  var ticks = axis.scale.getTicks();\n  var labelFormatter = makeLabelFormatter(axis);\n  return {\n    labels: zrUtil.map(ticks, function (tick, idx) {\n      return {\n        level: tick.level,\n        formattedLabel: labelFormatter(tick, idx),\n        rawLabel: axis.scale.getLabel(tick),\n        tickValue: tick.value\n      };\n    })\n  };\n}\nfunction getListCache(axis, prop) {\n  // Because key can be a function, and cache size always is small, we use array cache.\n  return inner(axis)[prop] || (inner(axis)[prop] = []);\n}\nfunction listCacheGet(cache, key) {\n  for (var i = 0; i < cache.length; i++) {\n    if (cache[i].key === key) {\n      return cache[i].value;\n    }\n  }\n}\nfunction listCacheSet(cache, key, value) {\n  cache.push({\n    key: key,\n    value: value\n  });\n  return value;\n}\nfunction makeAutoCategoryInterval(axis) {\n  var result = inner(axis).autoInterval;\n  return result != null ? result : inner(axis).autoInterval = axis.calculateCategoryInterval();\n}\n/**\n * Calculate interval for category axis ticks and labels.\n * To get precise result, at least one of `getRotate` and `isHorizontal`\n * should be implemented in axis.\n */\nexport function calculateCategoryInterval(axis) {\n  var params = fetchAutoCategoryIntervalCalculationParams(axis);\n  var labelFormatter = makeLabelFormatter(axis);\n  var rotation = (params.axisRotate - params.labelRotate) / 180 * Math.PI;\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  // Providing this method is for optimization:\n  // avoid generating a long array by `getTicks`\n  // in large category data case.\n  var tickCount = ordinalScale.count();\n  if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n    return 0;\n  }\n  var step = 1;\n  // Simple optimization. Empirical value: tick count should less than 40.\n  if (tickCount > 40) {\n    step = Math.max(1, Math.floor(tickCount / 40));\n  }\n  var tickValue = ordinalExtent[0];\n  var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n  var unitW = Math.abs(unitSpan * Math.cos(rotation));\n  var unitH = Math.abs(unitSpan * Math.sin(rotation));\n  var maxW = 0;\n  var maxH = 0;\n  // Caution: Performance sensitive for large category data.\n  // Consider dataZoom, we should make appropriate step to avoid O(n) loop.\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    var width = 0;\n    var height = 0;\n    // Not precise, do not consider align and vertical align\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(labelFormatter({\n      value: tickValue\n    }), params.font, 'center', 'top');\n    // Magic number\n    width = rect.width * 1.3;\n    height = rect.height * 1.3;\n    // Min size, void long loop.\n    maxW = Math.max(maxW, width, 7);\n    maxH = Math.max(maxH, height, 7);\n  }\n  var dw = maxW / unitW;\n  var dh = maxH / unitH;\n  // 0/0 is NaN, 1/0 is Infinity.\n  isNaN(dw) && (dw = Infinity);\n  isNaN(dh) && (dh = Infinity);\n  var interval = Math.max(0, Math.floor(Math.min(dw, dh)));\n  var cache = inner(axis.model);\n  var axisExtent = axis.getExtent();\n  var lastAutoInterval = cache.lastAutoInterval;\n  var lastTickCount = cache.lastTickCount;\n  // Use cache to keep interval stable while moving zoom window,\n  // otherwise the calculated interval might jitter when the zoom\n  // window size is close to the interval-changing size.\n  // For example, if all of the axis labels are `a, b, c, d, e, f, g`.\n  // The jitter will cause that sometimes the displayed labels are\n  // `a, d, g` (interval: 2) sometimes `a, c, e`(interval: 1).\n  if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n  // Always choose the bigger one, otherwise the critical\n  // point is not the same when zooming in or zooming out.\n  && lastAutoInterval > interval\n  // If the axis change is caused by chart resize, the cache should not\n  // be used. Otherwise some hidden labels might not be shown again.\n  && cache.axisExtent0 === axisExtent[0] && cache.axisExtent1 === axisExtent[1]) {\n    interval = lastAutoInterval;\n  }\n  // Only update cache if cache not used, otherwise the\n  // changing of interval is too insensitive.\n  else {\n    cache.lastTickCount = tickCount;\n    cache.lastAutoInterval = interval;\n    cache.axisExtent0 = axisExtent[0];\n    cache.axisExtent1 = axisExtent[1];\n  }\n  return interval;\n}\nfunction fetchAutoCategoryIntervalCalculationParams(axis) {\n  var labelModel = axis.getLabelModel();\n  return {\n    axisRotate: axis.getRotate ? axis.getRotate() : axis.isHorizontal && !axis.isHorizontal() ? 90 : 0,\n    labelRotate: labelModel.get('rotate') || 0,\n    font: labelModel.getFont()\n  };\n}\nfunction makeLabelsByNumericCategoryInterval(axis, categoryInterval, onlyTick) {\n  var labelFormatter = makeLabelFormatter(axis);\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  var labelModel = axis.getLabelModel();\n  var result = [];\n  // TODO: axisType: ordinalTime, pick the tick from each month/day/year/...\n  var step = Math.max((categoryInterval || 0) + 1, 1);\n  var startTick = ordinalExtent[0];\n  var tickCount = ordinalScale.count();\n  // Calculate start tick based on zero if possible to keep label consistent\n  // while zooming and moving while interval > 0. Otherwise the selection\n  // of displayable ticks and symbols probably keep changing.\n  // 3 is empirical value.\n  if (startTick !== 0 && step > 1 && tickCount / step > 2) {\n    startTick = Math.round(Math.ceil(startTick / step) * step);\n  }\n  // (1) Only add min max label here but leave overlap checking\n  // to render stage, which also ensure the returned list\n  // suitable for splitLine and splitArea rendering.\n  // (2) Scales except category always contain min max label so\n  // do not need to perform this process.\n  var showAllLabel = shouldShowAllLabels(axis);\n  var includeMinLabel = labelModel.get('showMinLabel') || showAllLabel;\n  var includeMaxLabel = labelModel.get('showMaxLabel') || showAllLabel;\n  if (includeMinLabel && startTick !== ordinalExtent[0]) {\n    addItem(ordinalExtent[0]);\n  }\n  // Optimize: avoid generating large array by `ordinalScale.getTicks()`.\n  var tickValue = startTick;\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    addItem(tickValue);\n  }\n  if (includeMaxLabel && tickValue - step !== ordinalExtent[1]) {\n    addItem(ordinalExtent[1]);\n  }\n  function addItem(tickValue) {\n    var tickObj = {\n      value: tickValue\n    };\n    result.push(onlyTick ? tickValue : {\n      formattedLabel: labelFormatter(tickObj),\n      rawLabel: ordinalScale.getLabel(tickObj),\n      tickValue: tickValue\n    });\n  }\n  return result;\n}\nfunction makeLabelsByCustomizedCategoryInterval(axis, categoryInterval, onlyTick) {\n  var ordinalScale = axis.scale;\n  var labelFormatter = makeLabelFormatter(axis);\n  var result = [];\n  zrUtil.each(ordinalScale.getTicks(), function (tick) {\n    var rawLabel = ordinalScale.getLabel(tick);\n    var tickValue = tick.value;\n    if (categoryInterval(tick.value, rawLabel)) {\n      result.push(onlyTick ? tickValue : {\n        formattedLabel: labelFormatter(tick),\n        rawLabel: rawLabel,\n        tickValue: tickValue\n      });\n    }\n  });\n  return result;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,WAAW,MAAM,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,kBAAkB,EAAEC,yBAAyB,EAAEC,mBAAmB,QAAQ,iBAAiB;AACpG,IAAIC,KAAK,GAAGJ,SAAS,CAAC,CAAC;AACvB,OAAO,SAASK,gBAAgBA,CAACC,IAAI,EAAE;EACrC;EACA,OAAOA,IAAI,CAACC,IAAI,KAAK,UAAU,GAAGC,kBAAkB,CAACF,IAAI,CAAC,GAAGG,oBAAoB,CAACH,IAAI,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,eAAeA,CAACJ,IAAI,EAAEK,SAAS,EAAE;EAC/C;EACA,OAAOL,IAAI,CAACC,IAAI,KAAK,UAAU,GAAGK,iBAAiB,CAACN,IAAI,EAAEK,SAAS,CAAC,GAAG;IACrEE,KAAK,EAAEf,MAAM,CAACgB,GAAG,CAACR,IAAI,CAACS,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,UAAUC,IAAI,EAAE;MACvD,OAAOA,IAAI,CAACC,KAAK;IACnB,CAAC;EACH,CAAC;AACH;AACA,SAASV,kBAAkBA,CAACF,IAAI,EAAE;EAChC,IAAIa,UAAU,GAAGb,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,IAAIC,MAAM,GAAGC,0BAA0B,CAAChB,IAAI,EAAEa,UAAU,CAAC;EACzD,OAAO,CAACA,UAAU,CAACI,GAAG,CAAC,MAAM,CAAC,IAAIjB,IAAI,CAACS,KAAK,CAACS,OAAO,CAAC,CAAC,GAAG;IACvDC,MAAM,EAAE,EAAE;IACVC,qBAAqB,EAAEL,MAAM,CAACK;EAChC,CAAC,GAAGL,MAAM;AACZ;AACA,SAASC,0BAA0BA,CAAChB,IAAI,EAAEa,UAAU,EAAE;EACpD,IAAIQ,WAAW,GAAGC,YAAY,CAACtB,IAAI,EAAE,QAAQ,CAAC;EAC9C,IAAIuB,mBAAmB,GAAG3B,yBAAyB,CAACiB,UAAU,CAAC;EAC/D,IAAIE,MAAM,GAAGS,YAAY,CAACH,WAAW,EAAEE,mBAAmB,CAAC;EAC3D,IAAIR,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAII,MAAM;EACV,IAAIM,oBAAoB;EACxB,IAAIjC,MAAM,CAACkC,UAAU,CAACH,mBAAmB,CAAC,EAAE;IAC1CJ,MAAM,GAAGQ,sCAAsC,CAAC3B,IAAI,EAAEuB,mBAAmB,CAAC;EAC5E,CAAC,MAAM;IACLE,oBAAoB,GAAGF,mBAAmB,KAAK,MAAM,GAAGK,wBAAwB,CAAC5B,IAAI,CAAC,GAAGuB,mBAAmB;IAC5GJ,MAAM,GAAGU,mCAAmC,CAAC7B,IAAI,EAAEyB,oBAAoB,CAAC;EAC1E;EACA;EACA,OAAOK,YAAY,CAACT,WAAW,EAAEE,mBAAmB,EAAE;IACpDJ,MAAM,EAAEA,MAAM;IACdC,qBAAqB,EAAEK;EACzB,CAAC,CAAC;AACJ;AACA,SAASnB,iBAAiBA,CAACN,IAAI,EAAEK,SAAS,EAAE;EAC1C,IAAI0B,UAAU,GAAGT,YAAY,CAACtB,IAAI,EAAE,OAAO,CAAC;EAC5C,IAAIgC,kBAAkB,GAAGpC,yBAAyB,CAACS,SAAS,CAAC;EAC7D,IAAIU,MAAM,GAAGS,YAAY,CAACO,UAAU,EAAEC,kBAAkB,CAAC;EACzD,IAAIjB,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAIR,KAAK;EACT,IAAI0B,oBAAoB;EACxB;EACA;EACA,IAAI,CAAC5B,SAAS,CAACY,GAAG,CAAC,MAAM,CAAC,IAAIjB,IAAI,CAACS,KAAK,CAACS,OAAO,CAAC,CAAC,EAAE;IAClDX,KAAK,GAAG,EAAE;EACZ;EACA,IAAIf,MAAM,CAACkC,UAAU,CAACM,kBAAkB,CAAC,EAAE;IACzCzB,KAAK,GAAGoB,sCAAsC,CAAC3B,IAAI,EAAEgC,kBAAkB,EAAE,IAAI,CAAC;EAChF;EACA;EACA;EACA;EAAA,KACK,IAAIA,kBAAkB,KAAK,MAAM,EAAE;IACtC,IAAIE,YAAY,GAAGlB,0BAA0B,CAAChB,IAAI,EAAEA,IAAI,CAACc,aAAa,CAAC,CAAC,CAAC;IACzEmB,oBAAoB,GAAGC,YAAY,CAACd,qBAAqB;IACzDb,KAAK,GAAGf,MAAM,CAACgB,GAAG,CAAC0B,YAAY,CAACf,MAAM,EAAE,UAAUgB,SAAS,EAAE;MAC3D,OAAOA,SAAS,CAACC,SAAS;IAC5B,CAAC,CAAC;EACJ,CAAC,MAAM;IACLH,oBAAoB,GAAGD,kBAAkB;IACzCzB,KAAK,GAAGsB,mCAAmC,CAAC7B,IAAI,EAAEiC,oBAAoB,EAAE,IAAI,CAAC;EAC/E;EACA;EACA,OAAOH,YAAY,CAACC,UAAU,EAAEC,kBAAkB,EAAE;IAClDzB,KAAK,EAAEA,KAAK;IACZ0B,oBAAoB,EAAEA;EACxB,CAAC,CAAC;AACJ;AACA,SAAS9B,oBAAoBA,CAACH,IAAI,EAAE;EAClC,IAAIO,KAAK,GAAGP,IAAI,CAACS,KAAK,CAACC,QAAQ,CAAC,CAAC;EACjC,IAAI2B,cAAc,GAAG1C,kBAAkB,CAACK,IAAI,CAAC;EAC7C,OAAO;IACLmB,MAAM,EAAE3B,MAAM,CAACgB,GAAG,CAACD,KAAK,EAAE,UAAUI,IAAI,EAAE2B,GAAG,EAAE;MAC7C,OAAO;QACLC,KAAK,EAAE5B,IAAI,CAAC4B,KAAK;QACjBC,cAAc,EAAEH,cAAc,CAAC1B,IAAI,EAAE2B,GAAG,CAAC;QACzCG,QAAQ,EAAEzC,IAAI,CAACS,KAAK,CAACiC,QAAQ,CAAC/B,IAAI,CAAC;QACnCyB,SAAS,EAAEzB,IAAI,CAACC;MAClB,CAAC;IACH,CAAC;EACH,CAAC;AACH;AACA,SAASU,YAAYA,CAACtB,IAAI,EAAE2C,IAAI,EAAE;EAChC;EACA,OAAO7C,KAAK,CAACE,IAAI,CAAC,CAAC2C,IAAI,CAAC,KAAK7C,KAAK,CAACE,IAAI,CAAC,CAAC2C,IAAI,CAAC,GAAG,EAAE,CAAC;AACtD;AACA,SAASnB,YAAYA,CAACoB,KAAK,EAAEC,GAAG,EAAE;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIF,KAAK,CAACE,CAAC,CAAC,CAACD,GAAG,KAAKA,GAAG,EAAE;MACxB,OAAOD,KAAK,CAACE,CAAC,CAAC,CAAClC,KAAK;IACvB;EACF;AACF;AACA,SAASkB,YAAYA,CAACc,KAAK,EAAEC,GAAG,EAAEjC,KAAK,EAAE;EACvCgC,KAAK,CAACI,IAAI,CAAC;IACTH,GAAG,EAAEA,GAAG;IACRjC,KAAK,EAAEA;EACT,CAAC,CAAC;EACF,OAAOA,KAAK;AACd;AACA,SAASgB,wBAAwBA,CAAC5B,IAAI,EAAE;EACtC,IAAIe,MAAM,GAAGjB,KAAK,CAACE,IAAI,CAAC,CAACiD,YAAY;EACrC,OAAOlC,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGjB,KAAK,CAACE,IAAI,CAAC,CAACiD,YAAY,GAAGjD,IAAI,CAACkD,yBAAyB,CAAC,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,yBAAyBA,CAAClD,IAAI,EAAE;EAC9C,IAAImD,MAAM,GAAGC,0CAA0C,CAACpD,IAAI,CAAC;EAC7D,IAAIqC,cAAc,GAAG1C,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAIqD,QAAQ,GAAG,CAACF,MAAM,CAACG,UAAU,GAAGH,MAAM,CAACI,WAAW,IAAI,GAAG,GAAGC,IAAI,CAACC,EAAE;EACvE,IAAIC,YAAY,GAAG1D,IAAI,CAACS,KAAK;EAC7B,IAAIkD,aAAa,GAAGD,YAAY,CAACE,SAAS,CAAC,CAAC;EAC5C;EACA;EACA;EACA,IAAIC,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC;EACpC,IAAIH,aAAa,CAAC,CAAC,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;IAC3C,OAAO,CAAC;EACV;EACA,IAAII,IAAI,GAAG,CAAC;EACZ;EACA,IAAIF,SAAS,GAAG,EAAE,EAAE;IAClBE,IAAI,GAAGP,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAER,IAAI,CAACS,KAAK,CAACJ,SAAS,GAAG,EAAE,CAAC,CAAC;EAChD;EACA,IAAIzB,SAAS,GAAGuB,aAAa,CAAC,CAAC,CAAC;EAChC,IAAIO,QAAQ,GAAGlE,IAAI,CAACmE,WAAW,CAAC/B,SAAS,GAAG,CAAC,CAAC,GAAGpC,IAAI,CAACmE,WAAW,CAAC/B,SAAS,CAAC;EAC5E,IAAIgC,KAAK,GAAGZ,IAAI,CAACa,GAAG,CAACH,QAAQ,GAAGV,IAAI,CAACc,GAAG,CAACjB,QAAQ,CAAC,CAAC;EACnD,IAAIkB,KAAK,GAAGf,IAAI,CAACa,GAAG,CAACH,QAAQ,GAAGV,IAAI,CAACgB,GAAG,CAACnB,QAAQ,CAAC,CAAC;EACnD,IAAIoB,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA;EACA,OAAOtC,SAAS,IAAIuB,aAAa,CAAC,CAAC,CAAC,EAAEvB,SAAS,IAAI2B,IAAI,EAAE;IACvD,IAAIY,KAAK,GAAG,CAAC;IACb,IAAIC,MAAM,GAAG,CAAC;IACd;IACA;IACA,IAAIC,IAAI,GAAGpF,WAAW,CAACqF,eAAe,CAACzC,cAAc,CAAC;MACpDzB,KAAK,EAAEwB;IACT,CAAC,CAAC,EAAEe,MAAM,CAAC4B,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC;IACjC;IACAJ,KAAK,GAAGE,IAAI,CAACF,KAAK,GAAG,GAAG;IACxBC,MAAM,GAAGC,IAAI,CAACD,MAAM,GAAG,GAAG;IAC1B;IACAH,IAAI,GAAGjB,IAAI,CAACQ,GAAG,CAACS,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;IAC/BD,IAAI,GAAGlB,IAAI,CAACQ,GAAG,CAACU,IAAI,EAAEE,MAAM,EAAE,CAAC,CAAC;EAClC;EACA,IAAII,EAAE,GAAGP,IAAI,GAAGL,KAAK;EACrB,IAAIa,EAAE,GAAGP,IAAI,GAAGH,KAAK;EACrB;EACAW,KAAK,CAACF,EAAE,CAAC,KAAKA,EAAE,GAAGG,QAAQ,CAAC;EAC5BD,KAAK,CAACD,EAAE,CAAC,KAAKA,EAAE,GAAGE,QAAQ,CAAC;EAC5B,IAAIC,QAAQ,GAAG5B,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAER,IAAI,CAACS,KAAK,CAACT,IAAI,CAAC6B,GAAG,CAACL,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;EACxD,IAAIrC,KAAK,GAAG9C,KAAK,CAACE,IAAI,CAACsF,KAAK,CAAC;EAC7B,IAAIC,UAAU,GAAGvF,IAAI,CAAC4D,SAAS,CAAC,CAAC;EACjC,IAAI4B,gBAAgB,GAAG5C,KAAK,CAAC4C,gBAAgB;EAC7C,IAAIC,aAAa,GAAG7C,KAAK,CAAC6C,aAAa;EACvC;EACA;EACA;EACA;EACA;EACA;EACA,IAAID,gBAAgB,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,IAAIjC,IAAI,CAACa,GAAG,CAACmB,gBAAgB,GAAGJ,QAAQ,CAAC,IAAI,CAAC,IAAI5B,IAAI,CAACa,GAAG,CAACoB,aAAa,GAAG5B,SAAS,CAAC,IAAI;EAC9I;EACA;EAAA,GACG2B,gBAAgB,GAAGJ;EACtB;EACA;EAAA,GACGxC,KAAK,CAAC8C,WAAW,KAAKH,UAAU,CAAC,CAAC,CAAC,IAAI3C,KAAK,CAAC+C,WAAW,KAAKJ,UAAU,CAAC,CAAC,CAAC,EAAE;IAC7EH,QAAQ,GAAGI,gBAAgB;EAC7B;EACA;EACA;EAAA,KACK;IACH5C,KAAK,CAAC6C,aAAa,GAAG5B,SAAS;IAC/BjB,KAAK,CAAC4C,gBAAgB,GAAGJ,QAAQ;IACjCxC,KAAK,CAAC8C,WAAW,GAAGH,UAAU,CAAC,CAAC,CAAC;IACjC3C,KAAK,CAAC+C,WAAW,GAAGJ,UAAU,CAAC,CAAC,CAAC;EACnC;EACA,OAAOH,QAAQ;AACjB;AACA,SAAShC,0CAA0CA,CAACpD,IAAI,EAAE;EACxD,IAAIa,UAAU,GAAGb,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,OAAO;IACLwC,UAAU,EAAEtD,IAAI,CAAC4F,SAAS,GAAG5F,IAAI,CAAC4F,SAAS,CAAC,CAAC,GAAG5F,IAAI,CAAC6F,YAAY,IAAI,CAAC7F,IAAI,CAAC6F,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAClGtC,WAAW,EAAE1C,UAAU,CAACI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC1C8D,IAAI,EAAElE,UAAU,CAACiF,OAAO,CAAC;EAC3B,CAAC;AACH;AACA,SAASjE,mCAAmCA,CAAC7B,IAAI,EAAE+F,gBAAgB,EAAEC,QAAQ,EAAE;EAC7E,IAAI3D,cAAc,GAAG1C,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAI0D,YAAY,GAAG1D,IAAI,CAACS,KAAK;EAC7B,IAAIkD,aAAa,GAAGD,YAAY,CAACE,SAAS,CAAC,CAAC;EAC5C,IAAI/C,UAAU,GAAGb,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,IAAIC,MAAM,GAAG,EAAE;EACf;EACA,IAAIgD,IAAI,GAAGP,IAAI,CAACQ,GAAG,CAAC,CAAC+B,gBAAgB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACnD,IAAIE,SAAS,GAAGtC,aAAa,CAAC,CAAC,CAAC;EAChC,IAAIE,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC;EACpC;EACA;EACA;EACA;EACA,IAAImC,SAAS,KAAK,CAAC,IAAIlC,IAAI,GAAG,CAAC,IAAIF,SAAS,GAAGE,IAAI,GAAG,CAAC,EAAE;IACvDkC,SAAS,GAAGzC,IAAI,CAAC0C,KAAK,CAAC1C,IAAI,CAAC2C,IAAI,CAACF,SAAS,GAAGlC,IAAI,CAAC,GAAGA,IAAI,CAAC;EAC5D;EACA;EACA;EACA;EACA;EACA;EACA,IAAIqC,YAAY,GAAGvG,mBAAmB,CAACG,IAAI,CAAC;EAC5C,IAAIqG,eAAe,GAAGxF,UAAU,CAACI,GAAG,CAAC,cAAc,CAAC,IAAImF,YAAY;EACpE,IAAIE,eAAe,GAAGzF,UAAU,CAACI,GAAG,CAAC,cAAc,CAAC,IAAImF,YAAY;EACpE,IAAIC,eAAe,IAAIJ,SAAS,KAAKtC,aAAa,CAAC,CAAC,CAAC,EAAE;IACrD4C,OAAO,CAAC5C,aAAa,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA;EACA,IAAIvB,SAAS,GAAG6D,SAAS;EACzB,OAAO7D,SAAS,IAAIuB,aAAa,CAAC,CAAC,CAAC,EAAEvB,SAAS,IAAI2B,IAAI,EAAE;IACvDwC,OAAO,CAACnE,SAAS,CAAC;EACpB;EACA,IAAIkE,eAAe,IAAIlE,SAAS,GAAG2B,IAAI,KAAKJ,aAAa,CAAC,CAAC,CAAC,EAAE;IAC5D4C,OAAO,CAAC5C,aAAa,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,SAAS4C,OAAOA,CAACnE,SAAS,EAAE;IAC1B,IAAIoE,OAAO,GAAG;MACZ5F,KAAK,EAAEwB;IACT,CAAC;IACDrB,MAAM,CAACiC,IAAI,CAACgD,QAAQ,GAAG5D,SAAS,GAAG;MACjCI,cAAc,EAAEH,cAAc,CAACmE,OAAO,CAAC;MACvC/D,QAAQ,EAAEiB,YAAY,CAAChB,QAAQ,CAAC8D,OAAO,CAAC;MACxCpE,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EACA,OAAOrB,MAAM;AACf;AACA,SAASY,sCAAsCA,CAAC3B,IAAI,EAAE+F,gBAAgB,EAAEC,QAAQ,EAAE;EAChF,IAAItC,YAAY,GAAG1D,IAAI,CAACS,KAAK;EAC7B,IAAI4B,cAAc,GAAG1C,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAIe,MAAM,GAAG,EAAE;EACfvB,MAAM,CAACiH,IAAI,CAAC/C,YAAY,CAAChD,QAAQ,CAAC,CAAC,EAAE,UAAUC,IAAI,EAAE;IACnD,IAAI8B,QAAQ,GAAGiB,YAAY,CAAChB,QAAQ,CAAC/B,IAAI,CAAC;IAC1C,IAAIyB,SAAS,GAAGzB,IAAI,CAACC,KAAK;IAC1B,IAAImF,gBAAgB,CAACpF,IAAI,CAACC,KAAK,EAAE6B,QAAQ,CAAC,EAAE;MAC1C1B,MAAM,CAACiC,IAAI,CAACgD,QAAQ,GAAG5D,SAAS,GAAG;QACjCI,cAAc,EAAEH,cAAc,CAAC1B,IAAI,CAAC;QACpC8B,QAAQ,EAAEA,QAAQ;QAClBL,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOrB,MAAM;AACf"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}