{"ast": null, "code": "import { slice } from \"./array.js\";\nimport constant from \"./constant.js\";\nimport offsetNone from \"./offset/none.js\";\nimport orderNone from \"./order/none.js\";\nfunction stackValue(d, key) {\n  return d[key];\n}\nexport default function () {\n  var keys = constant([]),\n    order = orderNone,\n    offset = offsetNone,\n    value = stackValue;\n  function stack(data) {\n    var kz = keys.apply(this, arguments),\n      i,\n      m = data.length,\n      n = kz.length,\n      sz = new Array(n),\n      oz;\n    for (i = 0; i < n; ++i) {\n      for (var ki = kz[i], si = sz[i] = new Array(m), j = 0, sij; j < m; ++j) {\n        si[j] = sij = [0, +value(data[j], ki, j, data)];\n        sij.data = data[j];\n      }\n      si.key = ki;\n    }\n    for (i = 0, oz = order(sz); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n    offset(sz, oz);\n    return sz;\n  }\n  stack.keys = function (_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : constant(slice.call(_)), stack) : keys;\n  };\n  stack.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), stack) : value;\n  };\n  stack.order = function (_) {\n    return arguments.length ? (order = _ == null ? orderNone : typeof _ === \"function\" ? _ : constant(slice.call(_)), stack) : order;\n  };\n  stack.offset = function (_) {\n    return arguments.length ? (offset = _ == null ? offsetNone : _, stack) : offset;\n  };\n  return stack;\n}", "map": {"version": 3, "names": ["slice", "constant", "offsetNone", "orderNone", "stackValue", "d", "key", "keys", "order", "offset", "value", "stack", "data", "kz", "apply", "arguments", "i", "m", "length", "n", "sz", "Array", "oz", "ki", "si", "j", "sij", "index", "_", "call"], "sources": ["C:/Users/<USER>/Desktop/Rapport_PFE/kuber/source/mian/node_modules/d3-sankey/node_modules/d3-shape/src/stack.js"], "sourcesContent": ["import {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport offsetNone from \"./offset/none.js\";\nimport orderNone from \"./order/none.js\";\n\nfunction stackValue(d, key) {\n  return d[key];\n}\n\nexport default function() {\n  var keys = constant([]),\n      order = orderNone,\n      offset = offsetNone,\n      value = stackValue;\n\n  function stack(data) {\n    var kz = keys.apply(this, arguments),\n        i,\n        m = data.length,\n        n = kz.length,\n        sz = new Array(n),\n        oz;\n\n    for (i = 0; i < n; ++i) {\n      for (var ki = kz[i], si = sz[i] = new Array(m), j = 0, sij; j < m; ++j) {\n        si[j] = sij = [0, +value(data[j], ki, j, data)];\n        sij.data = data[j];\n      }\n      si.key = ki;\n    }\n\n    for (i = 0, oz = order(sz); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n\n    offset(sz, oz);\n    return sz;\n  }\n\n  stack.keys = function(_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : constant(slice.call(_)), stack) : keys;\n  };\n\n  stack.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), stack) : value;\n  };\n\n  stack.order = function(_) {\n    return arguments.length ? (order = _ == null ? orderNone : typeof _ === \"function\" ? _ : constant(slice.call(_)), stack) : order;\n  };\n\n  stack.offset = function(_) {\n    return arguments.length ? (offset = _ == null ? offsetNone : _, stack) : offset;\n  };\n\n  return stack;\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,YAAY;AAChC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AAEvC,SAASC,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC1B,OAAOD,CAAC,CAACC,GAAG,CAAC;AACf;AAEA,eAAe,YAAW;EACxB,IAAIC,IAAI,GAAGN,QAAQ,CAAC,EAAE,CAAC;IACnBO,KAAK,GAAGL,SAAS;IACjBM,MAAM,GAAGP,UAAU;IACnBQ,KAAK,GAAGN,UAAU;EAEtB,SAASO,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIC,EAAE,GAAGN,IAAI,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAChCC,CAAC;MACDC,CAAC,GAAGL,IAAI,CAACM,MAAM;MACfC,CAAC,GAAGN,EAAE,CAACK,MAAM;MACbE,EAAE,GAAG,IAAIC,KAAK,CAACF,CAAC,CAAC;MACjBG,EAAE;IAEN,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,CAAC,EAAE,EAAEH,CAAC,EAAE;MACtB,KAAK,IAAIO,EAAE,GAAGV,EAAE,CAACG,CAAC,CAAC,EAAEQ,EAAE,GAAGJ,EAAE,CAACJ,CAAC,CAAC,GAAG,IAAIK,KAAK,CAACJ,CAAC,CAAC,EAAEQ,CAAC,GAAG,CAAC,EAAEC,GAAG,EAAED,CAAC,GAAGR,CAAC,EAAE,EAAEQ,CAAC,EAAE;QACtED,EAAE,CAACC,CAAC,CAAC,GAAGC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAChB,KAAK,CAACE,IAAI,CAACa,CAAC,CAAC,EAAEF,EAAE,EAAEE,CAAC,EAAEb,IAAI,CAAC,CAAC;QAC/Cc,GAAG,CAACd,IAAI,GAAGA,IAAI,CAACa,CAAC,CAAC;MACpB;MACAD,EAAE,CAAClB,GAAG,GAAGiB,EAAE;IACb;IAEA,KAAKP,CAAC,GAAG,CAAC,EAAEM,EAAE,GAAGd,KAAK,CAACY,EAAE,CAAC,EAAEJ,CAAC,GAAGG,CAAC,EAAE,EAAEH,CAAC,EAAE;MACtCI,EAAE,CAACE,EAAE,CAACN,CAAC,CAAC,CAAC,CAACW,KAAK,GAAGX,CAAC;IACrB;IAEAP,MAAM,CAACW,EAAE,EAAEE,EAAE,CAAC;IACd,OAAOF,EAAE;EACX;EAEAT,KAAK,CAACJ,IAAI,GAAG,UAASqB,CAAC,EAAE;IACvB,OAAOb,SAAS,CAACG,MAAM,IAAIX,IAAI,GAAG,OAAOqB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG3B,QAAQ,CAACD,KAAK,CAAC6B,IAAI,CAACD,CAAC,CAAC,CAAC,EAAEjB,KAAK,IAAIJ,IAAI;EACxG,CAAC;EAEDI,KAAK,CAACD,KAAK,GAAG,UAASkB,CAAC,EAAE;IACxB,OAAOb,SAAS,CAACG,MAAM,IAAIR,KAAK,GAAG,OAAOkB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG3B,QAAQ,CAAC,CAAC2B,CAAC,CAAC,EAAEjB,KAAK,IAAID,KAAK;EAC/F,CAAC;EAEDC,KAAK,CAACH,KAAK,GAAG,UAASoB,CAAC,EAAE;IACxB,OAAOb,SAAS,CAACG,MAAM,IAAIV,KAAK,GAAGoB,CAAC,IAAI,IAAI,GAAGzB,SAAS,GAAG,OAAOyB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG3B,QAAQ,CAACD,KAAK,CAAC6B,IAAI,CAACD,CAAC,CAAC,CAAC,EAAEjB,KAAK,IAAIH,KAAK;EAClI,CAAC;EAEDG,KAAK,CAACF,MAAM,GAAG,UAASmB,CAAC,EAAE;IACzB,OAAOb,SAAS,CAACG,MAAM,IAAIT,MAAM,GAAGmB,CAAC,IAAI,IAAI,GAAG1B,UAAU,GAAG0B,CAAC,EAAEjB,KAAK,IAAIF,MAAM;EACjF,CAAC;EAED,OAAOE,KAAK;AACd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}