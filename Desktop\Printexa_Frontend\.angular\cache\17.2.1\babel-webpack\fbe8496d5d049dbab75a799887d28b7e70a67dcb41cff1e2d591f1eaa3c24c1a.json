{"ast": null, "code": "import { RouterLink } from '@angular/router';\nimport { MatButtonModule } from '@angular/material/button';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/button\";\nexport class Page404Component {\n  constructor() {\n    // constructor code\n  }\n  static #_ = this.ɵfac = function Page404Component_Factory(t) {\n    return new (t || Page404Component)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: Page404Component,\n    selectors: [[\"app-page404\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 21,\n    vars: 0,\n    consts: [[1, \"auth-container\"], [1, \"row\", \"auth-main\"], [1, \"col-sm-6\", \"px-0\", \"d-none\", \"d-sm-block\"], [1, \"left-img\", 2, \"background-image\", \"url(assets/images/pages/bg-04.png)\"], [1, \"col-sm-6\", \"auth-form-section\"], [1, \"form-section\"], [1, \"auth-wrapper\"], [1, \"error-header\", \"p-b-45\"], [1, \"error-subheader\", \"p-b-5\"], [1, \"error-subheader2\", \"p-b-5\"], [1, \"container-auth-form-btn\", \"mt-5\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"auth-form-btn\"], [1, \"w-full\", \"p-t-15\", \"p-b-15\", \"text-center\"], [\"routerLink\", \"/authentication/signin\", 1, \"txt1\"]],\n    template: function Page404Component_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"form\")(8, \"span\", 7);\n        i0.ɵɵtext(9, \" 404 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"span\", 8);\n        i0.ɵɵtext(11, \" Looks Like You're Lost \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"span\", 9);\n        i0.ɵɵtext(13, \" The Page You Are Looking For Not Available! \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"div\", 10)(15, \"button\", 11);\n        i0.ɵɵtext(16, \" Go To Home Page \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 12)(18, \"div\")(19, \"a\", 13);\n        i0.ɵɵtext(20, \" Need Help? \");\n        i0.ɵɵelementEnd()()()()()()()()();\n      }\n    },\n    dependencies: [FormsModule, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.NgForm, MatButtonModule, i2.MatButton, RouterLink],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["RouterLink", "MatButtonModule", "FormsModule", "Page404Component", "constructor", "_", "_2", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "Page404Component_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "i1", "ɵNgNoValidate", "NgControlStatusGroup", "NgForm", "i2", "MatButton", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\authentication\\page404\\page404.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\authentication\\page404\\page404.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { RouterLink } from '@angular/router';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { FormsModule } from '@angular/forms';\r\n@Component({\r\n    selector: 'app-page404',\r\n    templateUrl: './page404.component.html',\r\n    styleUrls: ['./page404.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        FormsModule,\r\n        MatButtonModule,\r\n        RouterLink,\r\n    ],\r\n})\r\nexport class Page404Component {\r\n  constructor() {\r\n    // constructor code\r\n  }\r\n}\r\n", "<div class=\"auth-container\">\r\n  <div class=\"row auth-main\">\r\n    <div class=\"col-sm-6 px-0 d-none d-sm-block\">\r\n      <div class=\"left-img\" style=\"background-image: url(assets/images/pages/bg-04.png);\">\r\n      </div>\r\n    </div>\r\n    <div class=\"col-sm-6 auth-form-section\">\r\n      <div class=\"form-section\">\r\n        <div class=\"auth-wrapper\">\r\n          <form>\r\n            <span class=\"error-header p-b-45\">\r\n              404\r\n            </span>\r\n            <span class=\"error-subheader p-b-5\">\r\n              Looks Like You're Lost\r\n            </span>\r\n            <span class=\"error-subheader2 p-b-5\">\r\n              The Page You Are Looking For Not Available!\r\n            </span>\r\n            <div class=\"container-auth-form-btn mt-5\">\r\n              <button mat-raised-button color=\"primary\" class=\"auth-form-btn\" type=\"submit\">\r\n                Go To Home Page\r\n              </button>\r\n            </div>\r\n            <div class=\"w-full p-t-15 p-b-15 text-center\">\r\n              <div>\r\n                <a routerLink=\"/authentication/signin\" class=\"txt1\">\r\n                  Need Help?\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,WAAW,QAAQ,gBAAgB;;;;AAY5C,OAAM,MAAOC,gBAAgB;EAC3BC,YAAA;IACE;EAAA;EACD,QAAAC,CAAA,G;qBAHUF,gBAAgB;EAAA;EAAA,QAAAG,EAAA,G;UAAhBH,gBAAgB;IAAAI,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCf7BP,EAAA,CAAAS,cAAA,aAA4B;QAGtBT,EAAA,CAAAU,SAAA,aACM;QACRV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,aAAwC;QAK9BT,EAAA,CAAAY,MAAA,YACF;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QACPX,EAAA,CAAAS,cAAA,eAAoC;QAClCT,EAAA,CAAAY,MAAA,gCACF;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QACPX,EAAA,CAAAS,cAAA,eAAqC;QACnCT,EAAA,CAAAY,MAAA,qDACF;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QACPX,EAAA,CAAAS,cAAA,eAA0C;QAEtCT,EAAA,CAAAY,MAAA,yBACF;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QAEXX,EAAA,CAAAS,cAAA,eAA8C;QAGxCT,EAAA,CAAAY,MAAA,oBACF;QAAAZ,EAAA,CAAAW,YAAA,EAAI;;;mBDlBZnB,WAAW,EAAAqB,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,MAAA,EACXzB,eAAe,EAAA0B,EAAA,CAAAC,SAAA,EACf5B,UAAU;IAAA6B,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}