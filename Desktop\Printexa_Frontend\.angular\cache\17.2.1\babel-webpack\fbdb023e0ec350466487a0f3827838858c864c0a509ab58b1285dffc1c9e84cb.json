{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Inject, Input, Output, NgModule } from '@angular/core';\nimport { ReplaySubject, Subscription, Subject, asyncScheduler, Observable } from 'rxjs';\nimport { throttleTime, switchMap } from 'rxjs/operators';\nclass ChangeFilterV2 {\n  constructor() {\n    this.subject = new ReplaySubject(1);\n    this.subscriptions = new Subscription();\n  }\n  doFilter(changes) {\n    this.subject.next(changes);\n  }\n  dispose() {\n    this.subscriptions.unsubscribe();\n  }\n  notEmpty(key, handler) {\n    this.subscriptions.add(this.subject.subscribe(changes => {\n      if (changes[key]) {\n        const value = changes[key].currentValue;\n        if (value !== undefined && value !== null) {\n          handler(value);\n        }\n      }\n    }));\n  }\n  has(key, handler) {\n    this.subscriptions.add(this.subject.subscribe(changes => {\n      if (changes[key]) {\n        const value = changes[key].currentValue;\n        handler(value);\n      }\n    }));\n  }\n  notFirst(key, handler) {\n    this.subscriptions.add(this.subject.subscribe(changes => {\n      if (changes[key] && !changes[key].isFirstChange()) {\n        const value = changes[key].currentValue;\n        handler(value);\n      }\n    }));\n  }\n  notFirstAndEmpty(key, handler) {\n    this.subscriptions.add(this.subject.subscribe(changes => {\n      if (changes[key] && !changes[key].isFirstChange()) {\n        const value = changes[key].currentValue;\n        if (value !== undefined && value !== null) {\n          handler(value);\n        }\n      }\n    }));\n  }\n}\nconst NGX_ECHARTS_CONFIG = new InjectionToken('NGX_ECHARTS_CONFIG');\nclass NgxEchartsDirective {\n  constructor(config, el, ngZone) {\n    this.el = el;\n    this.ngZone = ngZone;\n    this.options = null;\n    this.theme = null;\n    this.initOpts = null;\n    this.merge = null;\n    this.autoResize = true;\n    this.loading = false;\n    this.loadingType = 'default';\n    this.loadingOpts = null;\n    // ngx-echarts events\n    this.chartInit = new EventEmitter();\n    this.optionsError = new EventEmitter();\n    // echarts mouse events\n    this.chartClick = this.createLazyEvent('click');\n    this.chartDblClick = this.createLazyEvent('dblclick');\n    this.chartMouseDown = this.createLazyEvent('mousedown');\n    this.chartMouseMove = this.createLazyEvent('mousemove');\n    this.chartMouseUp = this.createLazyEvent('mouseup');\n    this.chartMouseOver = this.createLazyEvent('mouseover');\n    this.chartMouseOut = this.createLazyEvent('mouseout');\n    this.chartGlobalOut = this.createLazyEvent('globalout');\n    this.chartContextMenu = this.createLazyEvent('contextmenu');\n    // echarts events\n    this.chartHighlight = this.createLazyEvent('highlight');\n    this.chartDownplay = this.createLazyEvent('downplay');\n    this.chartSelectChanged = this.createLazyEvent('selectchanged');\n    this.chartLegendSelectChanged = this.createLazyEvent('legendselectchanged');\n    this.chartLegendSelected = this.createLazyEvent('legendselected');\n    this.chartLegendUnselected = this.createLazyEvent('legendunselected');\n    this.chartLegendLegendSelectAll = this.createLazyEvent('legendselectall');\n    this.chartLegendLegendInverseSelect = this.createLazyEvent('legendinverseselect');\n    this.chartLegendScroll = this.createLazyEvent('legendscroll');\n    this.chartDataZoom = this.createLazyEvent('datazoom');\n    this.chartDataRangeSelected = this.createLazyEvent('datarangeselected');\n    this.chartGraphRoam = this.createLazyEvent('graphroam');\n    this.chartGeoRoam = this.createLazyEvent('georoam');\n    this.chartTreeRoam = this.createLazyEvent('treeroam');\n    this.chartTimelineChanged = this.createLazyEvent('timelinechanged');\n    this.chartTimelinePlayChanged = this.createLazyEvent('timelineplaychanged');\n    this.chartRestore = this.createLazyEvent('restore');\n    this.chartDataViewChanged = this.createLazyEvent('dataviewchanged');\n    this.chartMagicTypeChanged = this.createLazyEvent('magictypechanged');\n    this.chartGeoSelectChanged = this.createLazyEvent('geoselectchanged');\n    this.chartGeoSelected = this.createLazyEvent('geoselected');\n    this.chartGeoUnselected = this.createLazyEvent('geounselected');\n    this.chartAxisAreaSelected = this.createLazyEvent('axisareaselected');\n    this.chartBrush = this.createLazyEvent('brush');\n    this.chartBrushEnd = this.createLazyEvent('brushend');\n    this.chartBrushSelected = this.createLazyEvent('brushselected');\n    this.chartGlobalCursorTaken = this.createLazyEvent('globalcursortaken');\n    this.chartRendered = this.createLazyEvent('rendered');\n    this.chartFinished = this.createLazyEvent('finished');\n    this.animationFrameID = null;\n    this.chart$ = new ReplaySubject(1);\n    this.resize$ = new Subject();\n    this.changeFilter = new ChangeFilterV2();\n    this.echarts = config.echarts;\n  }\n  ngOnChanges(changes) {\n    this.changeFilter.doFilter(changes);\n  }\n  ngOnInit() {\n    if (!window.ResizeObserver) {\n      throw new Error('please install a polyfill for ResizeObserver');\n    }\n    this.resizeSub = this.resize$.pipe(throttleTime(100, asyncScheduler, {\n      leading: false,\n      trailing: true\n    })).subscribe(() => this.resize());\n    if (this.autoResize) {\n      this.resizeOb = this.ngZone.runOutsideAngular(() => new window.ResizeObserver(() => {\n        this.animationFrameID = window.requestAnimationFrame(() => this.resize$.next());\n      }));\n      this.resizeOb.observe(this.el.nativeElement);\n    }\n    this.changeFilter.notFirstAndEmpty('options', opt => this.onOptionsChange(opt));\n    this.changeFilter.notFirstAndEmpty('merge', opt => this.setOption(opt));\n    this.changeFilter.has('loading', v => this.toggleLoading(!!v));\n    this.changeFilter.notFirst('theme', () => this.refreshChart());\n  }\n  ngOnDestroy() {\n    window.clearTimeout(this.initChartTimer);\n    if (this.resizeSub) {\n      this.resizeSub.unsubscribe();\n    }\n    if (this.animationFrameID) {\n      window.cancelAnimationFrame(this.animationFrameID);\n    }\n    if (this.resizeOb) {\n      this.resizeOb.unobserve(this.el.nativeElement);\n    }\n    if (this.loadingSub) {\n      this.loadingSub.unsubscribe();\n    }\n    this.changeFilter.dispose();\n    this.dispose();\n  }\n  ngAfterViewInit() {\n    this.initChartTimer = window.setTimeout(() => this.initChart());\n  }\n  dispose() {\n    if (this.chart) {\n      if (!this.chart.isDisposed()) {\n        this.chart.dispose();\n      }\n      this.chart = null;\n    }\n  }\n  /**\n   * resize chart\n   */\n  resize() {\n    if (this.chart) {\n      this.chart.resize();\n    }\n  }\n  toggleLoading(loading) {\n    if (this.chart) {\n      loading ? this.chart.showLoading(this.loadingType, this.loadingOpts) : this.chart.hideLoading();\n    } else {\n      this.loadingSub = this.chart$.subscribe(chart => loading ? chart.showLoading(this.loadingType, this.loadingOpts) : chart.hideLoading());\n    }\n  }\n  setOption(option, opts) {\n    if (this.chart) {\n      try {\n        this.chart.setOption(option, opts);\n      } catch (e) {\n        console.error(e);\n        this.optionsError.emit(e);\n      }\n    }\n  }\n  /**\n   * dispose old chart and create a new one.\n   */\n  refreshChart() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.dispose();\n      yield _this.initChart();\n    })();\n  }\n  createChart() {\n    const dom = this.el.nativeElement;\n    if (window && window.getComputedStyle) {\n      const prop = window.getComputedStyle(dom, null).getPropertyValue('height');\n      if ((!prop || prop === '0px') && (!dom.style.height || dom.style.height === '0px')) {\n        dom.style.height = '400px';\n      }\n    }\n    // here a bit tricky: we check if the echarts module is provided as function returning native import('...') then use the promise\n    // otherwise create the function that imitates behaviour above with a provided as is module\n    return this.ngZone.runOutsideAngular(() => {\n      const load = typeof this.echarts === 'function' ? this.echarts : () => Promise.resolve(this.echarts);\n      return load().then(({\n        init\n      }) => init(dom, this.theme, this.initOpts));\n    });\n  }\n  initChart() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield _this2.onOptionsChange(_this2.options);\n      if (_this2.merge && _this2.chart) {\n        _this2.setOption(_this2.merge);\n      }\n    })();\n  }\n  onOptionsChange(opt) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!opt) {\n        return;\n      }\n      if (_this3.chart) {\n        _this3.setOption(_this3.options, true);\n      } else {\n        _this3.chart = yield _this3.createChart();\n        _this3.chart$.next(_this3.chart);\n        _this3.chartInit.emit(_this3.chart);\n        _this3.setOption(_this3.options, true);\n      }\n    })();\n  }\n  // allows to lazily bind to only those events that are requested through the `@Output` by parent components\n  // see https://stackoverflow.com/questions/51787972/optimal-reentering-the-ngzone-from-eventemitter-event for more info\n  createLazyEvent(eventName) {\n    return this.chartInit.pipe(switchMap(chart => new Observable(observer => {\n      chart.on(eventName, data => this.ngZone.run(() => observer.next(data)));\n      return () => {\n        if (this.chart) {\n          if (!this.chart.isDisposed()) {\n            chart.off(eventName);\n          }\n        }\n      };\n    })));\n  }\n  static #_ = this.ɵfac = function NgxEchartsDirective_Factory(t) {\n    return new (t || NgxEchartsDirective)(i0.ɵɵdirectiveInject(NGX_ECHARTS_CONFIG), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgxEchartsDirective,\n    selectors: [[\"echarts\"], [\"\", \"echarts\", \"\"]],\n    inputs: {\n      options: \"options\",\n      theme: \"theme\",\n      initOpts: \"initOpts\",\n      merge: \"merge\",\n      autoResize: \"autoResize\",\n      loading: \"loading\",\n      loadingType: \"loadingType\",\n      loadingOpts: \"loadingOpts\"\n    },\n    outputs: {\n      chartInit: \"chartInit\",\n      optionsError: \"optionsError\",\n      chartClick: \"chartClick\",\n      chartDblClick: \"chartDblClick\",\n      chartMouseDown: \"chartMouseDown\",\n      chartMouseMove: \"chartMouseMove\",\n      chartMouseUp: \"chartMouseUp\",\n      chartMouseOver: \"chartMouseOver\",\n      chartMouseOut: \"chartMouseOut\",\n      chartGlobalOut: \"chartGlobalOut\",\n      chartContextMenu: \"chartContextMenu\",\n      chartHighlight: \"chartHighlight\",\n      chartDownplay: \"chartDownplay\",\n      chartSelectChanged: \"chartSelectChanged\",\n      chartLegendSelectChanged: \"chartLegendSelectChanged\",\n      chartLegendSelected: \"chartLegendSelected\",\n      chartLegendUnselected: \"chartLegendUnselected\",\n      chartLegendLegendSelectAll: \"chartLegendLegendSelectAll\",\n      chartLegendLegendInverseSelect: \"chartLegendLegendInverseSelect\",\n      chartLegendScroll: \"chartLegendScroll\",\n      chartDataZoom: \"chartDataZoom\",\n      chartDataRangeSelected: \"chartDataRangeSelected\",\n      chartGraphRoam: \"chartGraphRoam\",\n      chartGeoRoam: \"chartGeoRoam\",\n      chartTreeRoam: \"chartTreeRoam\",\n      chartTimelineChanged: \"chartTimelineChanged\",\n      chartTimelinePlayChanged: \"chartTimelinePlayChanged\",\n      chartRestore: \"chartRestore\",\n      chartDataViewChanged: \"chartDataViewChanged\",\n      chartMagicTypeChanged: \"chartMagicTypeChanged\",\n      chartGeoSelectChanged: \"chartGeoSelectChanged\",\n      chartGeoSelected: \"chartGeoSelected\",\n      chartGeoUnselected: \"chartGeoUnselected\",\n      chartAxisAreaSelected: \"chartAxisAreaSelected\",\n      chartBrush: \"chartBrush\",\n      chartBrushEnd: \"chartBrushEnd\",\n      chartBrushSelected: \"chartBrushSelected\",\n      chartGlobalCursorTaken: \"chartGlobalCursorTaken\",\n      chartRendered: \"chartRendered\",\n      chartFinished: \"chartFinished\"\n    },\n    exportAs: [\"echarts\"],\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxEchartsDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'echarts, [echarts]',\n      exportAs: 'echarts'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NGX_ECHARTS_CONFIG]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }], {\n    options: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    initOpts: [{\n      type: Input\n    }],\n    merge: [{\n      type: Input\n    }],\n    autoResize: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    loadingType: [{\n      type: Input\n    }],\n    loadingOpts: [{\n      type: Input\n    }],\n    chartInit: [{\n      type: Output\n    }],\n    optionsError: [{\n      type: Output\n    }],\n    chartClick: [{\n      type: Output\n    }],\n    chartDblClick: [{\n      type: Output\n    }],\n    chartMouseDown: [{\n      type: Output\n    }],\n    chartMouseMove: [{\n      type: Output\n    }],\n    chartMouseUp: [{\n      type: Output\n    }],\n    chartMouseOver: [{\n      type: Output\n    }],\n    chartMouseOut: [{\n      type: Output\n    }],\n    chartGlobalOut: [{\n      type: Output\n    }],\n    chartContextMenu: [{\n      type: Output\n    }],\n    chartHighlight: [{\n      type: Output\n    }],\n    chartDownplay: [{\n      type: Output\n    }],\n    chartSelectChanged: [{\n      type: Output\n    }],\n    chartLegendSelectChanged: [{\n      type: Output\n    }],\n    chartLegendSelected: [{\n      type: Output\n    }],\n    chartLegendUnselected: [{\n      type: Output\n    }],\n    chartLegendLegendSelectAll: [{\n      type: Output\n    }],\n    chartLegendLegendInverseSelect: [{\n      type: Output\n    }],\n    chartLegendScroll: [{\n      type: Output\n    }],\n    chartDataZoom: [{\n      type: Output\n    }],\n    chartDataRangeSelected: [{\n      type: Output\n    }],\n    chartGraphRoam: [{\n      type: Output\n    }],\n    chartGeoRoam: [{\n      type: Output\n    }],\n    chartTreeRoam: [{\n      type: Output\n    }],\n    chartTimelineChanged: [{\n      type: Output\n    }],\n    chartTimelinePlayChanged: [{\n      type: Output\n    }],\n    chartRestore: [{\n      type: Output\n    }],\n    chartDataViewChanged: [{\n      type: Output\n    }],\n    chartMagicTypeChanged: [{\n      type: Output\n    }],\n    chartGeoSelectChanged: [{\n      type: Output\n    }],\n    chartGeoSelected: [{\n      type: Output\n    }],\n    chartGeoUnselected: [{\n      type: Output\n    }],\n    chartAxisAreaSelected: [{\n      type: Output\n    }],\n    chartBrush: [{\n      type: Output\n    }],\n    chartBrushEnd: [{\n      type: Output\n    }],\n    chartBrushSelected: [{\n      type: Output\n    }],\n    chartGlobalCursorTaken: [{\n      type: Output\n    }],\n    chartRendered: [{\n      type: Output\n    }],\n    chartFinished: [{\n      type: Output\n    }]\n  });\n})();\nconst provideEcharts = () => {\n  return {\n    provide: NGX_ECHARTS_CONFIG,\n    useFactory: () => ({\n      echarts: () => import('echarts')\n    })\n  };\n};\nconst provideEchartsCore = config => {\n  return {\n    provide: NGX_ECHARTS_CONFIG,\n    useValue: config\n  };\n};\nclass NgxEchartsModule {\n  static forRoot(config) {\n    return {\n      ngModule: NgxEchartsModule,\n      providers: [provideEchartsCore(config)]\n    };\n  }\n  static forChild() {\n    return {\n      ngModule: NgxEchartsModule\n    };\n  }\n  static #_ = this.ɵfac = function NgxEchartsModule_Factory(t) {\n    return new (t || NgxEchartsModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NgxEchartsModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxEchartsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NgxEchartsDirective],\n      exports: [NgxEchartsDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ngx-echarts\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NGX_ECHARTS_CONFIG, NgxEchartsDirective, NgxEchartsModule, provideEcharts, provideEchartsCore };", "map": {"version": 3, "names": ["i0", "InjectionToken", "EventEmitter", "Directive", "Inject", "Input", "Output", "NgModule", "ReplaySubject", "Subscription", "Subject", "asyncScheduler", "Observable", "throttleTime", "switchMap", "ChangeFilterV2", "constructor", "subject", "subscriptions", "<PERSON><PERSON><PERSON><PERSON>", "changes", "next", "dispose", "unsubscribe", "notEmpty", "key", "handler", "add", "subscribe", "value", "currentValue", "undefined", "has", "not<PERSON><PERSON><PERSON>", "isFirstChange", "notFirstAndEmpty", "NGX_ECHARTS_CONFIG", "NgxEchartsDirective", "config", "el", "ngZone", "options", "theme", "initOpts", "merge", "autoResize", "loading", "loadingType", "loadingOpts", "chartInit", "optionsError", "chartClick", "createLazyEvent", "chartDblClick", "chartMouseDown", "chartMouseMove", "chartMouseUp", "chartMouseOver", "chartMouseOut", "chartGlobalOut", "chartContextMenu", "chartHighlight", "chartDownplay", "chartSelectChanged", "chartLegendSelectChanged", "chartLegendSelected", "chartLegendUnselected", "chartLegendLegendSelectAll", "chartLegendLegendInverseSelect", "chartLegendScroll", "chartDataZoom", "chartDataRangeSelected", "chartGraphRoam", "chartGeoRoam", "chartTreeRoam", "chartTimelineChanged", "chartTimelinePlayChanged", "chartRestore", "chartDataViewChanged", "chartMagicTypeChanged", "chartGeoSelectChanged", "chartGeoSelected", "chartGeoUnselected", "chartAxisAreaSelected", "chartBrush", "chartBrushEnd", "chartBrushSelected", "chartGlobalCursorTaken", "chartRendered", "chartFinished", "animationFrameID", "chart$", "resize$", "changeFilter", "echarts", "ngOnChanges", "ngOnInit", "window", "ResizeObserver", "Error", "resizeSub", "pipe", "leading", "trailing", "resize", "resizeOb", "runOutsideAngular", "requestAnimationFrame", "observe", "nativeElement", "opt", "onOptionsChange", "setOption", "v", "toggleLoading", "refresh<PERSON><PERSON>", "ngOnDestroy", "clearTimeout", "initChartTimer", "cancelAnimationFrame", "unobserve", "loadingSub", "ngAfterViewInit", "setTimeout", "initChart", "chart", "isDisposed", "showLoading", "hideLoading", "option", "opts", "e", "console", "error", "emit", "_this", "_asyncToGenerator", "createChart", "dom", "getComputedStyle", "prop", "getPropertyValue", "style", "height", "load", "Promise", "resolve", "then", "init", "_this2", "_this3", "eventName", "observer", "on", "data", "run", "off", "_", "ɵfac", "NgxEchartsDirective_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgZone", "_2", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "outputs", "exportAs", "standalone", "features", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "decorators", "provideEcharts", "provide", "useFactory", "provideEchartsCore", "useValue", "NgxEchartsModule", "forRoot", "ngModule", "providers", "<PERSON><PERSON><PERSON><PERSON>", "NgxEchartsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/ngx-echarts/fesm2022/ngx-echarts.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Directive, Inject, Input, Output, NgModule } from '@angular/core';\nimport { ReplaySubject, Subscription, Subject, asyncScheduler, Observable } from 'rxjs';\nimport { throttleTime, switchMap } from 'rxjs/operators';\n\nclass ChangeFilterV2 {\n    constructor() {\n        this.subject = new ReplaySubject(1);\n        this.subscriptions = new Subscription();\n    }\n    doFilter(changes) {\n        this.subject.next(changes);\n    }\n    dispose() {\n        this.subscriptions.unsubscribe();\n    }\n    notEmpty(key, handler) {\n        this.subscriptions.add(this.subject.subscribe(changes => {\n            if (changes[key]) {\n                const value = changes[key].currentValue;\n                if (value !== undefined && value !== null) {\n                    handler(value);\n                }\n            }\n        }));\n    }\n    has(key, handler) {\n        this.subscriptions.add(this.subject.subscribe(changes => {\n            if (changes[key]) {\n                const value = changes[key].currentValue;\n                handler(value);\n            }\n        }));\n    }\n    notFirst(key, handler) {\n        this.subscriptions.add(this.subject.subscribe(changes => {\n            if (changes[key] && !changes[key].isFirstChange()) {\n                const value = changes[key].currentValue;\n                handler(value);\n            }\n        }));\n    }\n    notFirstAndEmpty(key, handler) {\n        this.subscriptions.add(this.subject.subscribe(changes => {\n            if (changes[key] && !changes[key].isFirstChange()) {\n                const value = changes[key].currentValue;\n                if (value !== undefined && value !== null) {\n                    handler(value);\n                }\n            }\n        }));\n    }\n}\n\nconst NGX_ECHARTS_CONFIG = new InjectionToken('NGX_ECHARTS_CONFIG');\nclass NgxEchartsDirective {\n    constructor(config, el, ngZone) {\n        this.el = el;\n        this.ngZone = ngZone;\n        this.options = null;\n        this.theme = null;\n        this.initOpts = null;\n        this.merge = null;\n        this.autoResize = true;\n        this.loading = false;\n        this.loadingType = 'default';\n        this.loadingOpts = null;\n        // ngx-echarts events\n        this.chartInit = new EventEmitter();\n        this.optionsError = new EventEmitter();\n        // echarts mouse events\n        this.chartClick = this.createLazyEvent('click');\n        this.chartDblClick = this.createLazyEvent('dblclick');\n        this.chartMouseDown = this.createLazyEvent('mousedown');\n        this.chartMouseMove = this.createLazyEvent('mousemove');\n        this.chartMouseUp = this.createLazyEvent('mouseup');\n        this.chartMouseOver = this.createLazyEvent('mouseover');\n        this.chartMouseOut = this.createLazyEvent('mouseout');\n        this.chartGlobalOut = this.createLazyEvent('globalout');\n        this.chartContextMenu = this.createLazyEvent('contextmenu');\n        // echarts events\n        this.chartHighlight = this.createLazyEvent('highlight');\n        this.chartDownplay = this.createLazyEvent('downplay');\n        this.chartSelectChanged = this.createLazyEvent('selectchanged');\n        this.chartLegendSelectChanged = this.createLazyEvent('legendselectchanged');\n        this.chartLegendSelected = this.createLazyEvent('legendselected');\n        this.chartLegendUnselected = this.createLazyEvent('legendunselected');\n        this.chartLegendLegendSelectAll = this.createLazyEvent('legendselectall');\n        this.chartLegendLegendInverseSelect = this.createLazyEvent('legendinverseselect');\n        this.chartLegendScroll = this.createLazyEvent('legendscroll');\n        this.chartDataZoom = this.createLazyEvent('datazoom');\n        this.chartDataRangeSelected = this.createLazyEvent('datarangeselected');\n        this.chartGraphRoam = this.createLazyEvent('graphroam');\n        this.chartGeoRoam = this.createLazyEvent('georoam');\n        this.chartTreeRoam = this.createLazyEvent('treeroam');\n        this.chartTimelineChanged = this.createLazyEvent('timelinechanged');\n        this.chartTimelinePlayChanged = this.createLazyEvent('timelineplaychanged');\n        this.chartRestore = this.createLazyEvent('restore');\n        this.chartDataViewChanged = this.createLazyEvent('dataviewchanged');\n        this.chartMagicTypeChanged = this.createLazyEvent('magictypechanged');\n        this.chartGeoSelectChanged = this.createLazyEvent('geoselectchanged');\n        this.chartGeoSelected = this.createLazyEvent('geoselected');\n        this.chartGeoUnselected = this.createLazyEvent('geounselected');\n        this.chartAxisAreaSelected = this.createLazyEvent('axisareaselected');\n        this.chartBrush = this.createLazyEvent('brush');\n        this.chartBrushEnd = this.createLazyEvent('brushend');\n        this.chartBrushSelected = this.createLazyEvent('brushselected');\n        this.chartGlobalCursorTaken = this.createLazyEvent('globalcursortaken');\n        this.chartRendered = this.createLazyEvent('rendered');\n        this.chartFinished = this.createLazyEvent('finished');\n        this.animationFrameID = null;\n        this.chart$ = new ReplaySubject(1);\n        this.resize$ = new Subject();\n        this.changeFilter = new ChangeFilterV2();\n        this.echarts = config.echarts;\n    }\n    ngOnChanges(changes) {\n        this.changeFilter.doFilter(changes);\n    }\n    ngOnInit() {\n        if (!window.ResizeObserver) {\n            throw new Error('please install a polyfill for ResizeObserver');\n        }\n        this.resizeSub = this.resize$\n            .pipe(throttleTime(100, asyncScheduler, { leading: false, trailing: true }))\n            .subscribe(() => this.resize());\n        if (this.autoResize) {\n            this.resizeOb = this.ngZone.runOutsideAngular(() => new window.ResizeObserver(() => {\n                this.animationFrameID = window.requestAnimationFrame(() => this.resize$.next());\n            }));\n            this.resizeOb.observe(this.el.nativeElement);\n        }\n        this.changeFilter.notFirstAndEmpty('options', opt => this.onOptionsChange(opt));\n        this.changeFilter.notFirstAndEmpty('merge', opt => this.setOption(opt));\n        this.changeFilter.has('loading', v => this.toggleLoading(!!v));\n        this.changeFilter.notFirst('theme', () => this.refreshChart());\n    }\n    ngOnDestroy() {\n        window.clearTimeout(this.initChartTimer);\n        if (this.resizeSub) {\n            this.resizeSub.unsubscribe();\n        }\n        if (this.animationFrameID) {\n            window.cancelAnimationFrame(this.animationFrameID);\n        }\n        if (this.resizeOb) {\n            this.resizeOb.unobserve(this.el.nativeElement);\n        }\n        if (this.loadingSub) {\n            this.loadingSub.unsubscribe();\n        }\n        this.changeFilter.dispose();\n        this.dispose();\n    }\n    ngAfterViewInit() {\n        this.initChartTimer = window.setTimeout(() => this.initChart());\n    }\n    dispose() {\n        if (this.chart) {\n            if (!this.chart.isDisposed()) {\n                this.chart.dispose();\n            }\n            this.chart = null;\n        }\n    }\n    /**\n     * resize chart\n     */\n    resize() {\n        if (this.chart) {\n            this.chart.resize();\n        }\n    }\n    toggleLoading(loading) {\n        if (this.chart) {\n            loading\n                ? this.chart.showLoading(this.loadingType, this.loadingOpts)\n                : this.chart.hideLoading();\n        }\n        else {\n            this.loadingSub = this.chart$.subscribe(chart => loading ? chart.showLoading(this.loadingType, this.loadingOpts) : chart.hideLoading());\n        }\n    }\n    setOption(option, opts) {\n        if (this.chart) {\n            try {\n                this.chart.setOption(option, opts);\n            }\n            catch (e) {\n                console.error(e);\n                this.optionsError.emit(e);\n            }\n        }\n    }\n    /**\n     * dispose old chart and create a new one.\n     */\n    async refreshChart() {\n        this.dispose();\n        await this.initChart();\n    }\n    createChart() {\n        const dom = this.el.nativeElement;\n        if (window && window.getComputedStyle) {\n            const prop = window.getComputedStyle(dom, null).getPropertyValue('height');\n            if ((!prop || prop === '0px') && (!dom.style.height || dom.style.height === '0px')) {\n                dom.style.height = '400px';\n            }\n        }\n        // here a bit tricky: we check if the echarts module is provided as function returning native import('...') then use the promise\n        // otherwise create the function that imitates behaviour above with a provided as is module\n        return this.ngZone.runOutsideAngular(() => {\n            const load = typeof this.echarts === 'function' ? this.echarts : () => Promise.resolve(this.echarts);\n            return load().then(({ init }) => init(dom, this.theme, this.initOpts));\n        });\n    }\n    async initChart() {\n        await this.onOptionsChange(this.options);\n        if (this.merge && this.chart) {\n            this.setOption(this.merge);\n        }\n    }\n    async onOptionsChange(opt) {\n        if (!opt) {\n            return;\n        }\n        if (this.chart) {\n            this.setOption(this.options, true);\n        }\n        else {\n            this.chart = await this.createChart();\n            this.chart$.next(this.chart);\n            this.chartInit.emit(this.chart);\n            this.setOption(this.options, true);\n        }\n    }\n    // allows to lazily bind to only those events that are requested through the `@Output` by parent components\n    // see https://stackoverflow.com/questions/51787972/optimal-reentering-the-ngzone-from-eventemitter-event for more info\n    createLazyEvent(eventName) {\n        return this.chartInit.pipe(switchMap((chart) => new Observable(observer => {\n            chart.on(eventName, (data) => this.ngZone.run(() => observer.next(data)));\n            return () => {\n                if (this.chart) {\n                    if (!this.chart.isDisposed()) {\n                        chart.off(eventName);\n                    }\n                }\n            };\n        })));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: NgxEchartsDirective, deps: [{ token: NGX_ECHARTS_CONFIG }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.1\", type: NgxEchartsDirective, isStandalone: true, selector: \"echarts, [echarts]\", inputs: { options: \"options\", theme: \"theme\", initOpts: \"initOpts\", merge: \"merge\", autoResize: \"autoResize\", loading: \"loading\", loadingType: \"loadingType\", loadingOpts: \"loadingOpts\" }, outputs: { chartInit: \"chartInit\", optionsError: \"optionsError\", chartClick: \"chartClick\", chartDblClick: \"chartDblClick\", chartMouseDown: \"chartMouseDown\", chartMouseMove: \"chartMouseMove\", chartMouseUp: \"chartMouseUp\", chartMouseOver: \"chartMouseOver\", chartMouseOut: \"chartMouseOut\", chartGlobalOut: \"chartGlobalOut\", chartContextMenu: \"chartContextMenu\", chartHighlight: \"chartHighlight\", chartDownplay: \"chartDownplay\", chartSelectChanged: \"chartSelectChanged\", chartLegendSelectChanged: \"chartLegendSelectChanged\", chartLegendSelected: \"chartLegendSelected\", chartLegendUnselected: \"chartLegendUnselected\", chartLegendLegendSelectAll: \"chartLegendLegendSelectAll\", chartLegendLegendInverseSelect: \"chartLegendLegendInverseSelect\", chartLegendScroll: \"chartLegendScroll\", chartDataZoom: \"chartDataZoom\", chartDataRangeSelected: \"chartDataRangeSelected\", chartGraphRoam: \"chartGraphRoam\", chartGeoRoam: \"chartGeoRoam\", chartTreeRoam: \"chartTreeRoam\", chartTimelineChanged: \"chartTimelineChanged\", chartTimelinePlayChanged: \"chartTimelinePlayChanged\", chartRestore: \"chartRestore\", chartDataViewChanged: \"chartDataViewChanged\", chartMagicTypeChanged: \"chartMagicTypeChanged\", chartGeoSelectChanged: \"chartGeoSelectChanged\", chartGeoSelected: \"chartGeoSelected\", chartGeoUnselected: \"chartGeoUnselected\", chartAxisAreaSelected: \"chartAxisAreaSelected\", chartBrush: \"chartBrush\", chartBrushEnd: \"chartBrushEnd\", chartBrushSelected: \"chartBrushSelected\", chartGlobalCursorTaken: \"chartGlobalCursorTaken\", chartRendered: \"chartRendered\", chartFinished: \"chartFinished\" }, exportAs: [\"echarts\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: NgxEchartsDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: 'echarts, [echarts]',\n                    exportAs: 'echarts',\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGX_ECHARTS_CONFIG]\n                }] }, { type: i0.ElementRef }, { type: i0.NgZone }], propDecorators: { options: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], initOpts: [{\n                type: Input\n            }], merge: [{\n                type: Input\n            }], autoResize: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], loadingType: [{\n                type: Input\n            }], loadingOpts: [{\n                type: Input\n            }], chartInit: [{\n                type: Output\n            }], optionsError: [{\n                type: Output\n            }], chartClick: [{\n                type: Output\n            }], chartDblClick: [{\n                type: Output\n            }], chartMouseDown: [{\n                type: Output\n            }], chartMouseMove: [{\n                type: Output\n            }], chartMouseUp: [{\n                type: Output\n            }], chartMouseOver: [{\n                type: Output\n            }], chartMouseOut: [{\n                type: Output\n            }], chartGlobalOut: [{\n                type: Output\n            }], chartContextMenu: [{\n                type: Output\n            }], chartHighlight: [{\n                type: Output\n            }], chartDownplay: [{\n                type: Output\n            }], chartSelectChanged: [{\n                type: Output\n            }], chartLegendSelectChanged: [{\n                type: Output\n            }], chartLegendSelected: [{\n                type: Output\n            }], chartLegendUnselected: [{\n                type: Output\n            }], chartLegendLegendSelectAll: [{\n                type: Output\n            }], chartLegendLegendInverseSelect: [{\n                type: Output\n            }], chartLegendScroll: [{\n                type: Output\n            }], chartDataZoom: [{\n                type: Output\n            }], chartDataRangeSelected: [{\n                type: Output\n            }], chartGraphRoam: [{\n                type: Output\n            }], chartGeoRoam: [{\n                type: Output\n            }], chartTreeRoam: [{\n                type: Output\n            }], chartTimelineChanged: [{\n                type: Output\n            }], chartTimelinePlayChanged: [{\n                type: Output\n            }], chartRestore: [{\n                type: Output\n            }], chartDataViewChanged: [{\n                type: Output\n            }], chartMagicTypeChanged: [{\n                type: Output\n            }], chartGeoSelectChanged: [{\n                type: Output\n            }], chartGeoSelected: [{\n                type: Output\n            }], chartGeoUnselected: [{\n                type: Output\n            }], chartAxisAreaSelected: [{\n                type: Output\n            }], chartBrush: [{\n                type: Output\n            }], chartBrushEnd: [{\n                type: Output\n            }], chartBrushSelected: [{\n                type: Output\n            }], chartGlobalCursorTaken: [{\n                type: Output\n            }], chartRendered: [{\n                type: Output\n            }], chartFinished: [{\n                type: Output\n            }] } });\n\nconst provideEcharts = () => {\n    return {\n        provide: NGX_ECHARTS_CONFIG,\n        useFactory: () => ({ echarts: () => import('echarts') }),\n    };\n};\nconst provideEchartsCore = (config) => {\n    return {\n        provide: NGX_ECHARTS_CONFIG,\n        useValue: config,\n    };\n};\nclass NgxEchartsModule {\n    static forRoot(config) {\n        return {\n            ngModule: NgxEchartsModule,\n            providers: [provideEchartsCore(config)],\n        };\n    }\n    static forChild() {\n        return {\n            ngModule: NgxEchartsModule,\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: NgxEchartsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.1\", ngImport: i0, type: NgxEchartsModule, imports: [NgxEchartsDirective], exports: [NgxEchartsDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: NgxEchartsModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: NgxEchartsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NgxEchartsDirective],\n                    exports: [NgxEchartsDirective],\n                }]\n        }] });\n\n/*\n * Public API Surface of ngx-echarts\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NGX_ECHARTS_CONFIG, NgxEchartsDirective, NgxEchartsModule, provideEcharts, provideEchartsCore };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACxG,SAASC,aAAa,EAAEC,YAAY,EAAEC,OAAO,EAAEC,cAAc,EAAEC,UAAU,QAAQ,MAAM;AACvF,SAASC,YAAY,EAAEC,SAAS,QAAQ,gBAAgB;AAExD,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAIT,aAAa,CAAC,CAAC,CAAC;IACnC,IAAI,CAACU,aAAa,GAAG,IAAIT,YAAY,CAAC,CAAC;EAC3C;EACAU,QAAQA,CAACC,OAAO,EAAE;IACd,IAAI,CAACH,OAAO,CAACI,IAAI,CAACD,OAAO,CAAC;EAC9B;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACJ,aAAa,CAACK,WAAW,CAAC,CAAC;EACpC;EACAC,QAAQA,CAACC,GAAG,EAAEC,OAAO,EAAE;IACnB,IAAI,CAACR,aAAa,CAACS,GAAG,CAAC,IAAI,CAACV,OAAO,CAACW,SAAS,CAACR,OAAO,IAAI;MACrD,IAAIA,OAAO,CAACK,GAAG,CAAC,EAAE;QACd,MAAMI,KAAK,GAAGT,OAAO,CAACK,GAAG,CAAC,CAACK,YAAY;QACvC,IAAID,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;UACvCH,OAAO,CAACG,KAAK,CAAC;QAClB;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;EACAG,GAAGA,CAACP,GAAG,EAAEC,OAAO,EAAE;IACd,IAAI,CAACR,aAAa,CAACS,GAAG,CAAC,IAAI,CAACV,OAAO,CAACW,SAAS,CAACR,OAAO,IAAI;MACrD,IAAIA,OAAO,CAACK,GAAG,CAAC,EAAE;QACd,MAAMI,KAAK,GAAGT,OAAO,CAACK,GAAG,CAAC,CAACK,YAAY;QACvCJ,OAAO,CAACG,KAAK,CAAC;MAClB;IACJ,CAAC,CAAC,CAAC;EACP;EACAI,QAAQA,CAACR,GAAG,EAAEC,OAAO,EAAE;IACnB,IAAI,CAACR,aAAa,CAACS,GAAG,CAAC,IAAI,CAACV,OAAO,CAACW,SAAS,CAACR,OAAO,IAAI;MACrD,IAAIA,OAAO,CAACK,GAAG,CAAC,IAAI,CAACL,OAAO,CAACK,GAAG,CAAC,CAACS,aAAa,CAAC,CAAC,EAAE;QAC/C,MAAML,KAAK,GAAGT,OAAO,CAACK,GAAG,CAAC,CAACK,YAAY;QACvCJ,OAAO,CAACG,KAAK,CAAC;MAClB;IACJ,CAAC,CAAC,CAAC;EACP;EACAM,gBAAgBA,CAACV,GAAG,EAAEC,OAAO,EAAE;IAC3B,IAAI,CAACR,aAAa,CAACS,GAAG,CAAC,IAAI,CAACV,OAAO,CAACW,SAAS,CAACR,OAAO,IAAI;MACrD,IAAIA,OAAO,CAACK,GAAG,CAAC,IAAI,CAACL,OAAO,CAACK,GAAG,CAAC,CAACS,aAAa,CAAC,CAAC,EAAE;QAC/C,MAAML,KAAK,GAAGT,OAAO,CAACK,GAAG,CAAC,CAACK,YAAY;QACvC,IAAID,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;UACvCH,OAAO,CAACG,KAAK,CAAC;QAClB;MACJ;IACJ,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,MAAMO,kBAAkB,GAAG,IAAInC,cAAc,CAAC,oBAAoB,CAAC;AACnE,MAAMoC,mBAAmB,CAAC;EACtBrB,WAAWA,CAACsB,MAAM,EAAEC,EAAE,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,WAAW,GAAG,SAAS;IAC5B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI/C,YAAY,CAAC,CAAC;IACnC,IAAI,CAACgD,YAAY,GAAG,IAAIhD,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACiD,UAAU,GAAG,IAAI,CAACC,eAAe,CAAC,OAAO,CAAC;IAC/C,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,eAAe,CAAC,UAAU,CAAC;IACrD,IAAI,CAACE,cAAc,GAAG,IAAI,CAACF,eAAe,CAAC,WAAW,CAAC;IACvD,IAAI,CAACG,cAAc,GAAG,IAAI,CAACH,eAAe,CAAC,WAAW,CAAC;IACvD,IAAI,CAACI,YAAY,GAAG,IAAI,CAACJ,eAAe,CAAC,SAAS,CAAC;IACnD,IAAI,CAACK,cAAc,GAAG,IAAI,CAACL,eAAe,CAAC,WAAW,CAAC;IACvD,IAAI,CAACM,aAAa,GAAG,IAAI,CAACN,eAAe,CAAC,UAAU,CAAC;IACrD,IAAI,CAACO,cAAc,GAAG,IAAI,CAACP,eAAe,CAAC,WAAW,CAAC;IACvD,IAAI,CAACQ,gBAAgB,GAAG,IAAI,CAACR,eAAe,CAAC,aAAa,CAAC;IAC3D;IACA,IAAI,CAACS,cAAc,GAAG,IAAI,CAACT,eAAe,CAAC,WAAW,CAAC;IACvD,IAAI,CAACU,aAAa,GAAG,IAAI,CAACV,eAAe,CAAC,UAAU,CAAC;IACrD,IAAI,CAACW,kBAAkB,GAAG,IAAI,CAACX,eAAe,CAAC,eAAe,CAAC;IAC/D,IAAI,CAACY,wBAAwB,GAAG,IAAI,CAACZ,eAAe,CAAC,qBAAqB,CAAC;IAC3E,IAAI,CAACa,mBAAmB,GAAG,IAAI,CAACb,eAAe,CAAC,gBAAgB,CAAC;IACjE,IAAI,CAACc,qBAAqB,GAAG,IAAI,CAACd,eAAe,CAAC,kBAAkB,CAAC;IACrE,IAAI,CAACe,0BAA0B,GAAG,IAAI,CAACf,eAAe,CAAC,iBAAiB,CAAC;IACzE,IAAI,CAACgB,8BAA8B,GAAG,IAAI,CAAChB,eAAe,CAAC,qBAAqB,CAAC;IACjF,IAAI,CAACiB,iBAAiB,GAAG,IAAI,CAACjB,eAAe,CAAC,cAAc,CAAC;IAC7D,IAAI,CAACkB,aAAa,GAAG,IAAI,CAAClB,eAAe,CAAC,UAAU,CAAC;IACrD,IAAI,CAACmB,sBAAsB,GAAG,IAAI,CAACnB,eAAe,CAAC,mBAAmB,CAAC;IACvE,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACpB,eAAe,CAAC,WAAW,CAAC;IACvD,IAAI,CAACqB,YAAY,GAAG,IAAI,CAACrB,eAAe,CAAC,SAAS,CAAC;IACnD,IAAI,CAACsB,aAAa,GAAG,IAAI,CAACtB,eAAe,CAAC,UAAU,CAAC;IACrD,IAAI,CAACuB,oBAAoB,GAAG,IAAI,CAACvB,eAAe,CAAC,iBAAiB,CAAC;IACnE,IAAI,CAACwB,wBAAwB,GAAG,IAAI,CAACxB,eAAe,CAAC,qBAAqB,CAAC;IAC3E,IAAI,CAACyB,YAAY,GAAG,IAAI,CAACzB,eAAe,CAAC,SAAS,CAAC;IACnD,IAAI,CAAC0B,oBAAoB,GAAG,IAAI,CAAC1B,eAAe,CAAC,iBAAiB,CAAC;IACnE,IAAI,CAAC2B,qBAAqB,GAAG,IAAI,CAAC3B,eAAe,CAAC,kBAAkB,CAAC;IACrE,IAAI,CAAC4B,qBAAqB,GAAG,IAAI,CAAC5B,eAAe,CAAC,kBAAkB,CAAC;IACrE,IAAI,CAAC6B,gBAAgB,GAAG,IAAI,CAAC7B,eAAe,CAAC,aAAa,CAAC;IAC3D,IAAI,CAAC8B,kBAAkB,GAAG,IAAI,CAAC9B,eAAe,CAAC,eAAe,CAAC;IAC/D,IAAI,CAAC+B,qBAAqB,GAAG,IAAI,CAAC/B,eAAe,CAAC,kBAAkB,CAAC;IACrE,IAAI,CAACgC,UAAU,GAAG,IAAI,CAAChC,eAAe,CAAC,OAAO,CAAC;IAC/C,IAAI,CAACiC,aAAa,GAAG,IAAI,CAACjC,eAAe,CAAC,UAAU,CAAC;IACrD,IAAI,CAACkC,kBAAkB,GAAG,IAAI,CAAClC,eAAe,CAAC,eAAe,CAAC;IAC/D,IAAI,CAACmC,sBAAsB,GAAG,IAAI,CAACnC,eAAe,CAAC,mBAAmB,CAAC;IACvE,IAAI,CAACoC,aAAa,GAAG,IAAI,CAACpC,eAAe,CAAC,UAAU,CAAC;IACrD,IAAI,CAACqC,aAAa,GAAG,IAAI,CAACrC,eAAe,CAAC,UAAU,CAAC;IACrD,IAAI,CAACsC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,MAAM,GAAG,IAAInF,aAAa,CAAC,CAAC,CAAC;IAClC,IAAI,CAACoF,OAAO,GAAG,IAAIlF,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACmF,YAAY,GAAG,IAAI9E,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC+E,OAAO,GAAGxD,MAAM,CAACwD,OAAO;EACjC;EACAC,WAAWA,CAAC3E,OAAO,EAAE;IACjB,IAAI,CAACyE,YAAY,CAAC1E,QAAQ,CAACC,OAAO,CAAC;EACvC;EACA4E,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,MAAM,CAACC,cAAc,EAAE;MACxB,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;IACnE;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,OAAO,CACxBS,IAAI,CAACxF,YAAY,CAAC,GAAG,EAAEF,cAAc,EAAE;MAAE2F,OAAO,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAC3E3E,SAAS,CAAC,MAAM,IAAI,CAAC4E,MAAM,CAAC,CAAC,CAAC;IACnC,IAAI,IAAI,CAAC3D,UAAU,EAAE;MACjB,IAAI,CAAC4D,QAAQ,GAAG,IAAI,CAACjE,MAAM,CAACkE,iBAAiB,CAAC,MAAM,IAAIT,MAAM,CAACC,cAAc,CAAC,MAAM;QAChF,IAAI,CAACR,gBAAgB,GAAGO,MAAM,CAACU,qBAAqB,CAAC,MAAM,IAAI,CAACf,OAAO,CAACvE,IAAI,CAAC,CAAC,CAAC;MACnF,CAAC,CAAC,CAAC;MACH,IAAI,CAACoF,QAAQ,CAACG,OAAO,CAAC,IAAI,CAACrE,EAAE,CAACsE,aAAa,CAAC;IAChD;IACA,IAAI,CAAChB,YAAY,CAAC1D,gBAAgB,CAAC,SAAS,EAAE2E,GAAG,IAAI,IAAI,CAACC,eAAe,CAACD,GAAG,CAAC,CAAC;IAC/E,IAAI,CAACjB,YAAY,CAAC1D,gBAAgB,CAAC,OAAO,EAAE2E,GAAG,IAAI,IAAI,CAACE,SAAS,CAACF,GAAG,CAAC,CAAC;IACvE,IAAI,CAACjB,YAAY,CAAC7D,GAAG,CAAC,SAAS,EAAEiF,CAAC,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,CAACD,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACpB,YAAY,CAAC5D,QAAQ,CAAC,OAAO,EAAE,MAAM,IAAI,CAACkF,YAAY,CAAC,CAAC,CAAC;EAClE;EACAC,WAAWA,CAAA,EAAG;IACVnB,MAAM,CAACoB,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;IACxC,IAAI,IAAI,CAAClB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC7E,WAAW,CAAC,CAAC;IAChC;IACA,IAAI,IAAI,CAACmE,gBAAgB,EAAE;MACvBO,MAAM,CAACsB,oBAAoB,CAAC,IAAI,CAAC7B,gBAAgB,CAAC;IACtD;IACA,IAAI,IAAI,CAACe,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACe,SAAS,CAAC,IAAI,CAACjF,EAAE,CAACsE,aAAa,CAAC;IAClD;IACA,IAAI,IAAI,CAACY,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAClG,WAAW,CAAC,CAAC;IACjC;IACA,IAAI,CAACsE,YAAY,CAACvE,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACA,OAAO,CAAC,CAAC;EAClB;EACAoG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACJ,cAAc,GAAGrB,MAAM,CAAC0B,UAAU,CAAC,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;EACnE;EACAtG,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACuG,KAAK,EAAE;MACZ,IAAI,CAAC,IAAI,CAACA,KAAK,CAACC,UAAU,CAAC,CAAC,EAAE;QAC1B,IAAI,CAACD,KAAK,CAACvG,OAAO,CAAC,CAAC;MACxB;MACA,IAAI,CAACuG,KAAK,GAAG,IAAI;IACrB;EACJ;EACA;AACJ;AACA;EACIrB,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACqB,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAACrB,MAAM,CAAC,CAAC;IACvB;EACJ;EACAU,aAAaA,CAACpE,OAAO,EAAE;IACnB,IAAI,IAAI,CAAC+E,KAAK,EAAE;MACZ/E,OAAO,GACD,IAAI,CAAC+E,KAAK,CAACE,WAAW,CAAC,IAAI,CAAChF,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC,GAC1D,IAAI,CAAC6E,KAAK,CAACG,WAAW,CAAC,CAAC;IAClC,CAAC,MACI;MACD,IAAI,CAACP,UAAU,GAAG,IAAI,CAAC9B,MAAM,CAAC/D,SAAS,CAACiG,KAAK,IAAI/E,OAAO,GAAG+E,KAAK,CAACE,WAAW,CAAC,IAAI,CAAChF,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC,GAAG6E,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC;IAC3I;EACJ;EACAhB,SAASA,CAACiB,MAAM,EAAEC,IAAI,EAAE;IACpB,IAAI,IAAI,CAACL,KAAK,EAAE;MACZ,IAAI;QACA,IAAI,CAACA,KAAK,CAACb,SAAS,CAACiB,MAAM,EAAEC,IAAI,CAAC;MACtC,CAAC,CACD,OAAOC,CAAC,EAAE;QACNC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;QAChB,IAAI,CAACjF,YAAY,CAACoF,IAAI,CAACH,CAAC,CAAC;MAC7B;IACJ;EACJ;EACA;AACJ;AACA;EACUhB,YAAYA,CAAA,EAAG;IAAA,IAAAoB,KAAA;IAAA,OAAAC,iBAAA;MACjBD,KAAI,CAACjH,OAAO,CAAC,CAAC;MACd,MAAMiH,KAAI,CAACX,SAAS,CAAC,CAAC;IAAC;EAC3B;EACAa,WAAWA,CAAA,EAAG;IACV,MAAMC,GAAG,GAAG,IAAI,CAACnG,EAAE,CAACsE,aAAa;IACjC,IAAIZ,MAAM,IAAIA,MAAM,CAAC0C,gBAAgB,EAAE;MACnC,MAAMC,IAAI,GAAG3C,MAAM,CAAC0C,gBAAgB,CAACD,GAAG,EAAE,IAAI,CAAC,CAACG,gBAAgB,CAAC,QAAQ,CAAC;MAC1E,IAAI,CAAC,CAACD,IAAI,IAAIA,IAAI,KAAK,KAAK,MAAM,CAACF,GAAG,CAACI,KAAK,CAACC,MAAM,IAAIL,GAAG,CAACI,KAAK,CAACC,MAAM,KAAK,KAAK,CAAC,EAAE;QAChFL,GAAG,CAACI,KAAK,CAACC,MAAM,GAAG,OAAO;MAC9B;IACJ;IACA;IACA;IACA,OAAO,IAAI,CAACvG,MAAM,CAACkE,iBAAiB,CAAC,MAAM;MACvC,MAAMsC,IAAI,GAAG,OAAO,IAAI,CAAClD,OAAO,KAAK,UAAU,GAAG,IAAI,CAACA,OAAO,GAAG,MAAMmD,OAAO,CAACC,OAAO,CAAC,IAAI,CAACpD,OAAO,CAAC;MACpG,OAAOkD,IAAI,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;QAAEC;MAAK,CAAC,KAAKA,IAAI,CAACV,GAAG,EAAE,IAAI,CAAChG,KAAK,EAAE,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC1E,CAAC,CAAC;EACN;EACMiF,SAASA,CAAA,EAAG;IAAA,IAAAyB,MAAA;IAAA,OAAAb,iBAAA;MACd,MAAMa,MAAI,CAACtC,eAAe,CAACsC,MAAI,CAAC5G,OAAO,CAAC;MACxC,IAAI4G,MAAI,CAACzG,KAAK,IAAIyG,MAAI,CAACxB,KAAK,EAAE;QAC1BwB,MAAI,CAACrC,SAAS,CAACqC,MAAI,CAACzG,KAAK,CAAC;MAC9B;IAAC;EACL;EACMmE,eAAeA,CAACD,GAAG,EAAE;IAAA,IAAAwC,MAAA;IAAA,OAAAd,iBAAA;MACvB,IAAI,CAAC1B,GAAG,EAAE;QACN;MACJ;MACA,IAAIwC,MAAI,CAACzB,KAAK,EAAE;QACZyB,MAAI,CAACtC,SAAS,CAACsC,MAAI,CAAC7G,OAAO,EAAE,IAAI,CAAC;MACtC,CAAC,MACI;QACD6G,MAAI,CAACzB,KAAK,SAASyB,MAAI,CAACb,WAAW,CAAC,CAAC;QACrCa,MAAI,CAAC3D,MAAM,CAACtE,IAAI,CAACiI,MAAI,CAACzB,KAAK,CAAC;QAC5ByB,MAAI,CAACrG,SAAS,CAACqF,IAAI,CAACgB,MAAI,CAACzB,KAAK,CAAC;QAC/ByB,MAAI,CAACtC,SAAS,CAACsC,MAAI,CAAC7G,OAAO,EAAE,IAAI,CAAC;MACtC;IAAC;EACL;EACA;EACA;EACAW,eAAeA,CAACmG,SAAS,EAAE;IACvB,OAAO,IAAI,CAACtG,SAAS,CAACoD,IAAI,CAACvF,SAAS,CAAE+G,KAAK,IAAK,IAAIjH,UAAU,CAAC4I,QAAQ,IAAI;MACvE3B,KAAK,CAAC4B,EAAE,CAACF,SAAS,EAAGG,IAAI,IAAK,IAAI,CAAClH,MAAM,CAACmH,GAAG,CAAC,MAAMH,QAAQ,CAACnI,IAAI,CAACqI,IAAI,CAAC,CAAC,CAAC;MACzE,OAAO,MAAM;QACT,IAAI,IAAI,CAAC7B,KAAK,EAAE;UACZ,IAAI,CAAC,IAAI,CAACA,KAAK,CAACC,UAAU,CAAC,CAAC,EAAE;YAC1BD,KAAK,CAAC+B,GAAG,CAACL,SAAS,CAAC;UACxB;QACJ;MACJ,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;EACR;EAAC,QAAAM,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,4BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwF3H,mBAAmB,EAA7BrC,EAAE,CAAAiK,iBAAA,CAA6C7H,kBAAkB,GAAjEpC,EAAE,CAAAiK,iBAAA,CAA4EjK,EAAE,CAACkK,UAAU,GAA3FlK,EAAE,CAAAiK,iBAAA,CAAsGjK,EAAE,CAACmK,MAAM;EAAA,CAA4C;EAAA,QAAAC,EAAA,GACpP,IAAI,CAACC,IAAI,kBAD8ErK,EAAE,CAAAsK,iBAAA;IAAAC,IAAA,EACJlI,mBAAmB;IAAAmI,SAAA;IAAAC,MAAA;MAAAhI,OAAA;MAAAC,KAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,UAAA;MAAAC,OAAA;MAAAC,WAAA;MAAAC,WAAA;IAAA;IAAA0H,OAAA;MAAAzH,SAAA;MAAAC,YAAA;MAAAC,UAAA;MAAAE,aAAA;MAAAC,cAAA;MAAAC,cAAA;MAAAC,YAAA;MAAAC,cAAA;MAAAC,aAAA;MAAAC,cAAA;MAAAC,gBAAA;MAAAC,cAAA;MAAAC,aAAA;MAAAC,kBAAA;MAAAC,wBAAA;MAAAC,mBAAA;MAAAC,qBAAA;MAAAC,0BAAA;MAAAC,8BAAA;MAAAC,iBAAA;MAAAC,aAAA;MAAAC,sBAAA;MAAAC,cAAA;MAAAC,YAAA;MAAAC,aAAA;MAAAC,oBAAA;MAAAC,wBAAA;MAAAC,YAAA;MAAAC,oBAAA;MAAAC,qBAAA;MAAAC,qBAAA;MAAAC,gBAAA;MAAAC,kBAAA;MAAAC,qBAAA;MAAAC,UAAA;MAAAC,aAAA;MAAAC,kBAAA;MAAAC,sBAAA;MAAAC,aAAA;MAAAC,aAAA;IAAA;IAAAkF,QAAA;IAAAC,UAAA;IAAAC,QAAA,GADjB7K,EAAE,CAAA8K,oBAAA;EAAA,EAC+1D;AACr8D;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG/K,EAAE,CAAAgL,iBAAA,CAGX3I,mBAAmB,EAAc,CAAC;IACjHkI,IAAI,EAAEpK,SAAS;IACf8K,IAAI,EAAE,CAAC;MACCL,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAE,oBAAoB;MAC9BP,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEJ,IAAI,EAAExI,SAAS;IAAEoJ,UAAU,EAAE,CAAC;MAC/CZ,IAAI,EAAEnK,MAAM;MACZ6K,IAAI,EAAE,CAAC7I,kBAAkB;IAC7B,CAAC;EAAE,CAAC,EAAE;IAAEmI,IAAI,EAAEvK,EAAE,CAACkK;EAAW,CAAC,EAAE;IAAEK,IAAI,EAAEvK,EAAE,CAACmK;EAAO,CAAC,CAAC,EAAkB;IAAE1H,OAAO,EAAE,CAAC;MACjF8H,IAAI,EAAElK;IACV,CAAC,CAAC;IAAEqC,KAAK,EAAE,CAAC;MACR6H,IAAI,EAAElK;IACV,CAAC,CAAC;IAAEsC,QAAQ,EAAE,CAAC;MACX4H,IAAI,EAAElK;IACV,CAAC,CAAC;IAAEuC,KAAK,EAAE,CAAC;MACR2H,IAAI,EAAElK;IACV,CAAC,CAAC;IAAEwC,UAAU,EAAE,CAAC;MACb0H,IAAI,EAAElK;IACV,CAAC,CAAC;IAAEyC,OAAO,EAAE,CAAC;MACVyH,IAAI,EAAElK;IACV,CAAC,CAAC;IAAE0C,WAAW,EAAE,CAAC;MACdwH,IAAI,EAAElK;IACV,CAAC,CAAC;IAAE2C,WAAW,EAAE,CAAC;MACduH,IAAI,EAAElK;IACV,CAAC,CAAC;IAAE4C,SAAS,EAAE,CAAC;MACZsH,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE4C,YAAY,EAAE,CAAC;MACfqH,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE6C,UAAU,EAAE,CAAC;MACboH,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE+C,aAAa,EAAE,CAAC;MAChBkH,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEgD,cAAc,EAAE,CAAC;MACjBiH,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEiD,cAAc,EAAE,CAAC;MACjBgH,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEkD,YAAY,EAAE,CAAC;MACf+G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEmD,cAAc,EAAE,CAAC;MACjB8G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEoD,aAAa,EAAE,CAAC;MAChB6G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEqD,cAAc,EAAE,CAAC;MACjB4G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEsD,gBAAgB,EAAE,CAAC;MACnB2G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEuD,cAAc,EAAE,CAAC;MACjB0G,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEwD,aAAa,EAAE,CAAC;MAChByG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEyD,kBAAkB,EAAE,CAAC;MACrBwG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE0D,wBAAwB,EAAE,CAAC;MAC3BuG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE2D,mBAAmB,EAAE,CAAC;MACtBsG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE4D,qBAAqB,EAAE,CAAC;MACxBqG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE6D,0BAA0B,EAAE,CAAC;MAC7BoG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE8D,8BAA8B,EAAE,CAAC;MACjCmG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE+D,iBAAiB,EAAE,CAAC;MACpBkG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEgE,aAAa,EAAE,CAAC;MAChBiG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEiE,sBAAsB,EAAE,CAAC;MACzBgG,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEkE,cAAc,EAAE,CAAC;MACjB+F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEmE,YAAY,EAAE,CAAC;MACf8F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEoE,aAAa,EAAE,CAAC;MAChB6F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEqE,oBAAoB,EAAE,CAAC;MACvB4F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEsE,wBAAwB,EAAE,CAAC;MAC3B2F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEuE,YAAY,EAAE,CAAC;MACf0F,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEwE,oBAAoB,EAAE,CAAC;MACvByF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEyE,qBAAqB,EAAE,CAAC;MACxBwF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE0E,qBAAqB,EAAE,CAAC;MACxBuF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE2E,gBAAgB,EAAE,CAAC;MACnBsF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE4E,kBAAkB,EAAE,CAAC;MACrBqF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE6E,qBAAqB,EAAE,CAAC;MACxBoF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE8E,UAAU,EAAE,CAAC;MACbmF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAE+E,aAAa,EAAE,CAAC;MAChBkF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEgF,kBAAkB,EAAE,CAAC;MACrBiF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEiF,sBAAsB,EAAE,CAAC;MACzBgF,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEkF,aAAa,EAAE,CAAC;MAChB+E,IAAI,EAAEjK;IACV,CAAC,CAAC;IAAEmF,aAAa,EAAE,CAAC;MAChB8E,IAAI,EAAEjK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8K,cAAc,GAAGA,CAAA,KAAM;EACzB,OAAO;IACHC,OAAO,EAAEjJ,kBAAkB;IAC3BkJ,UAAU,EAAEA,CAAA,MAAO;MAAExF,OAAO,EAAEA,CAAA,KAAM,MAAM,CAAC,SAAS;IAAE,CAAC;EAC3D,CAAC;AACL,CAAC;AACD,MAAMyF,kBAAkB,GAAIjJ,MAAM,IAAK;EACnC,OAAO;IACH+I,OAAO,EAAEjJ,kBAAkB;IAC3BoJ,QAAQ,EAAElJ;EACd,CAAC;AACL,CAAC;AACD,MAAMmJ,gBAAgB,CAAC;EACnB,OAAOC,OAAOA,CAACpJ,MAAM,EAAE;IACnB,OAAO;MACHqJ,QAAQ,EAAEF,gBAAgB;MAC1BG,SAAS,EAAE,CAACL,kBAAkB,CAACjJ,MAAM,CAAC;IAC1C,CAAC;EACL;EACA,OAAOuJ,QAAQA,CAAA,EAAG;IACd,OAAO;MACHF,QAAQ,EAAEF;IACd,CAAC;EACL;EAAC,QAAA5B,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAgC,yBAAA9B,CAAA;IAAA,YAAAA,CAAA,IAAwFyB,gBAAgB;EAAA,CAAkD;EAAA,QAAArB,EAAA,GACnK,IAAI,CAAC2B,IAAI,kBAxI8E/L,EAAE,CAAAgM,gBAAA;IAAAzB,IAAA,EAwISkB;EAAgB,EAAmE;EAAA,QAAAQ,EAAA,GACrL,IAAI,CAACC,IAAI,kBAzI8ElM,EAAE,CAAAmM,gBAAA,IAyI4B;AAClI;AACA;EAAA,QAAApB,SAAA,oBAAAA,SAAA,KA3IoG/K,EAAE,CAAAgL,iBAAA,CA2IXS,gBAAgB,EAAc,CAAC;IAC9GlB,IAAI,EAAEhK,QAAQ;IACd0K,IAAI,EAAE,CAAC;MACCmB,OAAO,EAAE,CAAC/J,mBAAmB,CAAC;MAC9BgK,OAAO,EAAE,CAAChK,mBAAmB;IACjC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASD,kBAAkB,EAAEC,mBAAmB,EAAEoJ,gBAAgB,EAAEL,cAAc,EAAEG,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}