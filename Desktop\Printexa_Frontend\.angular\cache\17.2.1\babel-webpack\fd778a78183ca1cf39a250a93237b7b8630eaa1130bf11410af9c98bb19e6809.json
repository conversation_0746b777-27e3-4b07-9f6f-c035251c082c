{"ast": null, "code": "import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nconst _c0 = () => [\"Home\", \"Multilevel\", \"Second\"];\nexport class Second1Component {\n  constructor() {\n    // constructor\n  }\n  static #_ = this.ɵfac = function Second1Component_Factory(t) {\n    return new (t || Second1Component)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: Second1Component,\n    selectors: [[\"app-second1\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 11,\n    vars: 4,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\", \"clearfix\"], [1, \"col-xs-12\", \"col-sm-12\", \"col-md-12\", \"col-lg-12\"], [1, \"card\"], [1, \"header\"], [1, \"body\"]],\n    template: function Second1Component_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Second Level\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(10, \"div\", 8);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Second 1\")(\"items\", i0.ɵɵpureFunction0(3, _c0))(\"active_item\", \"Second 1\");\n      }\n    },\n    dependencies: [BreadcrumbComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["BreadcrumbComponent", "Second1Component", "constructor", "_", "_2", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "Second1Component_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\multilevel\\secondlevel\\second1\\second1.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\multilevel\\secondlevel\\second1\\second1.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n\r\n@Component({\r\n  selector: 'app-second1',\r\n  templateUrl: './second1.component.html',\r\n  styleUrls: ['./second1.component.scss'],\r\n  standalone: true,\r\n  imports: [BreadcrumbComponent],\r\n})\r\nexport class Second1Component {\r\n  constructor() {\r\n    // constructor\r\n  }\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Second 1'\" [items]=\"['Home','Multilevel','Second']\" [active_item]=\"'Second 1'\">\r\n      </app-breadcrumb>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xs-12 col-sm-12 col-md-12 col-lg-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Second Level</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n\r\n            <!-- Add content here -->\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,oDAAoD;;;AASxF,OAAM,MAAOC,gBAAgB;EAC3BC,YAAA;IACE;EAAA;EACD,QAAAC,CAAA,G;qBAHUF,gBAAgB;EAAA;EAAA,QAAAG,EAAA,G;UAAhBH,gBAAgB;IAAAI,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCV7BP,EAAA,CAAAS,cAAA,iBAAyB;QAInBT,EAAA,CAAAU,SAAA,wBACiB;QACnBV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,aAA0B;QAIdT,EAAA,CAAAY,MAAA,mBAAY;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAEvBX,EAAA,CAAAU,SAAA,cAIM;QACRV,EAAA,CAAAW,YAAA,EAAM;;;QAdQX,EAAA,CAAAa,SAAA,GAAoB;QAApBb,EAAA,CAAAc,UAAA,qBAAoB,UAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA;;;mBDI9BxB,mBAAmB;IAAAyB,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}