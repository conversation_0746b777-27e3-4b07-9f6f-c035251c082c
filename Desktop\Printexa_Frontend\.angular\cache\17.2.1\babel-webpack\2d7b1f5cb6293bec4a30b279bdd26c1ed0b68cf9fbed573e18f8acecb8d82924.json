{"ast": null, "code": "import { ProduitComponent } from \"./produit/produit.component\";\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\nimport { FormProduitComponent } from \"./form-produit/form-produit.component\";\nimport { ProduitDeleteComponent } from \"./produit-delete/produit-delete.component\";\nconst newLocal = \"allProduits\";\nexport const PRODUIT_ROUTE = [{\n  path: newLocal,\n  component: ProduitComponent\n}, {\n  path: \"FormProduit\",\n  component: FormProduitComponent\n}, {\n  path: \"deleteProduit\",\n  component: ProduitDeleteComponent\n}, {\n  path: '**',\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["ProduitComponent", "Page404Component", "FormProduitComponent", "ProduitDeleteComponent", "newLocal", "PRODUIT_ROUTE", "path", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { ProduitComponent } from \"./produit/produit.component\";\r\n\r\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\r\nimport { FormProduitComponent } from \"./form-produit/form-produit.component\";\r\nimport { ProduitDeleteComponent } from \"./produit-delete/produit-delete.component\";\r\n\r\nconst newLocal = \"allProduits\";\r\nexport const PRODUIT_ROUTE: Route[] = [\r\n  {\r\n    path: newLocal,\r\n    component: ProduitComponent,\r\n  },\r\n  {\r\n    path: \"FormProduit\",\r\n    component: FormProduitComponent,\r\n  },\r\n  {\r\n    path: \"deleteProduit\",\r\n    component: ProduitDeleteComponent,\r\n  },\r\n  { path: '**', component: Page404Component },\r\n];\r\n\r\n"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,6BAA6B;AAE9D,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAElF,MAAMC,QAAQ,GAAG,aAAa;AAC9B,OAAO,MAAMC,aAAa,GAAY,CACpC;EACEC,IAAI,EAAEF,QAAQ;EACdG,SAAS,EAAEP;CACZ,EACD;EACEM,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEJ;CACZ,EACD;EAAEG,IAAI,EAAE,IAAI;EAAEC,SAAS,EAAEN;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}