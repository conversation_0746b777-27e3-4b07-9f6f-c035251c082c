{"ast": null, "code": "var overshoot = 1.70158;\nexport var backIn = function custom(s) {\n  s = +s;\n  function backIn(t) {\n    return (t = +t) * t * (s * (t - 1) + t);\n  }\n  backIn.overshoot = custom;\n  return backIn;\n}(overshoot);\nexport var backOut = function custom(s) {\n  s = +s;\n  function backOut(t) {\n    return --t * t * ((t + 1) * s + t) + 1;\n  }\n  backOut.overshoot = custom;\n  return backOut;\n}(overshoot);\nexport var backInOut = function custom(s) {\n  s = +s;\n  function backInOut(t) {\n    return ((t *= 2) < 1 ? t * t * ((s + 1) * t - s) : (t -= 2) * t * ((s + 1) * t + s) + 2) / 2;\n  }\n  backInOut.overshoot = custom;\n  return backInOut;\n}(overshoot);", "map": {"version": 3, "names": ["overshoot", "backIn", "custom", "s", "t", "backOut", "backInOut"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/d3-ease/src/back.js"], "sourcesContent": ["var overshoot = 1.70158;\n\nexport var backIn = (function custom(s) {\n  s = +s;\n\n  function backIn(t) {\n    return (t = +t) * t * (s * (t - 1) + t);\n  }\n\n  backIn.overshoot = custom;\n\n  return backIn;\n})(overshoot);\n\nexport var backOut = (function custom(s) {\n  s = +s;\n\n  function backOut(t) {\n    return --t * t * ((t + 1) * s + t) + 1;\n  }\n\n  backOut.overshoot = custom;\n\n  return backOut;\n})(overshoot);\n\nexport var backInOut = (function custom(s) {\n  s = +s;\n\n  function backInOut(t) {\n    return ((t *= 2) < 1 ? t * t * ((s + 1) * t - s) : (t -= 2) * t * ((s + 1) * t + s) + 2) / 2;\n  }\n\n  backInOut.overshoot = custom;\n\n  return backInOut;\n})(overshoot);\n"], "mappings": "AAAA,IAAIA,SAAS,GAAG,OAAO;AAEvB,OAAO,IAAIC,MAAM,GAAI,SAASC,MAAMA,CAACC,CAAC,EAAE;EACtCA,CAAC,GAAG,CAACA,CAAC;EAEN,SAASF,MAAMA,CAACG,CAAC,EAAE;IACjB,OAAO,CAACA,CAAC,GAAG,CAACA,CAAC,IAAIA,CAAC,IAAID,CAAC,IAAIC,CAAC,GAAG,CAAC,CAAC,GAAGA,CAAC,CAAC;EACzC;EAEAH,MAAM,CAACD,SAAS,GAAGE,MAAM;EAEzB,OAAOD,MAAM;AACf,CAAC,CAAED,SAAS,CAAC;AAEb,OAAO,IAAIK,OAAO,GAAI,SAASH,MAAMA,CAACC,CAAC,EAAE;EACvCA,CAAC,GAAG,CAACA,CAAC;EAEN,SAASE,OAAOA,CAACD,CAAC,EAAE;IAClB,OAAO,EAAEA,CAAC,GAAGA,CAAC,IAAI,CAACA,CAAC,GAAG,CAAC,IAAID,CAAC,GAAGC,CAAC,CAAC,GAAG,CAAC;EACxC;EAEAC,OAAO,CAACL,SAAS,GAAGE,MAAM;EAE1B,OAAOG,OAAO;AAChB,CAAC,CAAEL,SAAS,CAAC;AAEb,OAAO,IAAIM,SAAS,GAAI,SAASJ,MAAMA,CAACC,CAAC,EAAE;EACzCA,CAAC,GAAG,CAACA,CAAC;EAEN,SAASG,SAASA,CAACF,CAAC,EAAE;IACpB,OAAO,CAAC,CAACA,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAGA,CAAC,IAAI,CAACD,CAAC,GAAG,CAAC,IAAIC,CAAC,GAAGD,CAAC,CAAC,GAAG,CAACC,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAACD,CAAC,GAAG,CAAC,IAAIC,CAAC,GAAGD,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;EAC9F;EAEAG,SAAS,CAACN,SAAS,GAAGE,MAAM;EAE5B,OAAOI,SAAS;AAClB,CAAC,CAAEN,SAAS,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}