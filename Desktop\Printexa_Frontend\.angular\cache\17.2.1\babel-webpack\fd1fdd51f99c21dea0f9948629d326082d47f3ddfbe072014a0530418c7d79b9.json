{"ast": null, "code": "import { formatDate } from '@angular/common';\nexport class AllHoliday {\n  constructor(holiday) {\n    {\n      this.id = holiday.id || this.getRandomID();\n      this.hName = holiday.hName || '';\n      this.shift = holiday.shift || '';\n      this.details = holiday.details || '';\n      this.date = formatDate(new Date(), 'yyyy-MM-dd', 'en') || '';\n      this.location = holiday.location || '';\n    }\n  }\n  getRandomID() {\n    const S4 = () => {\n      return (1 + Math.random()) * 0x10000 | 0;\n    };\n    return S4() + S4();\n  }\n}", "map": {"version": 3, "names": ["formatDate", "AllHoliday", "constructor", "holiday", "id", "getRandomID", "hName", "shift", "details", "date", "Date", "location", "S4", "Math", "random"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\holidays\\all-holidays\\all-holidays.model.ts"], "sourcesContent": ["import { formatDate } from '@angular/common';\r\nexport class AllHoliday {\r\n  id: number;\r\n  hName: string;\r\n  shift: string;\r\n  details: string;\r\n  date: string;\r\n  location: string;\r\n  constructor(holiday: AllHoliday) {\r\n    {\r\n      this.id = holiday.id || this.getRandomID();\r\n      this.hName = holiday.hName || '';\r\n      this.shift = holiday.shift || '';\r\n      this.details = holiday.details || '';\r\n      this.date = formatDate(new Date(), 'yyyy-MM-dd', 'en') || '';\r\n      this.location = holiday.location || '';\r\n    }\r\n  }\r\n  public getRandomID(): number {\r\n    const S4 = () => {\r\n      return ((1 + Math.random()) * 0x10000) | 0;\r\n    };\r\n    return S4() + S4();\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAM,MAAOC,UAAU;EAOrBC,YAAYC,OAAmB;IAC7B;MACE,IAAI,CAACC,EAAE,GAAGD,OAAO,CAACC,EAAE,IAAI,IAAI,CAACC,WAAW,EAAE;MAC1C,IAAI,CAACC,KAAK,GAAGH,OAAO,CAACG,KAAK,IAAI,EAAE;MAChC,IAAI,CAACC,KAAK,GAAGJ,OAAO,CAACI,KAAK,IAAI,EAAE;MAChC,IAAI,CAACC,OAAO,GAAGL,OAAO,CAACK,OAAO,IAAI,EAAE;MACpC,IAAI,CAACC,IAAI,GAAGT,UAAU,CAAC,IAAIU,IAAI,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC,IAAI,EAAE;MAC5D,IAAI,CAACC,QAAQ,GAAGR,OAAO,CAACQ,QAAQ,IAAI,EAAE;;EAE1C;EACON,WAAWA,CAAA;IAChB,MAAMO,EAAE,GAAGA,CAAA,KAAK;MACd,OAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,IAAI,OAAO,GAAI,CAAC;IAC5C,CAAC;IACD,OAAOF,EAAE,EAAE,GAAGA,EAAE,EAAE;EACpB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}