{"ast": null, "code": "import { MatButtonModule } from '@angular/material/button';\nimport { NgxGaugeModule } from 'ngx-gauge';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-gauge\";\nimport * as i2 from \"@angular/material/button\";\nconst _c0 = () => [\"Home\", \"Charts\"];\nexport class GaugeComponent {\n  constructor() {\n    this.gaugeValue = 68;\n    this.gaugeSize = 120;\n    this.guageThick = 5;\n    this.guageType1 = 'full';\n    this.guageType2 = 'semi';\n    this.guageType3 = 'arch';\n    this.dynamicGaugeDemoValue = 10.2;\n    this.gaugeValues = {\n      1: 100,\n      2: 50,\n      3: 50,\n      4: 50,\n      5: 50,\n      6: 50,\n      7: 50\n    };\n    this.markerConfig = {\n      '0': {\n        color: '#555',\n        size: 8,\n        label: '0',\n        type: 'line'\n      },\n      '15': {\n        color: '#555',\n        size: 4,\n        type: 'line'\n      },\n      '30': {\n        color: '#555',\n        size: 8,\n        label: '30',\n        type: 'line'\n      },\n      '40': {\n        color: '#555',\n        size: 4,\n        type: 'line'\n      },\n      '50': {\n        color: '#555',\n        size: 8,\n        label: '50',\n        type: 'line'\n      },\n      '60': {\n        color: '#555',\n        size: 4,\n        type: 'line'\n      },\n      '70': {\n        color: '#555',\n        size: 8,\n        label: '70',\n        type: 'line'\n      },\n      '85': {\n        color: '#555',\n        size: 4,\n        type: 'line'\n      },\n      '100': {\n        color: '#555',\n        size: 8,\n        label: '100',\n        type: 'line'\n      }\n    };\n    this.percentageValue = function (value) {\n      return `${Math.round(value)}`;\n    };\n  }\n  onUpdateClick() {\n    this.dynamicGaugeDemoValue = Math.round(Math.random() * 1000) / 10;\n  }\n  static #_ = this.ɵfac = function GaugeComponent_Factory(t) {\n    return new (t || GaugeComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: GaugeComponent,\n    selectors: [[\"app-gauge\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 113,\n    vars: 50,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\", \"clearfix\"], [1, \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"card\"], [1, \"header\"], [1, \"body\"], [1, \"row\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-12\", \"col-sm-12\"], [1, \"text-center\"], [1, \"guage-chart-center\", 3, \"value\", \"size\", \"type\", \"thick\"], [1, \"font-15\"], [\"type\", \"arch\", \"cap\", \"butt\", \"label\", \"Speed\", \"append\", \"mph\", 1, \"guage-chart-center\", 3, \"size\", \"thick\", \"value\"], [\"type\", \"arch\", \"cap\", \"round\", \"label\", \"Speed\", \"append\", \"mph\", 1, \"guage-chart-center\", 3, \"size\", \"thick\", \"value\"], [\"type\", \"arch\", \"label\", \"Speed\", \"append\", \"mph\", 1, \"guage-chart-center\", 3, \"value\", \"thick\", \"size\"], [\"type\", \"arch\", \"cap\", \"round\", \"label\", \"I/O Utilization\", \"append\", \"%\", 3, \"value\", \"thick\", \"size\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [\"type\", \"arch\", \"cap\", \"round\", \"label\", \"Speed\", \"append\", \"mph\", \"foregroundColor\", \"#2980b9\", \"backgroundColor\", \"#ecf0f1\", 3, \"size\", \"thick\", \"value\"], [\"type\", \"arch\", \"cap\", \"round\", \"label\", \"Speed\", \"append\", \"mph\", \"foregroundColor\", \"#2ecc71\", \"backgroundColor\", \"#ecf0f1\", 3, \"size\", \"thick\", \"value\"], [\"type\", \"arch\", \"cap\", \"round\", \"label\", \"Speed\", \"append\", \"mph\", \"foregroundColor\", \"#e74c3c\", \"backgroundColor\", \"#ecf0f1\", 3, \"size\", \"thick\", \"value\"], [\"type\", \"arch\", \"cap\", \"butt\", \"label\", \"Revenue\", \"append\", \"%\", 1, \"p-3\", \"m-3\", 3, \"size\", \"thick\", \"value\", \"markers\"]],\n    template: function GaugeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Gauge Types\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"h5\", 11);\n        i0.ɵɵtext(14, \"Full\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"ngx-gauge\", 12)(16, \"ngx-gauge-append\")(17, \"span\", 13);\n        i0.ɵɵtext(18, \"'km/hr'\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"ngx-gauge-label\");\n        i0.ɵɵtext(20, \" 'Speed' \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"ngx-gauge-value\");\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(23, \"div\", 10)(24, \"h5\", 11);\n        i0.ɵɵtext(25, \"Semi\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"ngx-gauge\", 12)(27, \"ngx-gauge-append\")(28, \"span\", 13);\n        i0.ɵɵtext(29, \"'km/hr'\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"ngx-gauge-label\");\n        i0.ɵɵtext(31, \" 'Speed' \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"ngx-gauge-value\");\n        i0.ɵɵtext(33);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(34, \"div\", 10)(35, \"h5\", 11);\n        i0.ɵɵtext(36, \"Arch\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"ngx-gauge\", 12)(38, \"ngx-gauge-append\")(39, \"span\", 13);\n        i0.ɵɵtext(40, \"'km/hr'\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(41, \"ngx-gauge-label\");\n        i0.ɵɵtext(42, \" 'Speed' \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"ngx-gauge-value\");\n        i0.ɵɵtext(44);\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(45, \"div\", 4)(46, \"div\", 5)(47, \"div\", 6)(48, \"div\", 7)(49, \"h2\");\n        i0.ɵɵtext(50, \"Gauge Styles\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(51, \"div\", 8)(52, \"div\", 9)(53, \"div\", 10)(54, \"h5\", 11);\n        i0.ɵɵtext(55, \"butt \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(56, \"ngx-gauge\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"div\", 10)(58, \"h5\", 11);\n        i0.ɵɵtext(59, \"round \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(60, \"ngx-gauge\", 15);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(61, \"div\", 4)(62, \"div\", 5)(63, \"div\", 6)(64, \"div\", 7)(65, \"h2\");\n        i0.ɵɵtext(66, \"Gauge Thickness\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(67, \"div\", 8)(68, \"div\", 9)(69, \"div\", 10)(70, \"h5\", 11);\n        i0.ɵɵtext(71, \"thickness = 5 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(72, \"ngx-gauge\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(73, \"div\", 10)(74, \"h5\", 11);\n        i0.ɵɵtext(75, \"thickness = 10 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(76, \"ngx-gauge\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(77, \"div\", 10)(78, \"h5\", 11);\n        i0.ɵɵtext(79, \"thickness = 15 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(80, \"ngx-gauge\", 16);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(81, \"div\", 4)(82, \"div\", 5)(83, \"div\", 6)(84, \"div\", 7)(85, \"h2\");\n        i0.ɵɵtext(86, \"Dynamic Gauge Value\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(87, \"div\", 8);\n        i0.ɵɵelement(88, \"ngx-gauge\", 17);\n        i0.ɵɵelementStart(89, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function GaugeComponent_Template_button_click_89_listener() {\n          return ctx.onUpdateClick();\n        });\n        i0.ɵɵtext(90, \"Update\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(91, \"div\", 4)(92, \"div\", 5)(93, \"div\", 6)(94, \"div\", 7)(95, \"h2\");\n        i0.ɵɵtext(96, \"Gauge Themes\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(97, \"div\", 8)(98, \"div\", 9)(99, \"div\", 10);\n        i0.ɵɵelement(100, \"ngx-gauge\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(101, \"div\", 10);\n        i0.ɵɵelement(102, \"ngx-gauge\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"div\", 10);\n        i0.ɵɵelement(104, \"ngx-gauge\", 21);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(105, \"div\", 4)(106, \"div\", 5)(107, \"div\", 6)(108, \"div\", 7)(109, \"h2\");\n        i0.ɵɵtext(110, \"Adding Markers to the Gauge\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(111, \"div\", 8);\n        i0.ɵɵelement(112, \"ngx-gauge\", 22);\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Gauge\")(\"items\", i0.ɵɵpureFunction0(49, _c0))(\"active_item\", \"Gauge\");\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"value\", ctx.gaugeValue)(\"size\", ctx.gaugeSize)(\"type\", ctx.guageType1)(\"thick\", ctx.guageThick);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\" \", ctx.gaugeValue, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"value\", ctx.gaugeValue)(\"size\", ctx.gaugeSize)(\"type\", ctx.guageType2)(\"thick\", ctx.guageThick);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\" \", ctx.gaugeValue, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"value\", ctx.gaugeValue)(\"size\", ctx.gaugeSize)(\"type\", ctx.guageType3)(\"thick\", ctx.guageThick);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\" \", ctx.gaugeValue, \" \");\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"size\", 180)(\"thick\", 15)(\"value\", 68);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"size\", 180)(\"thick\", 15)(\"value\", 68);\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"value\", 68)(\"thick\", 5)(\"size\", 150);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"value\", 68)(\"thick\", 10)(\"size\", 150);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"value\", 68)(\"thick\", 15)(\"size\", 150);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"value\", ctx.dynamicGaugeDemoValue)(\"thick\", 13)(\"size\", 200);\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"size\", 150)(\"thick\", 7)(\"value\", 68.2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"size\", 150)(\"thick\", 7)(\"value\", 68.2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"size\", 150)(\"thick\", 7)(\"value\", 68.2);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"size\", 180)(\"thick\", 10)(\"value\", 68.2)(\"markers\", ctx.markerConfig);\n      }\n    },\n    dependencies: [BreadcrumbComponent, NgxGaugeModule, i1.NgxGauge, i1.NgxGaugeAppend, i1.NgxGaugeValue, i1.NgxGaugeLabel, MatButtonModule, i2.MatButton],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["MatButtonModule", "NgxGaugeModule", "BreadcrumbComponent", "GaugeComponent", "constructor", "gaugeValue", "gaugeSize", "guageThick", "guageType1", "guageType2", "guageType3", "dynamicGaugeDemoValue", "gaugeValues", "markerConfig", "color", "size", "label", "type", "percentageValue", "value", "Math", "round", "onUpdateClick", "random", "_", "_2", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "GaugeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "GaugeComponent_Template_button_click_89_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1", "i1", "NgxGauge", "NgxGaugeAppend", "NgxGaugeValue", "NgxGaugeLabel", "i2", "MatButton", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\mian\\src\\app\\charts\\gauge\\gauge.component.ts", "C:\\Users\\<USER>\\Desktop\\mian\\src\\app\\charts\\gauge\\gauge.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { NgxGaugeType } from 'ngx-gauge/gauge/gauge';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { NgxGaugeModule } from 'ngx-gauge';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n\r\ninterface GaugeValues {\r\n  [key: number]: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-gauge',\r\n  templateUrl: './gauge.component.html',\r\n  styleUrls: ['./gauge.component.scss'],\r\n  standalone: true,\r\n  imports: [BreadcrumbComponent, NgxGaugeModule, MatButtonModule],\r\n})\r\nexport class GaugeComponent {\r\n  gaugeValue = 68;\r\n  gaugeSize = 120;\r\n  guageThick = 5;\r\n\r\n  guageType1 = 'full' as NgxGaugeType;\r\n  guageType2 = 'semi' as NgxGaugeType;\r\n  guageType3 = 'arch' as NgxGaugeType;\r\n\r\n  dynamicGaugeDemoValue = 10.2;\r\n\r\n  constructor() {\r\n    this.percentageValue = function (value: number): string {\r\n      return `${Math.round(value)}`;\r\n    };\r\n  }\r\n\r\n  percentageValue: (value: number) => string;\r\n  gaugeValues: GaugeValues = {\r\n    1: 100,\r\n    2: 50,\r\n    3: 50,\r\n    4: 50,\r\n    5: 50,\r\n    6: 50,\r\n    7: 50,\r\n  };\r\n\r\n  onUpdateClick() {\r\n    this.dynamicGaugeDemoValue = Math.round(Math.random() * 1000) / 10;\r\n  }\r\n\r\n  markerConfig = {\r\n    '0': { color: '#555', size: 8, label: '0', type: 'line' },\r\n    '15': { color: '#555', size: 4, type: 'line' },\r\n    '30': { color: '#555', size: 8, label: '30', type: 'line' },\r\n    '40': { color: '#555', size: 4, type: 'line' },\r\n    '50': { color: '#555', size: 8, label: '50', type: 'line' },\r\n    '60': { color: '#555', size: 4, type: 'line' },\r\n    '70': { color: '#555', size: 8, label: '70', type: 'line' },\r\n    '85': { color: '#555', size: 4, type: 'line' },\r\n    '100': { color: '#555', size: 8, label: '100', type: 'line' },\r\n  };\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Gauge'\" [items]=\"['Home','Charts']\" [active_item]=\"'Gauge'\">\r\n      </app-breadcrumb>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Gauge Types</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"row\">\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <h5 class=\"text-center\">Full</h5>\r\n                <ngx-gauge [value]=\"gaugeValue\" [size]=\"gaugeSize\" [type]=\"guageType1\" [thick]=\"guageThick\"\r\n                  class=\"guage-chart-center\">\r\n                  <ngx-gauge-append>\r\n                    <span class=\"font-15\">'km/hr'</span>\r\n                  </ngx-gauge-append>\r\n                  <ngx-gauge-label>\r\n                    'Speed'\r\n                  </ngx-gauge-label>\r\n                  <ngx-gauge-value>\r\n                    {{gaugeValue}}\r\n                  </ngx-gauge-value>\r\n                </ngx-gauge>\r\n              </div>\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <h5 class=\"text-center\">Semi</h5>\r\n                <ngx-gauge [value]=\"gaugeValue\" [size]=\"gaugeSize\" [type]=\"guageType2\" [thick]=\"guageThick\"\r\n                  class=\"guage-chart-center\">\r\n                  <ngx-gauge-append>\r\n                    <span class=\"font-15\">'km/hr'</span>\r\n                  </ngx-gauge-append>\r\n                  <ngx-gauge-label>\r\n                    'Speed'\r\n                  </ngx-gauge-label>\r\n                  <ngx-gauge-value>\r\n                    {{gaugeValue}}\r\n                  </ngx-gauge-value>\r\n                </ngx-gauge>\r\n              </div>\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <h5 class=\"text-center\">Arch</h5>\r\n                <ngx-gauge [value]=\"gaugeValue\" [size]=\"gaugeSize\" [type]=\"guageType3\" [thick]=\"guageThick\"\r\n                  class=\"guage-chart-center\">\r\n                  <ngx-gauge-append>\r\n                    <span class=\"font-15\">'km/hr'</span>\r\n                  </ngx-gauge-append>\r\n                  <ngx-gauge-label>\r\n                    'Speed'\r\n                  </ngx-gauge-label>\r\n                  <ngx-gauge-value>\r\n                    {{gaugeValue}}\r\n                  </ngx-gauge-value>\r\n                </ngx-gauge>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Gauge Styles</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"row\">\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <h5 class=\"text-center\">butt </h5>\r\n                <ngx-gauge [size]=\"180\" type=\"arch\" [thick]=\"15\" \r\n                [value]=\"68\" cap=\"butt\" label=\"Speed\" \r\n                append=\"mph\" class=\"guage-chart-center\"></ngx-gauge>\r\n              </div>\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <h5 class=\"text-center\">round </h5>\r\n                <ngx-gauge [size]=\"180\" type=\"arch\" [thick]=\"15\" \r\n                [value]=\"68\" cap=\"round\" label=\"Speed\" \r\n                append=\"mph\" class=\"guage-chart-center\"></ngx-gauge>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Gauge Thickness</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"row\">\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <h5 class=\"text-center\">thickness = 5 </h5>\r\n                <ngx-gauge type=\"arch\" [value]=\"68\" [thick]=\"5\" [size]=\"150\" \r\n                label=\"Speed\" append=\"mph\" class=\"guage-chart-center\"></ngx-gauge>\r\n              </div>\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <h5 class=\"text-center\">thickness = 10 </h5>\r\n                <ngx-gauge type=\"arch\" [value]=\"68\" [thick]=\"10\" [size]=\"150\" \r\n                label=\"Speed\" append=\"mph\" class=\"guage-chart-center\"></ngx-gauge>\r\n              </div>\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <h5 class=\"text-center\">thickness = 15 </h5>\r\n                <ngx-gauge type=\"arch\" [value]=\"68\" [thick]=\"15\" [size]=\"150\" \r\n                label=\"Speed\" append=\"mph\" class=\"guage-chart-center\"></ngx-gauge>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Dynamic Gauge Value</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <ngx-gauge [value]=\"dynamicGaugeDemoValue\"\r\n              type=\"arch\" \r\n              [thick]=\"13\" \r\n              cap=\"round\" \r\n              [size]=\"200\" \r\n              label=\"I/O Utilization\" \r\n              append=\"%\">\r\n            </ngx-gauge>\r\n            <button mat-raised-button color=\"accent\" (click)=\"onUpdateClick()\">Update</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Gauge Themes</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"row\">\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <!--blue theme -->\r\n                <ngx-gauge [size]=\"150\" type=\"arch\" [thick]=\"7\" [value]=\"68.2\" \r\n                cap=\"round\" label=\"Speed\" append=\"mph\" \r\n                foregroundColor=\"#2980b9\"\r\n                backgroundColor=\"#ecf0f1\"></ngx-gauge>\r\n              </div>\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n               <!-- green theme -->\r\n                <ngx-gauge [size]=\"150\" type=\"arch\" [thick]=\"7\" [value]=\"68.2\" \r\n                cap=\"round\" label=\"Speed\" append=\"mph\" \r\n                foregroundColor=\"#2ecc71\"\r\n                backgroundColor=\"#ecf0f1\"></ngx-gauge>\r\n              </div>\r\n              <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n                <!-- red theme -->\r\n                <ngx-gauge [size]=\"150\" type=\"arch\" [thick]=\"7\" [value]=\"68.2\" \r\n                cap=\"round\" label=\"Speed\" append=\"mph\" \r\n                foregroundColor=\"#e74c3c\"\r\n                backgroundColor=\"#ecf0f1\"></ngx-gauge>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Adding Markers to the Gauge</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <ngx-gauge [size]=\"180\" \r\n              type=\"arch\" \r\n              [thick]=\"10\" \r\n              [value]=\"68.2\" \r\n              cap=\"butt\" \r\n              label=\"Revenue\" \r\n              append=\"%\"\r\n              [markers]=\"markerConfig\" class=\"p-3 m-3\">\r\n            </ngx-gauge>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</section>"], "mappings": "AAEA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,mBAAmB,QAAQ,oDAAoD;;;;;AAaxF,OAAM,MAAOC,cAAc;EAWzBC,YAAA;IAVA,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,SAAS,GAAG,GAAG;IACf,KAAAC,UAAU,GAAG,CAAC;IAEd,KAAAC,UAAU,GAAG,MAAsB;IACnC,KAAAC,UAAU,GAAG,MAAsB;IACnC,KAAAC,UAAU,GAAG,MAAsB;IAEnC,KAAAC,qBAAqB,GAAG,IAAI;IAS5B,KAAAC,WAAW,GAAgB;MACzB,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,EAAE;MACL,CAAC,EAAE,EAAE;MACL,CAAC,EAAE,EAAE;MACL,CAAC,EAAE,EAAE;MACL,CAAC,EAAE,EAAE;MACL,CAAC,EAAE;KACJ;IAMD,KAAAC,YAAY,GAAG;MACb,GAAG,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAM,CAAE;MACzD,IAAI,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEE,IAAI,EAAE;MAAM,CAAE;MAC9C,IAAI,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAM,CAAE;MAC3D,IAAI,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEE,IAAI,EAAE;MAAM,CAAE;MAC9C,IAAI,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAM,CAAE;MAC3D,IAAI,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEE,IAAI,EAAE;MAAM,CAAE;MAC9C,IAAI,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAM,CAAE;MAC3D,IAAI,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEE,IAAI,EAAE;MAAM,CAAE;MAC9C,KAAK,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAM;KAC5D;IA9BC,IAAI,CAACC,eAAe,GAAG,UAAUC,KAAa;MAC5C,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC,EAAE;IAC/B,CAAC;EACH;EAaAG,aAAaA,CAAA;IACX,IAAI,CAACX,qBAAqB,GAAGS,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;EACpE;EAAC,QAAAC,CAAA,G;qBA9BUrB,cAAc;EAAA;EAAA,QAAAsB,EAAA,G;UAAdtB,cAAc;IAAAuB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjB3BP,EAAA,CAAAS,cAAA,iBAAyB;QAInBT,EAAA,CAAAU,SAAA,wBACiB;QACnBV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,aAA0B;QAIdT,EAAA,CAAAY,MAAA,kBAAW;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAEtBX,EAAA,CAAAS,cAAA,cAAkB;QAGYT,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QACjCX,EAAA,CAAAS,cAAA,qBAC6B;QAEHT,EAAA,CAAAY,MAAA,eAAO;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QAEtCX,EAAA,CAAAS,cAAA,uBAAiB;QACfT,EAAA,CAAAY,MAAA,iBACF;QAAAZ,EAAA,CAAAW,YAAA,EAAkB;QAClBX,EAAA,CAAAS,cAAA,uBAAiB;QACfT,EAAA,CAAAY,MAAA,IACF;QAAAZ,EAAA,CAAAW,YAAA,EAAkB;QAGtBX,EAAA,CAAAS,cAAA,eAAmD;QACzBT,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QACjCX,EAAA,CAAAS,cAAA,qBAC6B;QAEHT,EAAA,CAAAY,MAAA,eAAO;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QAEtCX,EAAA,CAAAS,cAAA,uBAAiB;QACfT,EAAA,CAAAY,MAAA,iBACF;QAAAZ,EAAA,CAAAW,YAAA,EAAkB;QAClBX,EAAA,CAAAS,cAAA,uBAAiB;QACfT,EAAA,CAAAY,MAAA,IACF;QAAAZ,EAAA,CAAAW,YAAA,EAAkB;QAGtBX,EAAA,CAAAS,cAAA,eAAmD;QACzBT,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QACjCX,EAAA,CAAAS,cAAA,qBAC6B;QAEHT,EAAA,CAAAY,MAAA,eAAO;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QAEtCX,EAAA,CAAAS,cAAA,uBAAiB;QACfT,EAAA,CAAAY,MAAA,iBACF;QAAAZ,EAAA,CAAAW,YAAA,EAAkB;QAClBX,EAAA,CAAAS,cAAA,uBAAiB;QACfT,EAAA,CAAAY,MAAA,IACF;QAAAZ,EAAA,CAAAW,YAAA,EAAkB;QAShCX,EAAA,CAAAS,cAAA,cAA0B;QAIdT,EAAA,CAAAY,MAAA,oBAAY;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAEvBX,EAAA,CAAAS,cAAA,cAAkB;QAGYT,EAAA,CAAAY,MAAA,aAAK;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAClCX,EAAA,CAAAU,SAAA,qBAEoD;QACtDV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,eAAmD;QACzBT,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QACnCX,EAAA,CAAAU,SAAA,qBAEoD;QACtDV,EAAA,CAAAW,YAAA,EAAM;QAOhBX,EAAA,CAAAS,cAAA,cAA0B;QAIdT,EAAA,CAAAY,MAAA,uBAAe;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAE1BX,EAAA,CAAAS,cAAA,cAAkB;QAGYT,EAAA,CAAAY,MAAA,sBAAc;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAC3CX,EAAA,CAAAU,SAAA,qBACkE;QACpEV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,eAAmD;QACzBT,EAAA,CAAAY,MAAA,uBAAe;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAC5CX,EAAA,CAAAU,SAAA,qBACkE;QACpEV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,eAAmD;QACzBT,EAAA,CAAAY,MAAA,uBAAe;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAC5CX,EAAA,CAAAU,SAAA,qBACkE;QACpEV,EAAA,CAAAW,YAAA,EAAM;QAOhBX,EAAA,CAAAS,cAAA,cAA0B;QAIdT,EAAA,CAAAY,MAAA,2BAAmB;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAE9BX,EAAA,CAAAS,cAAA,cAAkB;QAChBT,EAAA,CAAAU,SAAA,qBAOY;QACZV,EAAA,CAAAS,cAAA,kBAAmE;QAA1BT,EAAA,CAAAa,UAAA,mBAAAC,iDAAA;UAAA,OAASN,GAAA,CAAAf,aAAA,EAAe;QAAA,EAAC;QAACO,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QAM1FX,EAAA,CAAAS,cAAA,cAA0B;QAIdT,EAAA,CAAAY,MAAA,oBAAY;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAEvBX,EAAA,CAAAS,cAAA,cAAkB;QAIZT,EAAA,CAAAU,SAAA,sBAGsC;QACxCV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,gBAAmD;QAEjDT,EAAA,CAAAU,SAAA,sBAGsC;QACxCV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,gBAAmD;QAEjDT,EAAA,CAAAU,SAAA,sBAGsC;QACxCV,EAAA,CAAAW,YAAA,EAAM;QAQhBX,EAAA,CAAAS,cAAA,eAA0B;QAIdT,EAAA,CAAAY,MAAA,oCAA2B;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAEtCX,EAAA,CAAAS,cAAA,eAAkB;QAChBT,EAAA,CAAAU,SAAA,sBAQY;QACdV,EAAA,CAAAW,YAAA,EAAM;;;QA9LMX,EAAA,CAAAe,SAAA,GAAiB;QAAjBf,EAAA,CAAAgB,UAAA,kBAAiB,UAAAhB,EAAA,CAAAiB,eAAA,KAAAC,GAAA;QAaZlB,EAAA,CAAAe,SAAA,IAAoB;QAApBf,EAAA,CAAAgB,UAAA,UAAAR,GAAA,CAAAhC,UAAA,CAAoB,SAAAgC,GAAA,CAAA/B,SAAA,UAAA+B,GAAA,CAAA7B,UAAA,WAAA6B,GAAA,CAAA9B,UAAA;QAS3BsB,EAAA,CAAAe,SAAA,GACF;QADEf,EAAA,CAAAmB,kBAAA,MAAAX,GAAA,CAAAhC,UAAA,MACF;QAKSwB,EAAA,CAAAe,SAAA,GAAoB;QAApBf,EAAA,CAAAgB,UAAA,UAAAR,GAAA,CAAAhC,UAAA,CAAoB,SAAAgC,GAAA,CAAA/B,SAAA,UAAA+B,GAAA,CAAA5B,UAAA,WAAA4B,GAAA,CAAA9B,UAAA;QAS3BsB,EAAA,CAAAe,SAAA,GACF;QADEf,EAAA,CAAAmB,kBAAA,MAAAX,GAAA,CAAAhC,UAAA,MACF;QAKSwB,EAAA,CAAAe,SAAA,GAAoB;QAApBf,EAAA,CAAAgB,UAAA,UAAAR,GAAA,CAAAhC,UAAA,CAAoB,SAAAgC,GAAA,CAAA/B,SAAA,UAAA+B,GAAA,CAAA3B,UAAA,WAAA2B,GAAA,CAAA9B,UAAA;QAS3BsB,EAAA,CAAAe,SAAA,GACF;QADEf,EAAA,CAAAmB,kBAAA,MAAAX,GAAA,CAAAhC,UAAA,MACF;QAmBSwB,EAAA,CAAAe,SAAA,IAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY;QAMZhB,EAAA,CAAAe,SAAA,GAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY;QAoBAhB,EAAA,CAAAe,SAAA,IAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY;QAKZhB,EAAA,CAAAe,SAAA,GAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY;QAKZhB,EAAA,CAAAe,SAAA,GAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY;QAgB5BhB,EAAA,CAAAe,SAAA,GAA+B;QAA/Bf,EAAA,CAAAgB,UAAA,UAAAR,GAAA,CAAA1B,qBAAA,CAA+B;QAwB3BkB,EAAA,CAAAe,SAAA,IAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY;QAOZhB,EAAA,CAAAe,SAAA,GAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY;QAOZhB,EAAA,CAAAe,SAAA,GAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY;QAmBhBhB,EAAA,CAAAe,SAAA,GAAY;QAAZf,EAAA,CAAAgB,UAAA,aAAY,wCAAAR,GAAA,CAAAxB,YAAA;;;mBD1KvBX,mBAAmB,EAAED,cAAc,EAAAgD,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,aAAA,EAAErD,eAAe,EAAAsD,EAAA,CAAAC,SAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}