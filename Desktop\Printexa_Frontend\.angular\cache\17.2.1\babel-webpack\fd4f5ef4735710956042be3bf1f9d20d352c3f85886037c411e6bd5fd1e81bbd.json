{"ast": null, "code": "import { Timeline1Component } from \"./timeline1/timeline1.component\";\nimport { Timeline2Component } from \"./timeline2/timeline2.component\";\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\nexport const TIMELINE_ROUTE = [{\n  path: \"\",\n  redirectTo: \"timeline1\",\n  pathMatch: \"full\"\n}, {\n  path: \"timeline1\",\n  component: Timeline1Component\n}, {\n  path: \"timeline2\",\n  component: Timeline2Component\n}, {\n  path: '**',\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["Timeline1Component", "Timeline2Component", "Page404Component", "TIMELINE_ROUTE", "path", "redirectTo", "pathMatch", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\timeline\\timeline.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { Timeline1Component } from \"./timeline1/timeline1.component\";\r\nimport { Timeline2Component } from \"./timeline2/timeline2.component\";\r\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\r\nexport const TIMELINE_ROUTE: Route[] = [\r\n  {\r\n    path: \"\",\r\n    redirectTo: \"timeline1\",\r\n    pathMatch: \"full\",\r\n  },\r\n  {\r\n    path: \"timeline1\",\r\n    component: Timeline1Component,\r\n  },\r\n  {\r\n    path: \"timeline2\",\r\n    component: Timeline2Component,\r\n  },\r\n  { path: '**', component: Page404Component },\r\n];\r\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,OAAO,MAAMC,cAAc,GAAY,CACrC;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,WAAW;EACvBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,WAAW;EACjBG,SAAS,EAAEP;CACZ,EACD;EACEI,IAAI,EAAE,WAAW;EACjBG,SAAS,EAAEN;CACZ,EACD;EAAEG,IAAI,EAAE,IAAI;EAAEG,SAAS,EAAEL;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}