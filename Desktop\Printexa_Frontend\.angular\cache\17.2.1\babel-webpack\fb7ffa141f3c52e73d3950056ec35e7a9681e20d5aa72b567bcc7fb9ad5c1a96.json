{"ast": null, "code": "import { Transition } from \"./index.js\";\nexport default function (transition) {\n  if (transition._id !== this._id) throw new Error();\n  for (var groups0 = this._groups, groups1 = transition._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n  return new Transition(merges, this._parents, this._name, this._id);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}