{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let MyProjectsService = /*#__PURE__*/(() => {\n  class MyProjectsService extends UnsubscribeOnDestroyAdapter {\n    constructor(httpClient) {\n      super();\n      this.httpClient = httpClient;\n      this.API_URL = 'assets/data/my-projects-client.json';\n      this.isTblLoading = true;\n      this.dataChange = new BehaviorSubject([]);\n    }\n    get data() {\n      return this.dataChange.value;\n    }\n    getDialogData() {\n      return this.dialogData;\n    }\n    /** CRUD METHODS */\n    getAllMyProjectss() {\n      this.subs.sink = this.httpClient.get(this.API_URL).subscribe({\n        next: data => {\n          this.isTblLoading = false;\n          this.dataChange.next(data);\n        },\n        error: error => {\n          this.isTblLoading = false;\n          console.log(error.name + ' ' + error.message);\n        }\n      });\n    }\n    addMyProjects(myProjects) {\n      this.dialogData = myProjects;\n      // this.httpClient.post(this.API_URL, myProjects)\n      //   .subscribe({\n      //     next: (data) => {\n      //       this.dialogData = myProjects;\n      //     },\n      //     error: (error: HttpErrorResponse) => {\n      //        // error code here\n      //     },\n      //   });\n    }\n    updateMyProjects(myProjects) {\n      this.dialogData = myProjects;\n      // this.httpClient.put(this.API_URL + myProjects.id, myProjects)\n      //     .subscribe({\n      //       next: (data) => {\n      //         this.dialogData = myProjects;\n      //       },\n      //       error: (error: HttpErrorResponse) => {\n      //          // error code here\n      //       },\n      //     });\n    }\n    deleteMyProjects(id) {\n      console.log(id);\n      // this.httpClient.delete(this.API_URL + id)\n      //     .subscribe({\n      //       next: (data) => {\n      //         console.log(id);\n      //       },\n      //       error: (error: HttpErrorResponse) => {\n      //          // error code here\n      //       },\n      //     });\n    }\n    static #_ = this.ɵfac = function MyProjectsService_Factory(t) {\n      return new (t || MyProjectsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MyProjectsService,\n      factory: MyProjectsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MyProjectsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}