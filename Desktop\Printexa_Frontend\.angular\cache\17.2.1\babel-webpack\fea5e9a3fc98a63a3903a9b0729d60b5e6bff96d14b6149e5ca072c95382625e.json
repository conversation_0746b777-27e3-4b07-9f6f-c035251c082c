{"ast": null, "code": "import pointer from \"./pointer.js\";\nimport sourceEvent from \"./sourceEvent.js\";\nexport default function (events, node) {\n  if (events.target) {\n    // i.e., instanceof Event, not TouchList or iterable\n    events = sourceEvent(events);\n    if (node === undefined) node = events.currentTarget;\n    events = events.touches || [events];\n  }\n  return Array.from(events, event => pointer(event, node));\n}", "map": {"version": 3, "names": ["pointer", "sourceEvent", "events", "node", "target", "undefined", "currentTarget", "touches", "Array", "from", "event"], "sources": ["C:/Users/<USER>/Desktop/mian/node_modules/d3-selection/src/pointers.js"], "sourcesContent": ["import pointer from \"./pointer.js\";\nimport sourceEvent from \"./sourceEvent.js\";\n\nexport default function(events, node) {\n  if (events.target) { // i.e., instanceof Event, not TouchList or iterable\n    events = sourceEvent(events);\n    if (node === undefined) node = events.currentTarget;\n    events = events.touches || [events];\n  }\n  return Array.from(events, event => pointer(event, node));\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,WAAW,MAAM,kBAAkB;AAE1C,eAAe,UAASC,MAAM,EAAEC,IAAI,EAAE;EACpC,IAAID,MAAM,CAACE,MAAM,EAAE;IAAE;IACnBF,MAAM,GAAGD,WAAW,CAACC,MAAM,CAAC;IAC5B,IAAIC,IAAI,KAAKE,SAAS,EAAEF,IAAI,GAAGD,MAAM,CAACI,aAAa;IACnDJ,MAAM,GAAGA,MAAM,CAACK,OAAO,IAAI,CAACL,MAAM,CAAC;EACrC;EACA,OAAOM,KAAK,CAACC,IAAI,CAACP,MAAM,EAAEQ,KAAK,IAAIV,OAAO,CAACU,KAAK,EAAEP,IAAI,CAAC,CAAC;AAC1D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}