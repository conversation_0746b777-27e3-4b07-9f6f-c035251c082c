{"ast": null, "code": "import { TodayComponent } from './today/today.component';\nimport { EmployeeComponent } from './employee/employee.component';\nimport { Page404Component } from 'app/authentication/page404/page404.component';\nimport { AttendanceSheetComponent } from './attendance-sheet/attendance-sheet.component';\nexport const ATTENDANCE_ROUTE = [{\n  path: 'today',\n  component: TodayComponent\n}, {\n  path: 'employee',\n  component: EmployeeComponent\n}, {\n  path: 'attendance-sheet',\n  component: AttendanceSheetComponent\n}, {\n  path: '**',\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["TodayComponent", "EmployeeComponent", "Page404Component", "AttendanceSheetComponent", "ATTENDANCE_ROUTE", "path", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\mian\\src\\app\\admin\\attendance\\attendance.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { TodayComponent } from './today/today.component';\r\nimport { EmployeeComponent } from './employee/employee.component';\r\nimport { Page404Component } from 'app/authentication/page404/page404.component';\r\nimport { AttendanceSheetComponent } from './attendance-sheet/attendance-sheet.component';\r\n\r\nexport const ATTENDANCE_ROUTE: Route[] = [\r\n  {\r\n    path: 'today',\r\n    component: TodayComponent,\r\n  },\r\n  {\r\n    path: 'employee',\r\n    component: EmployeeComponent,\r\n  },\r\n  {\r\n    path: 'attendance-sheet',\r\n    component: AttendanceSheetComponent,\r\n  },\r\n  { path: '**', component: Page404Component },\r\n];\r\n\r\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,wBAAwB,QAAQ,+CAA+C;AAExF,OAAO,MAAMC,gBAAgB,GAAY,CACvC;EACEC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEH;CACZ,EACD;EAAEE,IAAI,EAAE,IAAI;EAAEC,SAAS,EAAEJ;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}