{"ast": null, "code": "import interval from \"./interval.js\";\nvar year = interval(function (date) {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setFullYear(date.getFullYear() + step);\n}, function (start, end) {\n  return end.getFullYear() - start.getFullYear();\n}, function (date) {\n  return date.getFullYear();\n});\n\n// An optimized implementation for this simple case.\nyear.every = function (k) {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : interval(function (date) {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, function (date, step) {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\nexport default year;\nexport var years = year.range;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}