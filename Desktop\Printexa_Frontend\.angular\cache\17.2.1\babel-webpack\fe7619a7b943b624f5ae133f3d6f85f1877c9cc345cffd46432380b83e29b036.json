{"ast": null, "code": "import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('17.2.1');\nexport { VERSION };", "map": {"version": 3, "names": ["Version", "VERSION"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/@angular/cdk/fesm2022/cdk.mjs"], "sourcesContent": ["import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('17.2.1');\n\nexport { VERSION };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;;AAEvC;AACA,MAAMC,OAAO,GAAG,IAAID,OAAO,CAAC,QAAQ,CAAC;AAErC,SAASC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}