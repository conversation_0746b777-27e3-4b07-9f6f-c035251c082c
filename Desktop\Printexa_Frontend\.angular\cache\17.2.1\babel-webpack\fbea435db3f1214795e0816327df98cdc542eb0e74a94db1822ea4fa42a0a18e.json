{"ast": null, "code": "import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { DataSource } from '@angular/cdk/collections';\nimport { BehaviorSubject, fromEvent, merge } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { FormDialogComponent } from './dialogs/form-dialog/form-dialog.component';\nimport { DeleteComponent } from './dialogs/delete/delete.component';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport { MyTasksService } from './my-tasks.service';\nimport { TableExportUtil } from '@shared';\nimport { formatDate, NgClass, DatePipe } from '@angular/common';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatRippleModule } from '@angular/material/core';\nimport { FeatherIconsComponent } from '@shared/components/feather-icons/feather-icons.component';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"./my-tasks.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/tooltip\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/sort\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/core\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/paginator\";\nconst _c0 = [\"filter\"];\nfunction MyTasksComponent_mat_header_cell_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 55)(1, \"mat-checkbox\", 56);\n    i0.ɵɵlistener(\"change\", function MyTasksComponent_mat_header_cell_43_Template_mat_checkbox_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView($event ? ctx_r30.masterToggle() : null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", \"tbl-col-width-per-6\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.selection.hasValue() && ctx_r1.isAllSelected())(\"indeterminate\", ctx_r1.selection.hasValue() && !ctx_r1.isAllSelected())(\"ngClass\", \"tbl-checkbox\");\n  }\n}\nfunction MyTasksComponent_mat_cell_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-cell\", 55)(1, \"mat-checkbox\", 57);\n    i0.ɵɵlistener(\"click\", function MyTasksComponent_mat_cell_44_Template_mat_checkbox_click_1_listener($event) {\n      return $event.stopPropagation();\n    })(\"change\", function MyTasksComponent_mat_cell_44_Template_mat_checkbox_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r35);\n      const row_r32 = restoredCtx.$implicit;\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView($event ? ctx_r34.selection.toggle(row_r32) : null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r32 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", \"tbl-col-width-per-6\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r2.selection.isSelected(row_r32))(\"ngClass\", \"tbl-checkbox\");\n  }\n}\nfunction MyTasksComponent_mat_header_cell_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \"Id\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r36 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r36.id);\n  }\n}\nfunction MyTasksComponent_mat_header_cell_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \"Task No\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Task No:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r37 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r37.taskNo, \"\");\n  }\n}\nfunction MyTasksComponent_mat_header_cell_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \"Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Project:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r38 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r38.project);\n  }\n}\nfunction MyTasksComponent_mat_header_cell_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \"Client\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Client:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r39 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r39.client);\n  }\n}\nfunction MyTasksComponent_mat_header_cell_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_59_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r40.status, \"\");\n  }\n}\nfunction MyTasksComponent_mat_cell_59_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r40.status, \"\");\n  }\n}\nfunction MyTasksComponent_mat_cell_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 60)(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MyTasksComponent_mat_cell_59_Conditional_3_Template, 3, 1, \"div\")(4, MyTasksComponent_mat_cell_59_Conditional_4_Template, 3, 1, \"div\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r40 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(3, row_r40.status === \"Open\" ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, row_r40.status === \"Closed\" ? 4 : -1);\n  }\n}\nfunction MyTasksComponent_mat_header_cell_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \" Priority \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_62_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 63);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r45 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r45.priority, \"\");\n  }\n}\nfunction MyTasksComponent_mat_cell_62_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r45 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r45.priority, \"\");\n  }\n}\nfunction MyTasksComponent_mat_cell_62_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r45 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r45.priority, \"\");\n  }\n}\nfunction MyTasksComponent_mat_cell_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 60)(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Priority:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MyTasksComponent_mat_cell_62_Conditional_3_Template, 3, 1, \"div\")(4, MyTasksComponent_mat_cell_62_Conditional_4_Template, 3, 1, \"div\")(5, MyTasksComponent_mat_cell_62_Conditional_5_Template, 3, 1, \"div\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r45 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(3, row_r45.priority === \"Low\" ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, row_r45.priority === \"Medium\" ? 4 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(5, row_r45.priority === \"High\" ? 5 : -1);\n  }\n}\nfunction MyTasksComponent_mat_header_cell_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r52 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r52.type);\n  }\n}\nfunction MyTasksComponent_mat_header_cell_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \"Executor\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Executor:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r53 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r53.executor, \" \");\n  }\n}\nfunction MyTasksComponent_mat_header_cell_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \"Joining Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Joining Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r54 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 1, row_r54.date, \"MM/dd/yyyy\"), \"\");\n  }\n}\nfunction MyTasksComponent_mat_header_cell_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 58);\n    i0.ɵɵtext(1, \"Details\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 66)(2, \"span\", 59);\n    i0.ɵɵtext(3, \"Details:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r55 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(row_r55.details);\n  }\n}\nfunction MyTasksComponent_mat_header_cell_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 67);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MyTasksComponent_mat_cell_77_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-cell\", 67)(1, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function MyTasksComponent_mat_cell_77_Template_button_click_1_listener($event) {\n      return $event.stopPropagation();\n    })(\"click\", function MyTasksComponent_mat_cell_77_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r60);\n      const row_r56 = restoredCtx.$implicit;\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.editCall(row_r56));\n    });\n    i0.ɵɵelement(2, \"app-feather-icons\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function MyTasksComponent_mat_cell_77_Template_button_click_3_listener($event) {\n      return $event.stopPropagation();\n    })(\"click\", function MyTasksComponent_mat_cell_77_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r60);\n      const i_r57 = restoredCtx.index;\n      const row_r56 = restoredCtx.$implicit;\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.deleteItem(i_r57, row_r56));\n    });\n    i0.ɵɵelement(4, \"app-feather-icons\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"tbl-fav-edit\");\n    i0.ɵɵproperty(\"icon\", \"edit\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"tbl-fav-delete\");\n    i0.ɵɵproperty(\"icon\", \"trash-2\");\n  }\n}\nfunction MyTasksComponent_mat_header_row_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction MyTasksComponent_mat_row_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-row\", 71);\n    i0.ɵɵlistener(\"click\", function MyTasksComponent_mat_row_79_Template_mat_row_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r65);\n      const row_r63 = restoredCtx.$implicit;\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.editCall(row_r63));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵstyleProp(\"cursor\", \"pointer\");\n  }\n}\nfunction MyTasksComponent_Conditional_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 73);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"diameter\", 40);\n  }\n}\nfunction MyTasksComponent_Conditional_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1, \" No results \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r28.dataSource.renderedData.length === 0 ? \"\" : \"none\");\n  }\n}\nconst _c1 = () => [\"Home\"];\nconst _c2 = () => [5, 10, 25, 100];\nexport let MyTasksComponent = /*#__PURE__*/(() => {\n  class MyTasksComponent extends UnsubscribeOnDestroyAdapter {\n    constructor(httpClient, dialog, myTasksService, snackBar) {\n      super();\n      this.httpClient = httpClient;\n      this.dialog = dialog;\n      this.myTasksService = myTasksService;\n      this.snackBar = snackBar;\n      this.displayedColumns = ['select', 'taskNo', 'project', 'client', 'status', 'type', 'priority', 'executor', 'date', 'details', 'actions'];\n      this.selection = new SelectionModel(true, []);\n    }\n    ngOnInit() {\n      this.loadData();\n    }\n    refresh() {\n      this.loadData();\n    }\n    addNew() {\n      let tempDirection;\n      if (localStorage.getItem('isRtl') === 'true') {\n        tempDirection = 'rtl';\n      } else {\n        tempDirection = 'ltr';\n      }\n      const dialogRef = this.dialog.open(FormDialogComponent, {\n        data: {\n          myTasks: this.myTasks,\n          action: 'add'\n        },\n        direction: tempDirection\n      });\n      this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n        if (result === 1) {\n          // After dialog is closed we're doing frontend updates\n          // For add we're just pushing a new row inside DataServicex\n          this.exampleDatabase?.dataChange.value.unshift(this.myTasksService.getDialogData());\n          this.refreshTable();\n          this.showNotification('snackbar-success', 'Add Record Successfully...!!!', 'bottom', 'center');\n        }\n      });\n    }\n    editCall(row) {\n      this.id = row.id;\n      let tempDirection;\n      if (localStorage.getItem('isRtl') === 'true') {\n        tempDirection = 'rtl';\n      } else {\n        tempDirection = 'ltr';\n      }\n      const dialogRef = this.dialog.open(FormDialogComponent, {\n        data: {\n          myTasks: row,\n          action: 'edit'\n        },\n        direction: tempDirection\n      });\n      this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n        if (result === 1) {\n          // When using an edit things are little different, firstly we find record inside DataService by id\n          const foundIndex = this.exampleDatabase?.dataChange.value.findIndex(x => x.id === this.id);\n          // Then you update that record using data from dialogData (values you enetered)\n          if (foundIndex != null && this.exampleDatabase) {\n            this.exampleDatabase.dataChange.value[foundIndex] = this.myTasksService.getDialogData();\n            // And lastly refresh table\n            this.refreshTable();\n            this.showNotification('black', 'Edit Record Successfully...!!!', 'bottom', 'center');\n          }\n        }\n      });\n    }\n    deleteItem(i, row) {\n      this.index = i;\n      this.id = row.id;\n      let tempDirection;\n      if (localStorage.getItem('isRtl') === 'true') {\n        tempDirection = 'rtl';\n      } else {\n        tempDirection = 'ltr';\n      }\n      const dialogRef = this.dialog.open(DeleteComponent, {\n        height: '270px',\n        width: '300px',\n        data: row,\n        direction: tempDirection\n      });\n      this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n        if (result === 1) {\n          const foundIndex = this.exampleDatabase?.dataChange.value.findIndex(x => x.id === this.id);\n          // for delete we use splice in order to remove single object from DataService\n          if (foundIndex !== undefined && this.exampleDatabase !== undefined) {\n            this.exampleDatabase?.dataChange.value.splice(foundIndex, 1);\n            this.refreshTable();\n            this.showNotification('snackbar-danger', 'Delete Record Successfully...!!!', 'bottom', 'center');\n          }\n        }\n      });\n    }\n    refreshTable() {\n      this.paginator._changePageSize(this.paginator.pageSize);\n    }\n    /** Whether the number of selected elements matches the total number of rows. */\n    isAllSelected() {\n      const numSelected = this.selection.selected.length;\n      const numRows = this.dataSource.renderedData.length;\n      return numSelected === numRows;\n    }\n    /** Selects all rows if they are not all selected; otherwise clear selection. */\n    masterToggle() {\n      this.isAllSelected() ? this.selection.clear() : this.dataSource.renderedData.forEach(row => this.selection.select(row));\n    }\n    removeSelectedRows() {\n      const totalSelect = this.selection.selected.length;\n      this.selection.selected.forEach(item => {\n        const index = this.dataSource.renderedData.findIndex(d => d === item);\n        // console.log(this.dataSource.renderedData.findIndex((d) => d === item));\n        this.exampleDatabase?.dataChange.value.splice(index, 1);\n        this.refreshTable();\n        this.selection = new SelectionModel(true, []);\n      });\n      this.showNotification('snackbar-danger', totalSelect + ' Record Delete Successfully...!!!', 'bottom', 'center');\n    }\n    loadData() {\n      this.exampleDatabase = new MyTasksService(this.httpClient);\n      this.dataSource = new ExampleDataSource(this.exampleDatabase, this.paginator, this.sort);\n      this.subs.sink = fromEvent(this.filter.nativeElement, 'keyup').subscribe(() => {\n        if (!this.dataSource) {\n          return;\n        }\n        this.dataSource.filter = this.filter.nativeElement.value;\n      });\n    }\n    // export table data in excel file\n    exportExcel() {\n      // key name with space add in brackets\n      const exportData = this.dataSource.filteredData.map(x => ({\n        'Task No': x.taskNo,\n        Project: x.project,\n        Client: x.client,\n        Status: x.status,\n        Priority: x.priority,\n        Type: x.type,\n        Executor: x.executor,\n        'Joining Date': formatDate(new Date(x.date), 'yyyy-MM-dd', 'en') || '',\n        Details: x.details\n      }));\n      TableExportUtil.exportToExcel(exportData, 'excel');\n    }\n    showNotification(colorName, text, placementFrom, placementAlign) {\n      this.snackBar.open(text, '', {\n        duration: 2000,\n        verticalPosition: placementFrom,\n        horizontalPosition: placementAlign,\n        panelClass: colorName\n      });\n    }\n    static #_ = this.ɵfac = function MyTasksComponent_Factory(t) {\n      return new (t || MyTasksComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MyTasksService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MyTasksComponent,\n      selectors: [[\"app-my-tasks\"]],\n      viewQuery: function MyTasksComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 7);\n          i0.ɵɵviewQuery(MatSort, 7);\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 84,\n      vars: 15,\n      consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\"], [1, \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"card\"], [1, \"materialTableHeader\"], [1, \"left\"], [1, \"header-buttons-left\", \"ms-0\"], [1, \"tbl-title\"], [1, \"tbl-search-box\"], [\"for\", \"search-input\"], [1, \"material-icons\", \"search-icon\"], [\"placeholder\", \"Search\", \"type\", \"text\", \"aria-label\", \"Search box\", 1, \"browser-default\", \"search-field\"], [\"filter\", \"\"], [1, \"right\"], [1, \"tbl-export-btn\"], [1, \"tbl-header-btn\"], [\"matTooltip\", \"ADD\", 1, \"m-l-10\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"col-white\"], [\"matTooltip\", \"REFRESH\", 1, \"m-l-10\"], [\"matTooltip\", \"DELETE\", 1, \"m-l-10\", 3, \"hidden\"], [\"mat-mini-fab\", \"\", \"color\", \"warn\", 3, \"click\"], [\"matTooltip\", \"XLSX\", 1, \"export-button\", \"m-l-10\"], [\"src\", \"assets/images/icons/xlsx.png\", \"alt\", \"\", 3, \"click\"], [1, \"body\", \"overflow-auto\"], [1, \"responsive_table\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"mat-cell\", \"advance-table\", 3, \"dataSource\"], [\"matColumnDef\", \"select\"], [3, \"ngClass\", 4, \"matHeaderCellDef\"], [3, \"ngClass\", 4, \"matCellDef\"], [\"matColumnDef\", \"id\"], [\"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [4, \"matCellDef\"], [\"matColumnDef\", \"taskNo\"], [\"matColumnDef\", \"project\"], [\"matColumnDef\", \"client\"], [\"matColumnDef\", \"status\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"priority\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"executor\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"details\"], [\"matColumnDef\", \"actions\"], [\"class\", \"pr-0\", 4, \"matHeaderCellDef\"], [\"class\", \"pr-0\", 4, \"matCellDef\"], [4, \"matHeaderRowDef\"], [\"matRipple\", \"\", 3, \"cursor\", \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"tbl-spinner\"], [\"class\", \"no-results\", 3, \"display\"], [3, \"length\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [\"paginator\", \"\"], [3, \"ngClass\"], [3, \"checked\", \"indeterminate\", \"ngClass\", \"change\"], [3, \"checked\", \"ngClass\", \"click\", \"change\"], [\"mat-sort-header\", \"\"], [1, \"mobile-label\"], [\"mat-cell\", \"\"], [1, \"badge\", \"badge-solid-red\"], [1, \"badge\", \"badge-solid-green\"], [1, \"badge\", \"badge-pill\", \"badge-primary\", \"col-green\"], [1, \"badge\", \"badge-pill\", \"badge-primary\", \"col-blue\"], [1, \"badge\", \"badge-pill\", \"badge-primary\", \"col-red\"], [1, \"truncate-text\"], [1, \"pr-0\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Edit\", 1, \"tbl-action-btn\", 3, \"click\"], [3, \"icon\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Delete\", 1, \"tbl-action-btn\", 3, \"click\"], [\"matRipple\", \"\", 3, \"click\"], [1, \"tbl-spinner\"], [\"color\", \"primary\", \"mode\", \"indeterminate\", 3, \"diameter\"], [1, \"no-results\"]],\n      template: function MyTasksComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"ul\", 9)(10, \"li\", 10)(11, \"h2\");\n          i0.ɵɵtext(12, \"My Tasks\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"li\", 11)(14, \"label\", 12)(15, \"i\", 13);\n          i0.ɵɵtext(16, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(17, \"input\", 14, 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 16)(20, \"ul\", 17)(21, \"li\", 18)(22, \"div\", 19)(23, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MyTasksComponent_Template_button_click_23_listener() {\n            return ctx.addNew();\n          });\n          i0.ɵɵelementStart(24, \"mat-icon\", 21);\n          i0.ɵɵtext(25, \"add\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"li\", 18)(27, \"div\", 22)(28, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MyTasksComponent_Template_button_click_28_listener() {\n            return ctx.refresh();\n          });\n          i0.ɵɵelementStart(29, \"mat-icon\", 21);\n          i0.ɵɵtext(30, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(31, \"li\", 18)(32, \"div\", 23)(33, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function MyTasksComponent_Template_button_click_33_listener() {\n            return ctx.removeSelectedRows();\n          });\n          i0.ɵɵelementStart(34, \"mat-icon\", 21);\n          i0.ɵɵtext(35, \"delete \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"li\")(37, \"div\", 25)(38, \"img\", 26);\n          i0.ɵɵlistener(\"click\", function MyTasksComponent_Template_img_click_38_listener() {\n            return ctx.exportExcel();\n          });\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(39, \"div\", 27)(40, \"div\", 28)(41, \"table\", 29);\n          i0.ɵɵelementContainerStart(42, 30);\n          i0.ɵɵtemplate(43, MyTasksComponent_mat_header_cell_43_Template, 2, 4, \"mat-header-cell\", 31)(44, MyTasksComponent_mat_cell_44_Template, 2, 3, \"mat-cell\", 32);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(45, 33);\n          i0.ɵɵtemplate(46, MyTasksComponent_mat_header_cell_46_Template, 2, 0, \"mat-header-cell\", 34)(47, MyTasksComponent_mat_cell_47_Template, 2, 1, \"mat-cell\", 35);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(48, 36);\n          i0.ɵɵtemplate(49, MyTasksComponent_mat_header_cell_49_Template, 2, 0, \"mat-header-cell\", 34)(50, MyTasksComponent_mat_cell_50_Template, 4, 1, \"mat-cell\", 35);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(51, 37);\n          i0.ɵɵtemplate(52, MyTasksComponent_mat_header_cell_52_Template, 2, 0, \"mat-header-cell\", 34)(53, MyTasksComponent_mat_cell_53_Template, 4, 1, \"mat-cell\", 35);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(54, 38);\n          i0.ɵɵtemplate(55, MyTasksComponent_mat_header_cell_55_Template, 2, 0, \"mat-header-cell\", 34)(56, MyTasksComponent_mat_cell_56_Template, 4, 1, \"mat-cell\", 35);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(57, 39);\n          i0.ɵɵtemplate(58, MyTasksComponent_mat_header_cell_58_Template, 2, 0, \"mat-header-cell\", 34)(59, MyTasksComponent_mat_cell_59_Template, 5, 2, \"mat-cell\", 40);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(60, 41);\n          i0.ɵɵtemplate(61, MyTasksComponent_mat_header_cell_61_Template, 2, 0, \"mat-header-cell\", 34)(62, MyTasksComponent_mat_cell_62_Template, 6, 3, \"mat-cell\", 40);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(63, 42);\n          i0.ɵɵtemplate(64, MyTasksComponent_mat_header_cell_64_Template, 2, 0, \"mat-header-cell\", 34)(65, MyTasksComponent_mat_cell_65_Template, 4, 1, \"mat-cell\", 35);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(66, 43);\n          i0.ɵɵtemplate(67, MyTasksComponent_mat_header_cell_67_Template, 2, 0, \"mat-header-cell\", 34)(68, MyTasksComponent_mat_cell_68_Template, 4, 1, \"mat-cell\", 35);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(69, 44);\n          i0.ɵɵtemplate(70, MyTasksComponent_mat_header_cell_70_Template, 2, 0, \"mat-header-cell\", 34)(71, MyTasksComponent_mat_cell_71_Template, 5, 4, \"mat-cell\", 35);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(72, 45);\n          i0.ɵɵtemplate(73, MyTasksComponent_mat_header_cell_73_Template, 2, 0, \"mat-header-cell\", 34)(74, MyTasksComponent_mat_cell_74_Template, 5, 1, \"mat-cell\", 35);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(75, 46);\n          i0.ɵɵtemplate(76, MyTasksComponent_mat_header_cell_76_Template, 2, 0, \"mat-header-cell\", 47)(77, MyTasksComponent_mat_cell_77_Template, 5, 6, \"mat-cell\", 48);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(78, MyTasksComponent_mat_header_row_78_Template, 1, 0, \"mat-header-row\", 49)(79, MyTasksComponent_mat_row_79_Template, 1, 2, \"mat-row\", 50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(80, MyTasksComponent_Conditional_80_Template, 2, 1, \"div\", 51)(81, MyTasksComponent_Conditional_81_Template, 2, 2, \"div\", 52);\n          i0.ɵɵelement(82, \"mat-paginator\", 53, 54);\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"title\", \"My Tasks\")(\"items\", i0.ɵɵpureFunction0(13, _c1))(\"active_item\", \"My Tasks\");\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"hidden\", !ctx.selection.hasValue());\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(80, (ctx.exampleDatabase == null ? null : ctx.exampleDatabase.isTblLoading) ? 80 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(81, !(ctx.exampleDatabase == null ? null : ctx.exampleDatabase.isTblLoading) ? 81 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"length\", ctx.dataSource.filteredData.length)(\"pageIndex\", 0)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(14, _c2));\n        }\n      },\n      dependencies: [BreadcrumbComponent, MatTooltipModule, i5.MatTooltip, MatButtonModule, i6.MatIconButton, i6.MatMiniFabButton, MatIconModule, i7.MatIcon, MatTableModule, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow, MatSortModule, i9.MatSort, i9.MatSortHeader, NgClass, MatCheckboxModule, i10.MatCheckbox, FeatherIconsComponent, MatRippleModule, i11.MatRipple, MatProgressSpinnerModule, i12.MatProgressSpinner, MatPaginatorModule, i13.MatPaginator, DatePipe]\n    });\n  }\n  return MyTasksComponent;\n})();\nexport class ExampleDataSource extends DataSource {\n  get filter() {\n    return this.filterChange.value;\n  }\n  set filter(filter) {\n    this.filterChange.next(filter);\n  }\n  constructor(exampleDatabase, paginator, _sort) {\n    super();\n    this.exampleDatabase = exampleDatabase;\n    this.paginator = paginator;\n    this._sort = _sort;\n    this.filterChange = new BehaviorSubject('');\n    this.filteredData = [];\n    this.renderedData = [];\n    // Reset to the first page when the user changes the filter.\n    this.filterChange.subscribe(() => this.paginator.pageIndex = 0);\n  }\n  /** Connect function called by the table to retrieve one stream containing the data to render. */\n  connect() {\n    // Listen for any changes in the base data, sorting, filtering, or pagination\n    const displayDataChanges = [this.exampleDatabase.dataChange, this._sort.sortChange, this.filterChange, this.paginator.page];\n    this.exampleDatabase.getAllMyTaskss();\n    return merge(...displayDataChanges).pipe(map(() => {\n      // Filter data\n      this.filteredData = this.exampleDatabase.data.slice().filter(myTasks => {\n        const searchStr = (myTasks.taskNo + myTasks.project + myTasks.client + myTasks.status + myTasks.priority + myTasks.type + myTasks.executor + myTasks.date + myTasks.details).toLowerCase();\n        return searchStr.indexOf(this.filter.toLowerCase()) !== -1;\n      });\n      // Sort filtered data\n      const sortedData = this.sortData(this.filteredData.slice());\n      // Grab the page's slice of the filtered sorted data.\n      const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n      this.renderedData = sortedData.splice(startIndex, this.paginator.pageSize);\n      return this.renderedData;\n    }));\n  }\n  disconnect() {\n    //disconnect\n  }\n  /** Returns a sorted copy of the database data. */\n  sortData(data) {\n    if (!this._sort.active || this._sort.direction === '') {\n      return data;\n    }\n    return data.sort((a, b) => {\n      let propertyA = '';\n      let propertyB = '';\n      switch (this._sort.active) {\n        case 'id':\n          [propertyA, propertyB] = [a.id, b.id];\n          break;\n        case 'taskNo':\n          [propertyA, propertyB] = [a.taskNo, b.taskNo];\n          break;\n        case 'project':\n          [propertyA, propertyB] = [a.project, b.project];\n          break;\n        case 'client':\n          [propertyA, propertyB] = [a.client, b.client];\n          break;\n        case 'status':\n          [propertyA, propertyB] = [a.status, b.status];\n          break;\n        case 'priority':\n          [propertyA, propertyB] = [a.priority, b.priority];\n          break;\n        case 'type':\n          [propertyA, propertyB] = [a.type, b.type];\n          break;\n      }\n      const valueA = isNaN(+propertyA) ? propertyA : +propertyA;\n      const valueB = isNaN(+propertyB) ? propertyB : +propertyB;\n      return (valueA < valueB ? -1 : 1) * (this._sort.direction === 'asc' ? 1 : -1);\n    });\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}