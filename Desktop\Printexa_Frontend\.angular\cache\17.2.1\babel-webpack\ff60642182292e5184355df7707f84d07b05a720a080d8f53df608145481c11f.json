{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { observeOn } from '../operators/observeOn';\nimport { subscribeOn } from '../operators/subscribeOn';\nexport function schedulePromise(input, scheduler) {\n  return innerFrom(input).pipe(subscribeOn(scheduler), observeOn(scheduler));\n}", "map": {"version": 3, "names": ["innerFrom", "observeOn", "subscribeOn", "schedulePromise", "input", "scheduler", "pipe"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/rxjs/dist/esm/internal/scheduled/schedulePromise.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { observeOn } from '../operators/observeOn';\nimport { subscribeOn } from '../operators/subscribeOn';\nexport function schedulePromise(input, scheduler) {\n    return innerFrom(input).pipe(subscribeOn(scheduler), observeOn(scheduler));\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC9C,OAAOL,SAAS,CAACI,KAAK,CAAC,CAACE,IAAI,CAACJ,WAAW,CAACG,SAAS,CAAC,EAAEJ,SAAS,CAACI,SAAS,CAAC,CAAC;AAC9E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}