{"ast": null, "code": "import { ProduitComponent } from \"./produit/produit.component\";\nimport { FormProduitComponent } from \"./form-produit/form-produit.component\";\nimport { ProduitDeleteComponent } from \"./produit-delete/produit-delete.component\";\nexport const PRODUIT_ROUTE = [{\n  path: \"\",\n  children: [{\n    path: \"allProduits\",\n    component: ProduitComponent\n  }, {\n    path: \"addProduit\",\n    component: FormProduitComponent\n  }, {\n    path: \"editProduit/:id\",\n    component: FormProduitComponent\n  }, {\n    path: \"deleteProduit/:id\",\n    component: ProduitDeleteComponent\n  }, {\n    path: \"**\",\n    redirectTo: \"allProduits\"\n  } // Redirection par défaut\n  ]\n}];", "map": {"version": 3, "names": ["ProduitComponent", "FormProduitComponent", "ProduitDeleteComponent", "PRODUIT_ROUTE", "path", "children", "component", "redirectTo"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { ProduitComponent } from \"./produit/produit.component\";\r\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\r\nimport { FormProduitComponent } from \"./form-produit/form-produit.component\";\r\nimport { ProduitDeleteComponent } from \"./produit-delete/produit-delete.component\";\r\n\r\nexport const PRODUIT_ROUTE: Route[] = [\r\n  {\r\n    path: \"\", // Chemin vide pour la route parente\r\n    children: [\r\n      {\r\n        path: \"allProduits\", // Chemin relatif\r\n        component: ProduitComponent,\r\n      },\r\n      {\r\n        path: \"addProduit\", // Mieux que FormProduit pour la clarté\r\n        component: FormProduitComponent,\r\n      },\r\n      {\r\n        path: \"editProduit/:id\", // Avec paramètre ID\r\n        component: FormProduitComponent,\r\n      },\r\n      {\r\n        path: \"deleteProduit/:id\", // Avec paramètre ID\r\n        component: ProduitDeleteComponent,\r\n      },\r\n      { path: \"**\", redirectTo: \"allProduits\" }, // Redirection par défaut\r\n    ],\r\n  },\r\n];"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,6BAA6B;AAE9D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAElF,OAAO,MAAMC,aAAa,GAAY,CACpC;EACEC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CACR;IACED,IAAI,EAAE,aAAa;IACnBE,SAAS,EAAEN;GACZ,EACD;IACEI,IAAI,EAAE,YAAY;IAClBE,SAAS,EAAEL;GACZ,EACD;IACEG,IAAI,EAAE,iBAAiB;IACvBE,SAAS,EAAEL;GACZ,EACD;IACEG,IAAI,EAAE,mBAAmB;IACzBE,SAAS,EAAEJ;GACZ,EACD;IAAEE,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE;EAAa,CAAE,CAAE;EAAA;CAE9C,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}