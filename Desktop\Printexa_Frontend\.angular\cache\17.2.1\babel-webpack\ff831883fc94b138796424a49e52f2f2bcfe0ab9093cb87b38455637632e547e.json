{"ast": null, "code": "export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\nexport default function (event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}", "map": {"version": 3, "names": ["nopropagation", "event", "stopImmediatePropagation", "preventDefault"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/d3-brush/src/noevent.js"], "sourcesContent": ["export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAACC,KAAK,EAAE;EACnCA,KAAK,CAACC,wBAAwB,CAAC,CAAC;AAClC;AAEA,eAAe,UAASD,KAAK,EAAE;EAC7BA,KAAK,CAACE,cAAc,CAAC,CAAC;EACtBF,KAAK,CAACC,wBAAwB,CAAC,CAAC;AAClC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}