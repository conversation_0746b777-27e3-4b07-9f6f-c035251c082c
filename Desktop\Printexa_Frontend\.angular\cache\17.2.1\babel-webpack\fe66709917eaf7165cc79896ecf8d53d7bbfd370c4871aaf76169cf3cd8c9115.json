{"ast": null, "code": "import { AllHolidayComponent } from \"./all-holidays/all-holidays.component\";\nimport { AddHolidayComponent } from \"./add-holiday/add-holiday.component\";\nimport { EditHolidayComponent } from \"./edit-holiday/edit-holiday.component\";\nimport { Page404Component } from \"../../authentication/page404/page404.component\";\nexport const HOLIDAY_ROUTE = [{\n  path: \"all-holidays\",\n  component: AllHolidayComponent\n}, {\n  path: \"add-holiday\",\n  component: AddHolidayComponent\n}, {\n  path: \"edit-holiday\",\n  component: EditHolidayComponent\n}, {\n  path: \"**\",\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["AllHolidayComponent", "AddHolidayComponent", "EditHolidayComponent", "Page404Component", "HOLIDAY_ROUTE", "path", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\holidays\\holidays.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { AllHolidayComponent } from \"./all-holidays/all-holidays.component\";\r\nimport { AddHolidayComponent } from \"./add-holiday/add-holiday.component\";\r\nimport { EditHolidayComponent } from \"./edit-holiday/edit-holiday.component\";\r\nimport { Page404Component } from \"../../authentication/page404/page404.component\";\r\nexport const HOLIDAY_ROUTE: Route[] = [\r\n  {\r\n    path: \"all-holidays\",\r\n    component: AllHolidayComponent,\r\n  },\r\n  {\r\n    path: \"add-holiday\",\r\n    component: AddHolidayComponent,\r\n  },\r\n  {\r\n    path: \"edit-holiday\",\r\n    component: EditHolidayComponent,\r\n  },\r\n  { path: \"**\", component: Page404Component },\r\n];\r\n"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,OAAO,MAAMC,aAAa,GAAY,CACpC;EACEC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEJ;CACZ,EACD;EAAEG,IAAI,EAAE,IAAI;EAAEC,SAAS,EAAEH;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}