{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport DataZoomModel from './DataZoomModel.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar SliderZoomModel = /** @class */function (_super) {\n  __extends(SliderZoomModel, _super);\n  function SliderZoomModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SliderZoomModel.type;\n    return _this;\n  }\n  SliderZoomModel.type = 'dataZoom.slider';\n  SliderZoomModel.layoutMode = 'box';\n  SliderZoomModel.defaultOption = inheritDefaultOption(DataZoomModel.defaultOption, {\n    show: true,\n    // deault value can only be drived in view stage.\n    right: 'ph',\n    top: 'ph',\n    width: 'ph',\n    height: 'ph',\n    left: null,\n    bottom: null,\n    borderColor: '#d2dbee',\n    borderRadius: 3,\n    backgroundColor: 'rgba(47,69,84,0)',\n    // dataBackgroundColor: '#ddd',\n    dataBackground: {\n      lineStyle: {\n        color: '#d2dbee',\n        width: 0.5\n      },\n      areaStyle: {\n        color: '#d2dbee',\n        opacity: 0.2\n      }\n    },\n    selectedDataBackground: {\n      lineStyle: {\n        color: '#8fb0f7',\n        width: 0.5\n      },\n      areaStyle: {\n        color: '#8fb0f7',\n        opacity: 0.2\n      }\n    },\n    // Color of selected window.\n    fillerColor: 'rgba(135,175,274,0.2)',\n    handleIcon: 'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',\n    // Percent of the slider height\n    handleSize: '100%',\n    handleStyle: {\n      color: '#fff',\n      borderColor: '#ACB8D1'\n    },\n    moveHandleSize: 7,\n    moveHandleIcon: 'path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z',\n    moveHandleStyle: {\n      color: '#D2DBEE',\n      opacity: 0.7\n    },\n    showDetail: true,\n    showDataShadow: 'auto',\n    realtime: true,\n    zoomLock: false,\n    textStyle: {\n      color: '#6E7079'\n    },\n    brushSelect: true,\n    brushStyle: {\n      color: 'rgba(135,175,274,0.15)'\n    },\n    emphasis: {\n      handleStyle: {\n        borderColor: '#8FB0F7'\n      },\n      moveHandleStyle: {\n        color: '#8FB0F7'\n      }\n    }\n  });\n  return SliderZoomModel;\n}(DataZoomModel);\nexport default SliderZoomModel;", "map": {"version": 3, "names": ["__extends", "DataZoomModel", "inheritDefaultOption", "SliderZoomModel", "_super", "_this", "apply", "arguments", "type", "layoutMode", "defaultOption", "show", "right", "top", "width", "height", "left", "bottom", "borderColor", "borderRadius", "backgroundColor", "dataBackground", "lineStyle", "color", "areaStyle", "opacity", "selectedDataBackground", "fillerColor", "handleIcon", "handleSize", "handleStyle", "moveHandleSize", "moveHandleIcon", "moveHandleStyle", "showDetail", "showDataShadow", "realtime", "zoomLock", "textStyle", "brushSelect", "brushStyle", "emphasis"], "sources": ["C:/Users/<USER>/Desktop/mian/node_modules/echarts/lib/component/dataZoom/SliderZoomModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport DataZoomModel from './DataZoomModel.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar SliderZoomModel = /** @class */function (_super) {\n  __extends(SliderZoomModel, _super);\n  function SliderZoomModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SliderZoomModel.type;\n    return _this;\n  }\n  SliderZoomModel.type = 'dataZoom.slider';\n  SliderZoomModel.layoutMode = 'box';\n  SliderZoomModel.defaultOption = inheritDefaultOption(DataZoomModel.defaultOption, {\n    show: true,\n    // deault value can only be drived in view stage.\n    right: 'ph',\n    top: 'ph',\n    width: 'ph',\n    height: 'ph',\n    left: null,\n    bottom: null,\n    borderColor: '#d2dbee',\n    borderRadius: 3,\n    backgroundColor: 'rgba(47,69,84,0)',\n    // dataBackgroundColor: '#ddd',\n    dataBackground: {\n      lineStyle: {\n        color: '#d2dbee',\n        width: 0.5\n      },\n      areaStyle: {\n        color: '#d2dbee',\n        opacity: 0.2\n      }\n    },\n    selectedDataBackground: {\n      lineStyle: {\n        color: '#8fb0f7',\n        width: 0.5\n      },\n      areaStyle: {\n        color: '#8fb0f7',\n        opacity: 0.2\n      }\n    },\n    // Color of selected window.\n    fillerColor: 'rgba(135,175,274,0.2)',\n    handleIcon: 'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',\n    // Percent of the slider height\n    handleSize: '100%',\n    handleStyle: {\n      color: '#fff',\n      borderColor: '#ACB8D1'\n    },\n    moveHandleSize: 7,\n    moveHandleIcon: 'path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z',\n    moveHandleStyle: {\n      color: '#D2DBEE',\n      opacity: 0.7\n    },\n    showDetail: true,\n    showDataShadow: 'auto',\n    realtime: true,\n    zoomLock: false,\n    textStyle: {\n      color: '#6E7079'\n    },\n    brushSelect: true,\n    brushStyle: {\n      color: 'rgba(135,175,274,0.15)'\n    },\n    emphasis: {\n      handleStyle: {\n        borderColor: '#8FB0F7'\n      },\n      moveHandleStyle: {\n        color: '#8FB0F7'\n      }\n    }\n  });\n  return SliderZoomModel;\n}(DataZoomModel);\nexport default SliderZoomModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDJ,SAAS,CAACG,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,eAAe,CAACK,IAAI;IACjC,OAAOH,KAAK;EACd;EACAF,eAAe,CAACK,IAAI,GAAG,iBAAiB;EACxCL,eAAe,CAACM,UAAU,GAAG,KAAK;EAClCN,eAAe,CAACO,aAAa,GAAGR,oBAAoB,CAACD,aAAa,CAACS,aAAa,EAAE;IAChFC,IAAI,EAAE,IAAI;IACV;IACAC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,kBAAkB;IACnC;IACAC,cAAc,EAAE;MACdC,SAAS,EAAE;QACTC,KAAK,EAAE,SAAS;QAChBT,KAAK,EAAE;MACT,CAAC;MACDU,SAAS,EAAE;QACTD,KAAK,EAAE,SAAS;QAChBE,OAAO,EAAE;MACX;IACF,CAAC;IACDC,sBAAsB,EAAE;MACtBJ,SAAS,EAAE;QACTC,KAAK,EAAE,SAAS;QAChBT,KAAK,EAAE;MACT,CAAC;MACDU,SAAS,EAAE;QACTD,KAAK,EAAE,SAAS;QAChBE,OAAO,EAAE;MACX;IACF,CAAC;IACD;IACAE,WAAW,EAAE,uBAAuB;IACpCC,UAAU,EAAE,iHAAiH;IAC7H;IACAC,UAAU,EAAE,MAAM;IAClBC,WAAW,EAAE;MACXP,KAAK,EAAE,MAAM;MACbL,WAAW,EAAE;IACf,CAAC;IACDa,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,kaAAka;IAClbC,eAAe,EAAE;MACfV,KAAK,EAAE,SAAS;MAChBE,OAAO,EAAE;IACX,CAAC;IACDS,UAAU,EAAE,IAAI;IAChBC,cAAc,EAAE,MAAM;IACtBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;MACTf,KAAK,EAAE;IACT,CAAC;IACDgB,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE;MACVjB,KAAK,EAAE;IACT,CAAC;IACDkB,QAAQ,EAAE;MACRX,WAAW,EAAE;QACXZ,WAAW,EAAE;MACf,CAAC;MACDe,eAAe,EAAE;QACfV,KAAK,EAAE;MACT;IACF;EACF,CAAC,CAAC;EACF,OAAOpB,eAAe;AACxB,CAAC,CAACF,aAAa,CAAC;AAChB,eAAeE,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}