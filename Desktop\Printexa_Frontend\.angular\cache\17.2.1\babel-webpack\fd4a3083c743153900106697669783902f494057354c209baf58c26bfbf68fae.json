{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from 'app/app.component';\nimport { appConfig } from 'app/app.config';\nbootstrapApplication(AppComponent, appConfig).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "appConfig", "catch", "err", "console", "error"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { AppComponent } from 'app/app.component';\r\nimport { appConfig } from 'app/app.config';\r\n\r\nbootstrapApplication(AppComponent, appConfig).catch((err) =>\r\n  console.error(err)\r\n);\r\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,SAAS,QAAQ,gBAAgB;AAE1CF,oBAAoB,CAACC,YAAY,EAAEC,SAAS,CAAC,CAACC,KAAK,CAAEC,GAAG,IACtDC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CACnB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}