{"ast": null, "code": "import continuous from \"./continuous.js\";\nimport { initRange } from \"./init.js\";\nimport { linearish } from \"./linear.js\";\nimport number from \"./number.js\";\nfunction square(x) {\n  return Math.sign(x) * x * x;\n}\nfunction unsquare(x) {\n  return Math.sign(x) * Math.sqrt(Math.abs(x));\n}\nexport default function radial() {\n  var squared = continuous(),\n    range = [0, 1],\n    round = false,\n    unknown;\n  function scale(x) {\n    var y = unsquare(squared(x));\n    return isNaN(y) ? unknown : round ? Math.round(y) : y;\n  }\n  scale.invert = function (y) {\n    return squared.invert(square(y));\n  };\n  scale.domain = function (_) {\n    return arguments.length ? (squared.domain(_), scale) : squared.domain();\n  };\n  scale.range = function (_) {\n    return arguments.length ? (squared.range((range = Array.from(_, number)).map(square)), scale) : range.slice();\n  };\n  scale.rangeRound = function (_) {\n    return scale.range(_).round(true);\n  };\n  scale.round = function (_) {\n    return arguments.length ? (round = !!_, scale) : round;\n  };\n  scale.clamp = function (_) {\n    return arguments.length ? (squared.clamp(_), scale) : squared.clamp();\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.copy = function () {\n    return radial(squared.domain(), range).round(round).clamp(squared.clamp()).unknown(unknown);\n  };\n  initRange.apply(scale, arguments);\n  return linearish(scale);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}