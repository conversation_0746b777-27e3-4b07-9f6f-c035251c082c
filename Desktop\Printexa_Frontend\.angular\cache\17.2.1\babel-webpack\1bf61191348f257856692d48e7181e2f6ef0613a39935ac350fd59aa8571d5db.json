{"ast": null, "code": "import { MainLayoutComponent } from './layout/app-layout/main-layout/main-layout.component';\nimport { AuthGuard } from '@core/guard/auth.guard';\nimport { AuthLayoutComponent } from './layout/app-layout/auth-layout/auth-layout.component';\nimport { Page404Component } from './authentication/page404/page404.component';\nexport const APP_ROUTE = [{\n  path: '',\n  component: MainLayoutComponent,\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: '/admin',\n    pathMatch: 'full'\n  },\n  // Route admin\n  path, 'admin/gestion', canActivate, [AuthGuard], loadChildren, () => import('./admin/gestion/produit/produit.routes').then(m => m.PRODUIT_ROUTES)\n  // Route admin\n  ,\n  // Route admin\n  {\n    path: 'admin',\n    loadChildren: () => import('./admin/admin.routes').then(m => m.ADMIN_ROUTE)\n  },\n  // Route employee\n  {\n    path: 'employee',\n    loadChildren: () => import('./employee/employee.routes').then(m => m.EMPLOYEE_ROUTE)\n  },\n  // Route client\n  {\n    path: 'client',\n    loadChildren: () => import('./client/client.routes').then(m => m.CLIENT_ROUTE)\n  },\n  // Autres modules fonctionnels\n  {\n    path: 'calendar',\n    loadChildren: () => import('./calendar/calendar.routes').then(m => m.CALENDAR_ROUTE)\n  }, {\n    path: 'task',\n    loadChildren: () => import('./task/task.routes').then(m => m.TASK_ROUTE)\n  }, {\n    path: 'contacts',\n    loadChildren: () => import('./contacts/contacts.routes').then(m => m.CONTACT_ROUTE)\n  }, {\n    path: 'email',\n    loadChildren: () => import('./email/email.routes').then(m => m.EMAIL_ROUTE)\n  }, {\n    path: 'apps',\n    loadChildren: () => import('./apps/apps.routes').then(m => m.APPS_ROUTE)\n  }, {\n    path: 'widget',\n    loadChildren: () => import('./widget/widget.routes').then(m => m.WIDGET_ROUTE)\n  }, {\n    path: 'ui',\n    loadChildren: () => import('./ui/ui.routes').then(m => m.UI_ROUTE)\n  }, {\n    path: 'forms',\n    loadChildren: () => import('./forms/forms.routes').then(m => m.FORMS_ROUTE)\n  }, {\n    path: 'tables',\n    loadChildren: () => import('./tables/tables.routes').then(m => m.TEBLES_ROUTE)\n  }, {\n    path: 'charts',\n    loadChildren: () => import('./charts/charts.routes').then(m => m.CHART_ROUTE)\n  }, {\n    path: 'timeline',\n    loadChildren: () => import('./timeline/timeline.routes').then(m => m.TIMELINE_ROUTE)\n  }, {\n    path: 'icons',\n    loadChildren: () => import('./icons/icons.routes').then(m => m.ICONS_ROUTE)\n  }, {\n    path: 'extra-pages',\n    loadChildren: () => import('./extra-pages/extra-pages.routes').then(m => m.EXTRA_PAGES_ROUTE)\n  }, {\n    path: 'maps',\n    loadChildren: () => import('./maps/maps.routes').then(m => m.MAPS_ROUTE)\n  }, {\n    path: 'multilevel',\n    loadChildren: () => import('./multilevel/multilevel.routes').then(m => m.MULTILEVEL_ROUTE)\n  }]\n}, {\n  path: 'authentication',\n  component: AuthLayoutComponent,\n  loadChildren: () => import('./authentication/auth.routes').then(m => m.AUTH_ROUTE)\n}, {\n  path: '**',\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["MainLayoutComponent", "<PERSON><PERSON><PERSON><PERSON>", "AuthLayoutComponent", "Page404Component", "APP_ROUTE", "path", "component", "canActivate", "children", "redirectTo", "pathMatch", "loadChildren", "then", "m", "PRODUIT_ROUTES", "ADMIN_ROUTE", "EMPLOYEE_ROUTE", "CLIENT_ROUTE", "CALENDAR_ROUTE", "TASK_ROUTE", "CONTACT_ROUTE", "EMAIL_ROUTE", "APPS_ROUTE", "WIDGET_ROUTE", "UI_ROUTE", "FORMS_ROUTE", "TEBLES_ROUTE", "CHART_ROUTE", "TIMELINE_ROUTE", "ICONS_ROUTE", "EXTRA_PAGES_ROUTE", "MAPS_ROUTE", "MULTILEVEL_ROUTE", "AUTH_ROUTE"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Route } from '@angular/router';\r\nimport { MainLayoutComponent } from './layout/app-layout/main-layout/main-layout.component';\r\nimport { AuthGuard } from '@core/guard/auth.guard';\r\nimport { AuthLayoutComponent } from './layout/app-layout/auth-layout/auth-layout.component';\r\nimport { Page404Component } from './authentication/page404/page404.component';\r\n\r\nexport const APP_ROUTE: Route[] = [\r\n  {\r\n    path: '',\r\n    component: MainLayoutComponent,\r\n    canActivate: [AuthGuard], // Protection globale pour toutes les routes enfants\r\n    children: [\r\n      { path: '', redirectTo: '/admin', pathMatch: 'full' }, // Redirection vers admin par défaut\r\n\r\n      // Route admin\r\n         path: 'admin/gestion',\r\n    canActivate: [AuthGuard],\r\n    loadChildren: () => import('./admin/gestion/produit/produit.routes').then(m => m.PRODUIT_ROUTES)\r\n\r\n      // Route admin\r\n      {\r\n        path: 'admin',\r\n        loadChildren: () => import('./admin/admin.routes').then((m) => m.ADMIN_ROUTE),\r\n      },\r\n\r\n      // Route employee\r\n      {\r\n        path: 'employee',\r\n        loadChildren: () => import('./employee/employee.routes').then((m) => m.EMPLOYEE_ROUTE),\r\n      },\r\n\r\n      // Route client\r\n      {\r\n        path: 'client',\r\n        loadChildren: () => import('./client/client.routes').then((m) => m.CLIENT_ROUTE),\r\n      },\r\n\r\n      // Autres modules fonctionnels\r\n      {\r\n        path: 'calendar',\r\n        loadChildren: () => import('./calendar/calendar.routes').then((m) => m.CALENDAR_ROUTE),\r\n      },\r\n      {\r\n        path: 'task',\r\n        loadChildren: () => import('./task/task.routes').then((m) => m.TASK_ROUTE),\r\n      },\r\n      {\r\n        path: 'contacts',\r\n        loadChildren: () => import('./contacts/contacts.routes').then((m) => m.CONTACT_ROUTE),\r\n      },\r\n      {\r\n        path: 'email',\r\n        loadChildren: () => import('./email/email.routes').then((m) => m.EMAIL_ROUTE),\r\n      },\r\n      {\r\n        path: 'apps',\r\n        loadChildren: () => import('./apps/apps.routes').then((m) => m.APPS_ROUTE),\r\n      },\r\n      {\r\n        path: 'widget',\r\n        loadChildren: () => import('./widget/widget.routes').then((m) => m.WIDGET_ROUTE),\r\n      },\r\n      {\r\n        path: 'ui',\r\n        loadChildren: () => import('./ui/ui.routes').then((m) => m.UI_ROUTE),\r\n      },\r\n      {\r\n        path: 'forms',\r\n        loadChildren: () => import('./forms/forms.routes').then((m) => m.FORMS_ROUTE),\r\n      },\r\n      {\r\n        path: 'tables',\r\n        loadChildren: () => import('./tables/tables.routes').then((m) => m.TEBLES_ROUTE),\r\n      },\r\n      {\r\n        path: 'charts',\r\n        loadChildren: () => import('./charts/charts.routes').then((m) => m.CHART_ROUTE),\r\n      },\r\n      {\r\n        path: 'timeline',\r\n        loadChildren: () => import('./timeline/timeline.routes').then((m) => m.TIMELINE_ROUTE),\r\n      },\r\n      {\r\n        path: 'icons',\r\n        loadChildren: () => import('./icons/icons.routes').then((m) => m.ICONS_ROUTE),\r\n      },\r\n      {\r\n        path: 'extra-pages',\r\n        loadChildren: () => import('./extra-pages/extra-pages.routes').then((m) => m.EXTRA_PAGES_ROUTE),\r\n      },\r\n      {\r\n        path: 'maps',\r\n        loadChildren: () => import('./maps/maps.routes').then((m) => m.MAPS_ROUTE),\r\n      },\r\n      {\r\n        path: 'multilevel',\r\n        loadChildren: () => import('./multilevel/multilevel.routes').then((m) => m.MULTILEVEL_ROUTE),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: 'authentication',\r\n    component: AuthLayoutComponent,\r\n    loadChildren: () => import('./authentication/auth.routes').then((m) => m.AUTH_ROUTE),\r\n  },\r\n  { path: '**', component: Page404Component },\r\n];"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,mBAAmB,QAAQ,uDAAuD;AAC3F,SAASC,gBAAgB,QAAQ,4CAA4C;AAE7E,OAAO,MAAMC,SAAS,GAAY,CAChC;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEN,mBAAmB;EAC9BO,WAAW,EAAE,CAACN,SAAS,CAAC;EACxBO,QAAQ,EAAE,CACR;IAAEH,IAAI,EAAE,EAAE;IAAEI,UAAU,EAAE,QAAQ;IAAEC,SAAS,EAAE;EAAM,CAAE;EAErD;EACGL,IAAI,EAAE,eAAe,EAC1BE,WAAW,EAAE,CAACN,SAAS,CAAC,EACxBU,YAAY,EAAE,MAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc;EAE7F;EAAA;EAAA;EACA;IACET,IAAI,EAAE,OAAO;IACbM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,WAAW;GAC7E;EAED;EACA;IACEV,IAAI,EAAE,UAAU;IAChBM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,cAAc;GACtF;EAED;EACA;IACEX,IAAI,EAAE,QAAQ;IACdM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,YAAY;GAChF;EAED;EACA;IACEZ,IAAI,EAAE,UAAU;IAChBM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACK,cAAc;GACtF,EACD;IACEb,IAAI,EAAE,MAAM;IACZM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,UAAU;GAC1E,EACD;IACEd,IAAI,EAAE,UAAU;IAChBM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACO,aAAa;GACrF,EACD;IACEf,IAAI,EAAE,OAAO;IACbM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACQ,WAAW;GAC7E,EACD;IACEhB,IAAI,EAAE,MAAM;IACZM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,UAAU;GAC1E,EACD;IACEjB,IAAI,EAAE,QAAQ;IACdM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,YAAY;GAChF,EACD;IACElB,IAAI,EAAE,IAAI;IACVM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACW,QAAQ;GACpE,EACD;IACEnB,IAAI,EAAE,OAAO;IACbM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACY,WAAW;GAC7E,EACD;IACEpB,IAAI,EAAE,QAAQ;IACdM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACa,YAAY;GAChF,EACD;IACErB,IAAI,EAAE,QAAQ;IACdM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACc,WAAW;GAC/E,EACD;IACEtB,IAAI,EAAE,UAAU;IAChBM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACe,cAAc;GACtF,EACD;IACEvB,IAAI,EAAE,OAAO;IACbM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACgB,WAAW;GAC7E,EACD;IACExB,IAAI,EAAE,aAAa;IACnBM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACiB,iBAAiB;GAC/F,EACD;IACEzB,IAAI,EAAE,MAAM;IACZM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACkB,UAAU;GAC1E,EACD;IACE1B,IAAI,EAAE,YAAY;IAClBM,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACmB,gBAAgB;GAC5F;CAEJ,EACD;EACE3B,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEJ,mBAAmB;EAC9BS,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACoB,UAAU;CACpF,EACD;EAAE5B,IAAI,EAAE,IAAI;EAAEC,SAAS,EAAEH;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}