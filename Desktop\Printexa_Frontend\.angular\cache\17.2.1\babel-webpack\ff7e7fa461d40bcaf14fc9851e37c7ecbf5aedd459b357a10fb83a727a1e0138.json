{"ast": null, "code": "import { MatButtonModule } from '@angular/material/button';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/button\";\nconst _c0 = () => [\"Accounts\"];\nexport let InvoiceComponent = /*#__PURE__*/(() => {\n  class InvoiceComponent {\n    constructor() {\n      // constructor\n    }\n    static #_ = this.ɵfac = function InvoiceComponent_Factory(t) {\n      return new (t || InvoiceComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InvoiceComponent,\n      selectors: [[\"app-invoice\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 158,\n      vars: 4,\n      consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\", \"clearfix\"], [1, \"col-xs-12\", \"col-sm-12\", \"col-md-12\", \"col-lg-12\"], [1, \"card\"], [1, \"body\"], [1, \"row\"], [1, \"col-md-12\"], [1, \"white-box\"], [1, \"float-end\"], [1, \"float-start\"], [\"src\", \"assets/images/logo.png\", \"alt\", \"logo\", 1, \"logo-default\"], [1, \"fs-2\", \"fw-bold\", \"m-2\", \"align-middle\"], [1, \"m-l-5\"], [1, \"float-end\", \"text-end\"], [1, \"addr-font-h3\", \"font-bold\"], [1, \"font-bold\", \"addr-font-h4\"], [1, \"m-l-30\"], [1, \"m-t-30\"], [1, \"fa\", \"fa-calendar\"], [1, \"label\", \"label-success\"], [1, \"table-responsive\", \"m-t-40\"], [1, \"table\", \"table-hover\", \"text-center\"], [1, \"text-center\"], [1, \"\"], [1, \"float-end\", \"m-t-30\", \"text-end\"], [1, \"clearfix\"], [1, \"text-end\"], [\"mat-flat-button\", \"\", \"color\", \"primary\"], [1, \"fas\", \"fa-print\"]],\n      template: function InvoiceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10)(11, \"h3\")(12, \"b\");\n          i0.ɵɵtext(13, \"INVOICE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 11);\n          i0.ɵɵtext(15, \"#345766\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(16, \"hr\");\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"div\", 9)(19, \"div\", 12)(20, \"address\");\n          i0.ɵɵelement(21, \"img\", 13);\n          i0.ɵɵelementStart(22, \"Span\", 14);\n          i0.ɵɵtext(23, \"Kuber\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\", 15);\n          i0.ɵɵtext(25, \" D 103, Kuber Solutions, \");\n          i0.ɵɵelement(26, \"br\");\n          i0.ɵɵtext(27, \" Opp. Town Hall, \");\n          i0.ɵɵelement(28, \"br\");\n          i0.ɵɵtext(29, \" Sardar Patel Road, \");\n          i0.ɵɵelement(30, \"br\");\n          i0.ɵɵtext(31, \" Ahmedabad - 380015 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"address\")(34, \"p\", 17);\n          i0.ɵɵtext(35, \"BILL TO :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"p\", 18);\n          i0.ɵɵtext(37, \"Jayesh Patel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\", 19);\n          i0.ɵɵtext(39, \" 207, Prem Sagar Appt., \");\n          i0.ɵɵelement(40, \"br\");\n          i0.ɵɵtext(41, \" Near Income Tax Office, \");\n          i0.ɵɵelement(42, \"br\");\n          i0.ɵɵtext(43, \" Ashram Road, \");\n          i0.ɵɵelement(44, \"br\");\n          i0.ɵɵtext(45, \" Ahmedabad - 380057 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"p\", 20)(47, \"b\");\n          i0.ɵɵtext(48, \"Invoice Date :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"i\", 21);\n          i0.ɵɵtext(50, \" 14th July 2017 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\")(52, \"b\");\n          i0.ɵɵtext(53, \"Status :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\", 22);\n          i0.ɵɵtext(55, \"Success\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(56, \"div\", 9)(57, \"div\", 23)(58, \"table\", 24)(59, \"thead\")(60, \"tr\")(61, \"th\", 25);\n          i0.ɵɵtext(62, \"#\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\");\n          i0.ɵɵtext(64, \"Project Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\");\n          i0.ɵɵtext(66, \"Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\");\n          i0.ɵɵtext(68, \"Work Hours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\");\n          i0.ɵɵtext(70, \"Hourly Charges\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\");\n          i0.ɵɵtext(72, \"Total\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"tbody\")(74, \"tr\")(75, \"td\", 25);\n          i0.ɵɵtext(76, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"td\");\n          i0.ɵɵtext(78, \"Angular App\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"td\", 26);\n          i0.ɵɵtext(80, \"Ecommerce app\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"td\");\n          i0.ɵɵtext(82, \"23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"td\");\n          i0.ɵɵtext(84, \"$20\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"td\");\n          i0.ɵɵtext(86, \"$460\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"tr\")(88, \"td\", 25);\n          i0.ɵɵtext(89, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"td\");\n          i0.ɵɵtext(91, \"Angular App\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"td\");\n          i0.ɵɵtext(93, \"Ecommerce app\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"td\");\n          i0.ɵɵtext(95, \"23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"td\");\n          i0.ɵɵtext(97, \"$20\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"td\");\n          i0.ɵɵtext(99, \"$460\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(100, \"tr\")(101, \"td\", 25);\n          i0.ɵɵtext(102, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"td\");\n          i0.ɵɵtext(104, \"Angular App\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"td\");\n          i0.ɵɵtext(106, \"Ecommerce app\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"td\");\n          i0.ɵɵtext(108, \"23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"td\");\n          i0.ɵɵtext(110, \"$20\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"td\");\n          i0.ɵɵtext(112, \"$460\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"tr\")(114, \"td\", 25);\n          i0.ɵɵtext(115, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"td\");\n          i0.ɵɵtext(117, \"Angular App\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"td\");\n          i0.ɵɵtext(119, \"Ecommerce app\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"td\");\n          i0.ɵɵtext(121, \"23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"td\");\n          i0.ɵɵtext(123, \"$20\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"td\");\n          i0.ɵɵtext(125, \"$460\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"tr\")(127, \"td\", 25);\n          i0.ɵɵtext(128, \"5\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"td\");\n          i0.ɵɵtext(130, \"Angular App\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"td\");\n          i0.ɵɵtext(132, \"Ecommerce app\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"td\");\n          i0.ɵɵtext(134, \"23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"td\");\n          i0.ɵɵtext(136, \"$20\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"td\");\n          i0.ɵɵtext(138, \"$460\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(139, \"div\", 9)(140, \"div\", 27)(141, \"p\");\n          i0.ɵɵtext(142, \"Sub - Total amount: $2600\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"p\");\n          i0.ɵɵtext(144, \"Discount : $100 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(145, \"p\");\n          i0.ɵɵtext(146, \"vat (10%) : $160 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(147, \"hr\");\n          i0.ɵɵelementStart(148, \"h3\")(149, \"b\");\n          i0.ɵɵtext(150, \"Total :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(151, \" $2760\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(152, \"div\", 28)(153, \"hr\");\n          i0.ɵɵelementStart(154, \"div\", 29)(155, \"button\", 30);\n          i0.ɵɵelement(156, \"i\", 31);\n          i0.ɵɵtext(157, \" Print\");\n          i0.ɵɵelementEnd()()()()()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"title\", \"Invoice\")(\"items\", i0.ɵɵpureFunction0(3, _c0))(\"active_item\", \"Invoice\");\n        }\n      },\n      dependencies: [BreadcrumbComponent, MatButtonModule, i1.MatButton]\n    });\n  }\n  return InvoiceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}