{"ast": null, "code": "import { ExpenseReportService } from './expense-report.service';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { DataSource } from '@angular/cdk/collections';\nimport { BehaviorSubject, fromEvent, merge } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport { DatePipe } from '@angular/common';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatTableModule } from '@angular/material/table';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./expense-report.service\";\nimport * as i3 from \"@angular/material/table\";\nimport * as i4 from \"@angular/material/sort\";\nimport * as i5 from \"@angular/material/core\";\nimport * as i6 from \"@angular/material/progress-spinner\";\nimport * as i7 from \"@angular/material/paginator\";\nconst _c0 = [\"filter\"];\nfunction ExpenseReportComponent_mat_header_cell_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 42);\n    i0.ɵɵtext(1, \"Id \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r24 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(row_r24.id);\n  }\n}\nfunction ExpenseReportComponent_mat_header_cell_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 42);\n    i0.ɵɵtext(1, \"Invoice No \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 43);\n    i0.ɵɵtext(2, \"Invoice No:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r25 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", row_r25.invoiceNo, \" \");\n  }\n}\nfunction ExpenseReportComponent_mat_header_cell_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 42);\n    i0.ɵɵtext(1, \"Date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 43);\n    i0.ɵɵtext(2, \"Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r26 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 1, row_r26.date, \"MM/dd/yyyy\"), \"\");\n  }\n}\nfunction ExpenseReportComponent_mat_header_cell_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 42);\n    i0.ɵɵtext(1, \"Expense \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 43);\n    i0.ɵɵtext(2, \"Expense:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r27 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", row_r27.expense, \" \");\n  }\n}\nfunction ExpenseReportComponent_mat_header_cell_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 44);\n    i0.ɵɵtext(1, \" Expense By \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 45)(1, \"span\", 43);\n    i0.ɵɵtext(2, \" Expense By:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"img\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r28 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", row_r28.img, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r28.name, \" \");\n  }\n}\nfunction ExpenseReportComponent_mat_header_cell_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 42);\n    i0.ɵɵtext(1, \"Amount \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 47)(1, \"span\", 43);\n    i0.ɵɵtext(2, \"Amount:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r29 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", row_r29.amount, \" \");\n  }\n}\nfunction ExpenseReportComponent_mat_header_cell_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 42);\n    i0.ɵɵtext(1, \"Payment Mode \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 43);\n    i0.ɵɵtext(2, \"Payment Mode:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r30 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r30.pmode, \" \");\n  }\n}\nfunction ExpenseReportComponent_mat_header_cell_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 42);\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_48_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r31 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r31.status, \"\");\n  }\n}\nfunction ExpenseReportComponent_mat_cell_48_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 50);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r31 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r31.status, \"\");\n  }\n}\nfunction ExpenseReportComponent_mat_cell_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 48)(1, \"span\", 43);\n    i0.ɵɵtext(2, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ExpenseReportComponent_mat_cell_48_Conditional_3_Template, 3, 1, \"div\")(4, ExpenseReportComponent_mat_cell_48_Conditional_4_Template, 3, 1, \"div\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r31 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(3, row_r31.status === \"Paid\" ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, row_r31.status === \"Unpaid\" ? 4 : -1);\n  }\n}\nfunction ExpenseReportComponent_mat_header_cell_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 42);\n    i0.ɵɵtext(1, \"Paid To \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExpenseReportComponent_mat_cell_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 43);\n    i0.ɵɵtext(2, \"Paid To:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r36 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r36.paidTo, \" \");\n  }\n}\nfunction ExpenseReportComponent_mat_header_row_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction ExpenseReportComponent_mat_row_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\", 51);\n  }\n  if (rf & 2) {\n    i0.ɵɵstyleProp(\"cursor\", \"pointer\");\n  }\n}\nfunction ExpenseReportComponent_Conditional_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"diameter\", 40);\n  }\n}\nfunction ExpenseReportComponent_Conditional_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \" No results \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r22.dataSource.renderedData.length === 0 ? \"\" : \"none\");\n  }\n}\nconst _c1 = () => [\"Reports\"];\nconst _c2 = () => [5, 10, 25, 100];\nexport let ExpenseReportComponent = /*#__PURE__*/(() => {\n  class ExpenseReportComponent extends UnsubscribeOnDestroyAdapter {\n    constructor(httpClient, leavesService) {\n      super();\n      this.httpClient = httpClient;\n      this.leavesService = leavesService;\n      this.filterToggle = false;\n      this.displayedColumns = ['invoiceNo', 'date', 'expense', 'img', 'amount', 'pmode', 'status', 'paidTo'];\n    }\n    ngOnInit() {\n      this.loadData();\n    }\n    toggleStar(row) {\n      console.log(row);\n    }\n    loadData() {\n      this.exampleDatabase = new ExpenseReportService(this.httpClient);\n      this.dataSource = new ExampleDataSource(this.exampleDatabase, this.paginator, this.sort);\n      this.subs.sink = fromEvent(this.filter.nativeElement, 'keyup').subscribe(() => {\n        if (!this.dataSource) {\n          return;\n        }\n        this.dataSource.filter = this.filter.nativeElement.value;\n      });\n    }\n    static #_ = this.ɵfac = function ExpenseReportComponent_Factory(t) {\n      return new (t || ExpenseReportComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.ExpenseReportService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ExpenseReportComponent,\n      selectors: [[\"app-expense\"]],\n      viewQuery: function ExpenseReportComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 7);\n          i0.ɵɵviewQuery(MatSort, 7);\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 58,\n      vars: 14,\n      consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\"], [1, \"col-md-12\"], [1, \"tabbable-line\"], [1, \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"card\"], [1, \"materialTableHeader\"], [1, \"left\"], [1, \"header-buttons-left\", \"ms-0\"], [1, \"tbl-title\"], [1, \"tbl-search-box\"], [\"for\", \"search-input\"], [1, \"material-icons\", \"search-icon\"], [\"placeholder\", \"Search\", \"type\", \"text\", \"aria-label\", \"Search box\", 1, \"browser-default\", \"search-field\"], [\"filter\", \"\"], [1, \"body\", \"overflow-auto\"], [1, \"responsive_table\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"mat-cell\", \"advance-table\", 3, \"dataSource\"], [\"matColumnDef\", \"id\"], [\"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [4, \"matCellDef\"], [\"matColumnDef\", \"invoiceNo\"], [\"matColumnDef\", \"date\"], [\"matColumnDef\", \"expense\"], [\"matColumnDef\", \"img\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", \"class\", \"table-img\", 4, \"matCellDef\"], [\"matColumnDef\", \"amount\"], [\"class\", \"column-nowrap\", 4, \"matCellDef\"], [\"matColumnDef\", \"pmode\"], [\"matColumnDef\", \"status\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"paidTo\"], [4, \"matHeaderRowDef\"], [\"matRipple\", \"\", 3, \"cursor\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"tbl-spinner\"], [\"class\", \"no-results\", 3, \"display\"], [3, \"length\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [\"paginator\", \"\"], [\"mat-sort-header\", \"\"], [1, \"mobile-label\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\", 1, \"table-img\"], [1, \"ms-2\", \"me-2\", 3, \"src\"], [1, \"column-nowrap\"], [\"mat-cell\", \"\"], [1, \"badge\", \"badge-solid-green\"], [1, \"badge\", \"badge-solid-red\"], [\"matRipple\", \"\"], [1, \"tbl-spinner\"], [\"color\", \"primary\", \"mode\", \"indeterminate\", 3, \"diameter\"], [1, \"no-results\"]],\n      template: function ExpenseReportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 4)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"ul\", 11)(13, \"li\", 12)(14, \"h2\");\n          i0.ɵɵtext(15, \"Expense Report\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\", 13)(17, \"label\", 14)(18, \"i\", 15);\n          i0.ɵɵtext(19, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"input\", 16, 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 18)(23, \"div\", 19)(24, \"table\", 20);\n          i0.ɵɵelementContainerStart(25, 21);\n          i0.ɵɵtemplate(26, ExpenseReportComponent_mat_header_cell_26_Template, 2, 0, \"mat-header-cell\", 22)(27, ExpenseReportComponent_mat_cell_27_Template, 2, 1, \"mat-cell\", 23);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(28, 24);\n          i0.ɵɵtemplate(29, ExpenseReportComponent_mat_header_cell_29_Template, 2, 0, \"mat-header-cell\", 22)(30, ExpenseReportComponent_mat_cell_30_Template, 4, 1, \"mat-cell\", 23);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(31, 25);\n          i0.ɵɵtemplate(32, ExpenseReportComponent_mat_header_cell_32_Template, 2, 0, \"mat-header-cell\", 22)(33, ExpenseReportComponent_mat_cell_33_Template, 5, 4, \"mat-cell\", 23);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(34, 26);\n          i0.ɵɵtemplate(35, ExpenseReportComponent_mat_header_cell_35_Template, 2, 0, \"mat-header-cell\", 22)(36, ExpenseReportComponent_mat_cell_36_Template, 4, 1, \"mat-cell\", 23);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(37, 27);\n          i0.ɵɵtemplate(38, ExpenseReportComponent_mat_header_cell_38_Template, 2, 0, \"mat-header-cell\", 28)(39, ExpenseReportComponent_mat_cell_39_Template, 5, 2, \"mat-cell\", 29);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(40, 30);\n          i0.ɵɵtemplate(41, ExpenseReportComponent_mat_header_cell_41_Template, 2, 0, \"mat-header-cell\", 22)(42, ExpenseReportComponent_mat_cell_42_Template, 4, 1, \"mat-cell\", 31);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(43, 32);\n          i0.ɵɵtemplate(44, ExpenseReportComponent_mat_header_cell_44_Template, 2, 0, \"mat-header-cell\", 22)(45, ExpenseReportComponent_mat_cell_45_Template, 4, 1, \"mat-cell\", 23);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(46, 33);\n          i0.ɵɵtemplate(47, ExpenseReportComponent_mat_header_cell_47_Template, 2, 0, \"mat-header-cell\", 22)(48, ExpenseReportComponent_mat_cell_48_Template, 5, 2, \"mat-cell\", 34);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(49, 35);\n          i0.ɵɵtemplate(50, ExpenseReportComponent_mat_header_cell_50_Template, 2, 0, \"mat-header-cell\", 22)(51, ExpenseReportComponent_mat_cell_51_Template, 4, 1, \"mat-cell\", 23);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(52, ExpenseReportComponent_mat_header_row_52_Template, 1, 0, \"mat-header-row\", 36)(53, ExpenseReportComponent_mat_row_53_Template, 1, 2, \"mat-row\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(54, ExpenseReportComponent_Conditional_54_Template, 2, 1, \"div\", 38)(55, ExpenseReportComponent_Conditional_55_Template, 2, 2, \"div\", 39);\n          i0.ɵɵelement(56, \"mat-paginator\", 40, 41);\n          i0.ɵɵelementEnd()()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"title\", \"Expense Report\")(\"items\", i0.ɵɵpureFunction0(12, _c1))(\"active_item\", \"Expense Report\");\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(54, (ctx.exampleDatabase == null ? null : ctx.exampleDatabase.isTblLoading) ? 54 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(55, !(ctx.exampleDatabase == null ? null : ctx.exampleDatabase.isTblLoading) ? 55 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"length\", ctx.dataSource.filteredData.length)(\"pageIndex\", 0)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(13, _c2));\n        }\n      },\n      dependencies: [BreadcrumbComponent, MatTableModule, i3.MatTable, i3.MatHeaderCellDef, i3.MatHeaderRowDef, i3.MatColumnDef, i3.MatCellDef, i3.MatRowDef, i3.MatHeaderCell, i3.MatCell, i3.MatHeaderRow, i3.MatRow, MatSortModule, i4.MatSort, i4.MatSortHeader, MatRippleModule, i5.MatRipple, MatProgressSpinnerModule, i6.MatProgressSpinner, MatPaginatorModule, i7.MatPaginator, DatePipe]\n    });\n  }\n  return ExpenseReportComponent;\n})();\nexport class ExampleDataSource extends DataSource {\n  get filter() {\n    return this.filterChange.value;\n  }\n  set filter(filter) {\n    this.filterChange.next(filter);\n  }\n  constructor(exampleDatabase, paginator, _sort) {\n    super();\n    this.exampleDatabase = exampleDatabase;\n    this.paginator = paginator;\n    this._sort = _sort;\n    this.filterChange = new BehaviorSubject('');\n    this.filteredData = [];\n    this.renderedData = [];\n    // Reset to the first page when the user changes the filter.\n    this.filterChange.subscribe(() => this.paginator.pageIndex = 0);\n  }\n  /** Connect function called by the table to retrieve one stream containing the data to render. */\n  connect() {\n    // Listen for any changes in the base data, sorting, filtering, or pagination\n    const displayDataChanges = [this.exampleDatabase.dataChange, this._sort.sortChange, this.filterChange, this.paginator.page];\n    this.exampleDatabase.getAllLeavess();\n    return merge(...displayDataChanges).pipe(map(() => {\n      // Filter data\n      this.filteredData = this.exampleDatabase.data.slice().filter(leaves => {\n        const searchStr = leaves.name.toLowerCase();\n        return searchStr.indexOf(this.filter.toLowerCase()) !== -1;\n      });\n      // Sort filtered data\n      const sortedData = this.sortData(this.filteredData.slice());\n      // Grab the page's slice of the filtered sorted data.\n      const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n      this.renderedData = sortedData.splice(startIndex, this.paginator.pageSize);\n      return this.renderedData;\n    }));\n  }\n  disconnect() {\n    //disconnect\n  }\n  /** Returns a sorted copy of the database data. */\n  sortData(data) {\n    if (!this._sort.active || this._sort.direction === '') {\n      return data;\n    }\n    return data.sort((a, b) => {\n      let propertyA = '';\n      let propertyB = '';\n      switch (this._sort.active) {\n        case 'id':\n          [propertyA, propertyB] = [a.id, b.id];\n          break;\n        case 'name':\n          [propertyA, propertyB] = [a.name, b.name];\n          break;\n        case 'status':\n          [propertyA, propertyB] = [a.status, b.status];\n          break;\n        case 'amount':\n          [propertyA, propertyB] = [a.amount, b.amount];\n          break;\n        case 'pmode':\n          [propertyA, propertyB] = [a.pmode, b.pmode];\n          break;\n      }\n      const valueA = isNaN(+propertyA) ? propertyA : +propertyA;\n      const valueB = isNaN(+propertyB) ? propertyB : +propertyB;\n      return (valueA < valueB ? -1 : 1) * (this._sort.direction === 'asc' ? 1 : -1);\n    });\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}