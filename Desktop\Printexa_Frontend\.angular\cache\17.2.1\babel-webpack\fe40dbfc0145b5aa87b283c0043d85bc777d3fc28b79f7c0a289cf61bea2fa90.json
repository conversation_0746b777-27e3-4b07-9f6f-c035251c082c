{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { RouterLink } from '@angular/router';\nimport { Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatButtonModule } from '@angular/material/button';\nlet SigninComponent = class SigninComponent extends UnsubscribeOnDestroyAdapter {\n  constructor(formBuilder, route, router, adminService) {\n    super();\n    this.formBuilder = formBuilder;\n    this.route = route;\n    this.router = router;\n    this.adminService = adminService;\n    this.submitted = false;\n    this.loading = false;\n    this.error = '';\n    this.hide = true;\n  }\n  ngOnInit() {\n    this.authForm = this.formBuilder.group({\n      username: ['<EMAIL>', Validators.required],\n      password: ['Admin@123', Validators.required]\n    });\n  }\n  get f() {\n    return this.authForm.controls;\n  }\n  adminSet() {\n    this.authForm.get('username')?.setValue('<EMAIL>');\n    this.authForm.get('password')?.setValue('Admin@123');\n  }\n  onSubmit() {\n    this.submitted = true;\n    this.loading = true;\n    this.error = '';\n    if (this.authForm.invalid) {\n      this.error = 'Username and Password are required!';\n      this.loading = false;\n      return;\n    }\n    this.subs.sink = this.adminService.login(this.f['username'].value, this.f['password'].value).subscribe({\n      next: result => {\n        if (result.success) {\n          setTimeout(() => {\n            this.router.navigate(['/admin/dashboard/main']);\n            this.loading = false;\n          }, 1000);\n        } else {\n          this.error = result.error || 'Invalid credentials';\n          this.loading = false;\n        }\n      },\n      error: error => {\n        this.error = error.message || 'Login failed';\n        this.submitted = false;\n        this.loading = false;\n      }\n    });\n  }\n};\nSigninComponent = __decorate([Component({\n  selector: 'app-signin',\n  templateUrl: './signin.component.html',\n  styleUrls: ['./signin.component.scss'],\n  standalone: true,\n  imports: [RouterLink, MatButtonModule, FormsModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatIconModule]\n})], SigninComponent);\nexport { SigninComponent };", "map": {"version": 3, "names": ["Component", "RouterLink", "Validators", "FormsModule", "ReactiveFormsModule", "UnsubscribeOnDestroyAdapter", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatButtonModule", "SigninComponent", "constructor", "formBuilder", "route", "router", "adminService", "submitted", "loading", "error", "hide", "ngOnInit", "authForm", "group", "username", "required", "password", "f", "controls", "adminSet", "get", "setValue", "onSubmit", "invalid", "subs", "sink", "login", "value", "subscribe", "next", "result", "success", "setTimeout", "navigate", "message", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\authentication\\signin\\signin.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router, ActivatedRoute, RouterLink } from '@angular/router';\r\nimport { UntypedFormBuilder, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { AdminService } from '';\r\nimport { Admin ,AuthResult} from '../../admin';\r\nimport { Role } from '@core/models/admin/role.enum';\r\n\r\n@Component({\r\n    selector: 'app-signin',\r\n    templateUrl: './signin.component.html',\r\n    styleUrls: ['./signin.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        RouterLink,\r\n        MatButtonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        MatFormFieldModule,\r\n        MatInputModule,\r\n        MatIconModule,\r\n    ],\r\n})\r\nexport class SigninComponent extends UnsubscribeOnDestroyAdapter implements OnInit {\r\n  authForm!: UntypedFormGroup;\r\n  submitted = false;\r\n  loading = false;\r\n  error = '';\r\n  hide = true;\r\n\r\n  constructor(\r\n    private formBuilder: UntypedFormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private adminService: AdminService\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.authForm = this.formBuilder.group({\r\n      username: ['<EMAIL>', Validators.required],\r\n      password: ['Admin@123', Validators.required],\r\n    });\r\n  }\r\n\r\n  get f() {\r\n    return this.authForm.controls;\r\n  }\r\n\r\n  adminSet() {\r\n    this.authForm.get('username')?.setValue('<EMAIL>');\r\n    this.authForm.get('password')?.setValue('Admin@123');\r\n  }\r\n\r\n  onSubmit() {\r\n    this.submitted = true;\r\n    this.loading = true;\r\n    this.error = '';\r\n    \r\n    if (this.authForm.invalid) {\r\n      this.error = 'Username and Password are required!';\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.subs.sink = this.adminService.login(\r\n      this.f['username'].value,\r\n      this.f['password'].value\r\n    ).subscribe({\r\n      next: (result) => {\r\n        if (result.success) {\r\n          setTimeout(() => {\r\n            this.router.navigate(['/admin/dashboard/main']);\r\n            this.loading = false;\r\n          }, 1000);\r\n        } else {\r\n          this.error = result.error || 'Invalid credentials';\r\n          this.loading = false;\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.error = error.message || 'Login failed';\r\n        this.submitted = false;\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n}"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAAiCC,UAAU,QAAQ,iBAAiB;AACpE,SAA+CC,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACnH,SAASC,2BAA2B,QAAQ,SAAS;AACrD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAoBnD,IAAMC,eAAe,GAArB,MAAMA,eAAgB,SAAQL,2BAA2B;EAO9DM,YACUC,WAA+B,EAC/BC,KAAqB,EACrBC,MAAc,EACdC,YAA0B;IAElC,KAAK,EAAE;IALC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IATtB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,IAAI,GAAG,IAAI;EASX;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACT,WAAW,CAACU,KAAK,CAAC;MACrCC,QAAQ,EAAE,CAAC,oBAAoB,EAAErB,UAAU,CAACsB,QAAQ,CAAC;MACrDC,QAAQ,EAAE,CAAC,WAAW,EAAEvB,UAAU,CAACsB,QAAQ;KAC5C,CAAC;EACJ;EAEA,IAAIE,CAACA,CAAA;IACH,OAAO,IAAI,CAACL,QAAQ,CAACM,QAAQ;EAC/B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACP,QAAQ,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAEC,QAAQ,CAAC,oBAAoB,CAAC;IAC7D,IAAI,CAACT,QAAQ,CAACQ,GAAG,CAAC,UAAU,CAAC,EAAEC,QAAQ,CAAC,WAAW,CAAC;EACtD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;IAEf,IAAI,IAAI,CAACG,QAAQ,CAACW,OAAO,EAAE;MACzB,IAAI,CAACd,KAAK,GAAG,qCAAqC;MAClD,IAAI,CAACD,OAAO,GAAG,KAAK;MACpB;;IAGF,IAAI,CAACgB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACnB,YAAY,CAACoB,KAAK,CACtC,IAAI,CAACT,CAAC,CAAC,UAAU,CAAC,CAACU,KAAK,EACxB,IAAI,CAACV,CAAC,CAAC,UAAU,CAAC,CAACU,KAAK,CACzB,CAACC,SAAS,CAAC;MACVC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,OAAO,EAAE;UAClBC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;YAC/C,IAAI,CAACzB,OAAO,GAAG,KAAK;UACtB,CAAC,EAAE,IAAI,CAAC;SACT,MAAM;UACL,IAAI,CAACC,KAAK,GAAGqB,MAAM,CAACrB,KAAK,IAAI,qBAAqB;UAClD,IAAI,CAACD,OAAO,GAAG,KAAK;;MAExB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAGA,KAAK,CAACyB,OAAO,IAAI,cAAc;QAC5C,IAAI,CAAC3B,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;CACD;AAjEYP,eAAe,GAAAkC,UAAA,EAf3B5C,SAAS,CAAC;EACP6C,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,yBAAyB;EACtCC,SAAS,EAAE,CAAC,yBAAyB,CAAC;EACtCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACLhD,UAAU,EACVQ,eAAe,EACfN,WAAW,EACXC,mBAAmB,EACnBI,kBAAkB,EAClBD,cAAc,EACdD,aAAa;CAEpB,CAAC,C,EACWI,eAAe,CAiE3B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}