{"ast": null, "code": "import { Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatOptionModule } from '@angular/material/core';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/form-field\";\nimport * as i3 from \"@angular/material/input\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/checkbox\";\nimport * as i8 from \"@angular/material/button\";\nfunction FormExamplesComponent_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" First name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" City name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" State name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Country is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" First name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_142_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" City name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_165_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" State name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_188_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Country is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_215_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" First name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_231_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_240_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_255_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" City name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_263_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" State name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormExamplesComponent_Conditional_286_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Country is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = () => [\"Home\", \"Forms\"];\nexport class FormExamplesComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.hide = true;\n    this.hide2 = true;\n    this.hide3 = true;\n    this.initForm();\n    this.initSecondForm();\n    this.initThirdForm();\n  }\n  initForm() {\n    this.register = this.fb.group({\n      first: ['', [Validators.required, Validators.pattern('[a-zA-Z]+')]],\n      last: [''],\n      password: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email, Validators.minLength(5)]],\n      address: [''],\n      city: ['', [Validators.required]],\n      state: ['', [Validators.required]],\n      country: ['', [Validators.required]],\n      termcondition: [false, [Validators.requiredTrue]]\n    });\n  }\n  initSecondForm() {\n    this.secondForm = this.fb.group({\n      first: ['', [Validators.required, Validators.pattern('[a-zA-Z]+')]],\n      last: [''],\n      password: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email, Validators.minLength(5)]],\n      address: [''],\n      city: ['', [Validators.required]],\n      state: ['', [Validators.required]],\n      country: ['', [Validators.required]],\n      termcondition: [false, [Validators.requiredTrue]]\n    });\n  }\n  initThirdForm() {\n    this.thirdForm = this.fb.group({\n      first: ['', [Validators.required, Validators.pattern('[a-zA-Z]+')]],\n      last: [''],\n      password: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email, Validators.minLength(5)]],\n      address: [''],\n      city: ['', [Validators.required]],\n      state: ['', [Validators.required]],\n      country: ['', [Validators.required]],\n      termcondition: [false, [Validators.requiredTrue]]\n    });\n  }\n  onRegister() {\n    console.log('Form Value', this.register?.value);\n  }\n  onsecondFormSubmit() {\n    console.log('Form Value', this.secondForm?.value);\n  }\n  onThirdFormSubmit() {\n    console.log('Form Value', this.thirdForm?.value);\n  }\n  static #_ = this.ɵfac = function FormExamplesComponent_Factory(t) {\n    return new (t || FormExamplesComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FormExamplesComponent,\n    selectors: [[\"app-form-examples\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 297,\n    vars: 55,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\", \"clearfix\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\"], [1, \"card\"], [1, \"header\"], [1, \"body\"], [1, \"register-form\", \"m-4\", 3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"appearance\", \"outline\", 1, \"example-full-width\"], [\"matInput\", \"\", \"formControlName\", \"first\", \"required\", \"\"], [\"matSuffix\", \"\", 1, \"material-icons-outlined\", \"color-icon\", \"p-3\"], [\"matInput\", \"\", \"formControlName\", \"last\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"required\", \"\", 3, \"type\"], [\"matSuffix\", \"\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"address\"], [\"matInput\", \"\", \"formControlName\", \"city\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"state\", \"required\", \"\"], [\"formControlName\", \"country\", \"required\", \"\"], [3, \"value\"], [\"formControlName\", \"termcondition\", 1, \"example-margin\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"btn-space\", 3, \"disabled\"], [\"type\", \"button\", \"mat-raised-button\", \"\", \"color\", \"warn\"], [1, \"m-4\", 3, \"formGroup\", \"ngSubmit\"], [\"appearance\", \"fill\", 1, \"example-full-width\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\", \"mb-3\"], [1, \"example-full-width\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mb-3\"]],\n    template: function FormExamplesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Reactive Forms (Outline)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function FormExamplesComponent_Template_form_ngSubmit_11_listener() {\n          return ctx.onRegister();\n        });\n        i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-form-field\", 12)(15, \"mat-label\");\n        i0.ɵɵtext(16, \"First name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"input\", 13);\n        i0.ɵɵelementStart(18, \"mat-icon\", 14);\n        i0.ɵɵtext(19, \"face\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(20, FormExamplesComponent_Conditional_20_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 11)(22, \"mat-form-field\", 12)(23, \"mat-label\");\n        i0.ɵɵtext(24, \"Last name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(25, \"input\", 15);\n        i0.ɵɵelementStart(26, \"mat-icon\", 14);\n        i0.ɵɵtext(27, \"face\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(28, \"div\", 10)(29, \"div\", 16)(30, \"mat-form-field\", 12)(31, \"mat-label\");\n        i0.ɵɵtext(32, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"input\", 17);\n        i0.ɵɵelementStart(34, \"mat-icon\", 18);\n        i0.ɵɵlistener(\"click\", function FormExamplesComponent_Template_mat_icon_click_34_listener() {\n          return ctx.hide = !ctx.hide;\n        });\n        i0.ɵɵtext(35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(36, FormExamplesComponent_Conditional_36_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(37, \"div\", 10)(38, \"div\", 16)(39, \"mat-form-field\", 12)(40, \"mat-label\");\n        i0.ɵɵtext(41, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(42, \"input\", 19);\n        i0.ɵɵelementStart(43, \"mat-icon\", 14);\n        i0.ɵɵtext(44, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(45, FormExamplesComponent_Conditional_45_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(46, \"div\", 10)(47, \"div\", 16)(48, \"mat-form-field\", 12)(49, \"mat-label\");\n        i0.ɵɵtext(50, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(51, \"textarea\", 20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(52, \"div\", 10)(53, \"div\", 11)(54, \"mat-form-field\", 12)(55, \"mat-label\");\n        i0.ɵɵtext(56, \"City\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(57, \"input\", 21);\n        i0.ɵɵelementStart(58, \"mat-icon\", 14);\n        i0.ɵɵtext(59, \"location_city\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(60, FormExamplesComponent_Conditional_60_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(61, \"div\", 11)(62, \"mat-form-field\", 12)(63, \"mat-label\");\n        i0.ɵɵtext(64, \"State\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(65, \"input\", 22);\n        i0.ɵɵelementStart(66, \"mat-icon\", 14);\n        i0.ɵɵtext(67, \"business\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(68, FormExamplesComponent_Conditional_68_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(69, \"div\", 10)(70, \"div\", 16)(71, \"mat-form-field\", 12)(72, \"mat-label\");\n        i0.ɵɵtext(73, \"Country\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"mat-select\", 23)(75, \"mat-option\", 24);\n        i0.ɵɵtext(76, \" India \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(77, \"mat-option\", 24);\n        i0.ɵɵtext(78, \" Algeria \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(79, \"mat-option\", 24);\n        i0.ɵɵtext(80, \" Brazil \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"mat-option\", 24);\n        i0.ɵɵtext(82, \" Poland \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(83, \"mat-option\", 24);\n        i0.ɵɵtext(84, \" Sri Lanka \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(85, \"mat-option\", 24);\n        i0.ɵɵtext(86, \" Thailand \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(87, \"mat-option\", 24);\n        i0.ɵɵtext(88, \" Japan \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(89, \"mat-icon\", 14);\n        i0.ɵɵtext(90, \"flag\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(91, FormExamplesComponent_Conditional_91_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(92, \"div\", 10)(93, \"div\", 16)(94, \"mat-checkbox\", 25);\n        i0.ɵɵtext(95, \"I accept terms and conditions \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(96, \"div\", 10)(97, \"div\", 16)(98, \"button\", 26);\n        i0.ɵɵtext(99, \"Submit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(100, \"button\", 27);\n        i0.ɵɵtext(101, \"Cancel\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(102, \"div\", 5)(103, \"div\", 6)(104, \"div\", 7)(105, \"h2\");\n        i0.ɵɵtext(106, \"Reactive Forms (Fill)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(107, \"div\", 8)(108, \"form\", 28);\n        i0.ɵɵlistener(\"ngSubmit\", function FormExamplesComponent_Template_form_ngSubmit_108_listener() {\n          return ctx.onsecondFormSubmit();\n        });\n        i0.ɵɵelementStart(109, \"div\", 10)(110, \"div\", 11)(111, \"mat-form-field\", 29)(112, \"mat-label\");\n        i0.ɵɵtext(113, \"First name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(114, \"input\", 13);\n        i0.ɵɵelementStart(115, \"mat-icon\", 14);\n        i0.ɵɵtext(116, \"face\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(117, FormExamplesComponent_Conditional_117_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(118, \"div\", 11)(119, \"mat-form-field\", 29)(120, \"mat-label\");\n        i0.ɵɵtext(121, \"Last name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(122, \"input\", 15);\n        i0.ɵɵelementStart(123, \"mat-icon\", 14);\n        i0.ɵɵtext(124, \"face\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(125, \"div\", 10)(126, \"div\", 16)(127, \"mat-form-field\", 29)(128, \"mat-label\");\n        i0.ɵɵtext(129, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(130, \"input\", 17);\n        i0.ɵɵelementStart(131, \"mat-icon\", 18);\n        i0.ɵɵlistener(\"click\", function FormExamplesComponent_Template_mat_icon_click_131_listener() {\n          return ctx.hide2 = !ctx.hide2;\n        });\n        i0.ɵɵtext(132);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(133, FormExamplesComponent_Conditional_133_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(134, \"div\", 10)(135, \"div\", 16)(136, \"mat-form-field\", 29)(137, \"mat-label\");\n        i0.ɵɵtext(138, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(139, \"input\", 19);\n        i0.ɵɵelementStart(140, \"mat-icon\", 14);\n        i0.ɵɵtext(141, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(142, FormExamplesComponent_Conditional_142_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(143, \"div\", 10)(144, \"div\", 16)(145, \"mat-form-field\", 29)(146, \"mat-label\");\n        i0.ɵɵtext(147, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(148, \"textarea\", 20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(149, \"div\", 10)(150, \"div\", 11)(151, \"mat-form-field\", 29)(152, \"mat-label\");\n        i0.ɵɵtext(153, \"City\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(154, \"input\", 21);\n        i0.ɵɵelementStart(155, \"mat-icon\", 14);\n        i0.ɵɵtext(156, \"location_city\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(157, FormExamplesComponent_Conditional_157_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(158, \"div\", 11)(159, \"mat-form-field\", 29)(160, \"mat-label\");\n        i0.ɵɵtext(161, \"State\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(162, \"input\", 22);\n        i0.ɵɵelementStart(163, \"mat-icon\", 14);\n        i0.ɵɵtext(164, \"business\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(165, FormExamplesComponent_Conditional_165_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(166, \"div\", 10)(167, \"div\", 16)(168, \"mat-form-field\", 29)(169, \"mat-label\");\n        i0.ɵɵtext(170, \"Country\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(171, \"mat-select\", 23)(172, \"mat-option\", 24);\n        i0.ɵɵtext(173, \" India \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(174, \"mat-option\", 24);\n        i0.ɵɵtext(175, \" Algeria \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(176, \"mat-option\", 24);\n        i0.ɵɵtext(177, \" Brazil \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(178, \"mat-option\", 24);\n        i0.ɵɵtext(179, \" Poland \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(180, \"mat-option\", 24);\n        i0.ɵɵtext(181, \" Sri Lanka \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(182, \"mat-option\", 24);\n        i0.ɵɵtext(183, \" Thailand \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(184, \"mat-option\", 24);\n        i0.ɵɵtext(185, \" Japan \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(186, \"mat-icon\", 14);\n        i0.ɵɵtext(187, \"flag\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(188, FormExamplesComponent_Conditional_188_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(189, \"div\", 10)(190, \"div\", 16)(191, \"mat-checkbox\", 25);\n        i0.ɵɵtext(192, \"I accept terms and conditions \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(193, \"div\", 10)(194, \"div\", 16)(195, \"button\", 26);\n        i0.ɵɵtext(196, \"Submit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(197, \"button\", 27);\n        i0.ɵɵtext(198, \"Cancel\");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(199, \"div\", 4)(200, \"div\", 30)(201, \"div\", 6)(202, \"div\", 7)(203, \"h2\");\n        i0.ɵɵtext(204, \"Reactive Forms (Standard)\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(205, \"div\", 8)(206, \"form\", 28);\n        i0.ɵɵlistener(\"ngSubmit\", function FormExamplesComponent_Template_form_ngSubmit_206_listener() {\n          return ctx.onThirdFormSubmit();\n        });\n        i0.ɵɵelementStart(207, \"div\", 10)(208, \"div\", 31)(209, \"mat-form-field\", 32)(210, \"mat-label\");\n        i0.ɵɵtext(211, \"First name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(212, \"input\", 13);\n        i0.ɵɵelementStart(213, \"mat-icon\", 14);\n        i0.ɵɵtext(214, \"face\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(215, FormExamplesComponent_Conditional_215_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(216, \"div\", 31)(217, \"mat-form-field\", 32)(218, \"mat-label\");\n        i0.ɵɵtext(219, \"Last name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(220, \"input\", 15);\n        i0.ɵɵelementStart(221, \"mat-icon\", 14);\n        i0.ɵɵtext(222, \"face\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(223, \"div\", 10)(224, \"div\", 33)(225, \"mat-form-field\", 32)(226, \"mat-label\");\n        i0.ɵɵtext(227, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(228, \"input\", 17);\n        i0.ɵɵelementStart(229, \"mat-icon\", 18);\n        i0.ɵɵlistener(\"click\", function FormExamplesComponent_Template_mat_icon_click_229_listener() {\n          return ctx.hide3 = !ctx.hide3;\n        });\n        i0.ɵɵtext(230);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(231, FormExamplesComponent_Conditional_231_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(232, \"div\", 10)(233, \"div\", 33)(234, \"mat-form-field\", 32)(235, \"mat-label\");\n        i0.ɵɵtext(236, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(237, \"input\", 19);\n        i0.ɵɵelementStart(238, \"mat-icon\", 14);\n        i0.ɵɵtext(239, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(240, FormExamplesComponent_Conditional_240_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(241, \"div\", 10)(242, \"div\", 33)(243, \"mat-form-field\", 32)(244, \"mat-label\");\n        i0.ɵɵtext(245, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(246, \"textarea\", 20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(247, \"div\", 10)(248, \"div\", 31)(249, \"mat-form-field\", 32)(250, \"mat-label\");\n        i0.ɵɵtext(251, \"City\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(252, \"input\", 21);\n        i0.ɵɵelementStart(253, \"mat-icon\", 14);\n        i0.ɵɵtext(254, \"location_city\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(255, FormExamplesComponent_Conditional_255_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(256, \"div\", 31)(257, \"mat-form-field\", 32)(258, \"mat-label\");\n        i0.ɵɵtext(259, \"State\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(260, \"input\", 22);\n        i0.ɵɵelementStart(261, \"mat-icon\", 14);\n        i0.ɵɵtext(262, \"business\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(263, FormExamplesComponent_Conditional_263_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(264, \"div\", 10)(265, \"div\", 33)(266, \"mat-form-field\", 32)(267, \"mat-label\");\n        i0.ɵɵtext(268, \"Country\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(269, \"mat-select\", 23)(270, \"mat-option\", 24);\n        i0.ɵɵtext(271, \" India \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(272, \"mat-option\", 24);\n        i0.ɵɵtext(273, \" Algeria \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(274, \"mat-option\", 24);\n        i0.ɵɵtext(275, \" Brazil \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(276, \"mat-option\", 24);\n        i0.ɵɵtext(277, \" Poland \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(278, \"mat-option\", 24);\n        i0.ɵɵtext(279, \" Sri Lanka \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(280, \"mat-option\", 24);\n        i0.ɵɵtext(281, \" Thailand \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(282, \"mat-option\", 24);\n        i0.ɵɵtext(283, \" Japan \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(284, \"mat-icon\", 14);\n        i0.ɵɵtext(285, \"flag\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(286, FormExamplesComponent_Conditional_286_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(287, \"div\", 10)(288, \"div\", 33)(289, \"mat-checkbox\", 25);\n        i0.ɵɵtext(290, \"I accept terms and conditions \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(291, \"div\", 10)(292, \"div\", 33)(293, \"button\", 26);\n        i0.ɵɵtext(294, \"Submit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(295, \"button\", 27);\n        i0.ɵɵtext(296, \"Cancel\");\n        i0.ɵɵelementEnd()()()()()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_4_0;\n        let tmp_7_0;\n        let tmp_8_0;\n        let tmp_9_0;\n        let tmp_10_0;\n        let tmp_18_0;\n        let tmp_21_0;\n        let tmp_24_0;\n        let tmp_25_0;\n        let tmp_26_0;\n        let tmp_27_0;\n        let tmp_35_0;\n        let tmp_38_0;\n        let tmp_41_0;\n        let tmp_42_0;\n        let tmp_43_0;\n        let tmp_44_0;\n        let tmp_52_0;\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Form Examples\")(\"items\", i0.ɵɵpureFunction0(54, _c0))(\"active_item\", \"Form Examples\");\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.register);\n        i0.ɵɵadvance(9);\n        i0.ɵɵconditional(20, (ctx.register == null ? null : (tmp_4_0 = ctx.register.get(\"first\")) == null ? null : tmp_4_0.hasError(\"required\")) ? 20 : -1);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"type\", ctx.hide ? \"password\" : \"text\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.hide ? \"visibility_off\" : \"visibility\", \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(36, (ctx.register == null ? null : (tmp_7_0 = ctx.register.get(\"password\")) == null ? null : tmp_7_0.hasError(\"required\")) ? 36 : -1);\n        i0.ɵɵadvance(9);\n        i0.ɵɵconditional(45, (ctx.register == null ? null : (tmp_8_0 = ctx.register.get(\"email\")) == null ? null : tmp_8_0.hasError(\"required\")) || (ctx.register == null ? null : (tmp_8_0 = ctx.register.get(\"email\")) == null ? null : tmp_8_0.touched) ? 45 : -1);\n        i0.ɵɵadvance(15);\n        i0.ɵɵconditional(60, (ctx.register == null ? null : (tmp_9_0 = ctx.register.get(\"city\")) == null ? null : tmp_9_0.hasError(\"required\")) ? 60 : -1);\n        i0.ɵɵadvance(8);\n        i0.ɵɵconditional(68, (ctx.register == null ? null : (tmp_10_0 = ctx.register.get(\"state\")) == null ? null : tmp_10_0.hasError(\"required\")) ? 68 : -1);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"value\", \"India\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Algeria\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Brazil\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Poland\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Sri Lanka\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Thailand\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Japan\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(91, (ctx.register == null ? null : (tmp_18_0 = ctx.register.get(\"country\")) == null ? null : tmp_18_0.hasError(\"required\")) ? 91 : -1);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"disabled\", !(ctx.register == null ? null : ctx.register.valid));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"formGroup\", ctx.secondForm);\n        i0.ɵɵadvance(9);\n        i0.ɵɵconditional(117, (ctx.secondForm == null ? null : (tmp_21_0 = ctx.secondForm.get(\"first\")) == null ? null : tmp_21_0.hasError(\"required\")) ? 117 : -1);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"type\", ctx.hide2 ? \"password\" : \"text\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.hide2 ? \"visibility_off\" : \"visibility\", \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(133, (ctx.secondForm == null ? null : (tmp_24_0 = ctx.secondForm.get(\"password\")) == null ? null : tmp_24_0.hasError(\"required\")) ? 133 : -1);\n        i0.ɵɵadvance(9);\n        i0.ɵɵconditional(142, (ctx.secondForm == null ? null : (tmp_25_0 = ctx.secondForm.get(\"email\")) == null ? null : tmp_25_0.hasError(\"email\")) && (ctx.secondForm == null ? null : (tmp_25_0 = ctx.secondForm.get(\"email\")) == null ? null : tmp_25_0.touched) ? 142 : -1);\n        i0.ɵɵadvance(15);\n        i0.ɵɵconditional(157, (ctx.secondForm == null ? null : (tmp_26_0 = ctx.secondForm.get(\"city\")) == null ? null : tmp_26_0.hasError(\"required\")) ? 157 : -1);\n        i0.ɵɵadvance(8);\n        i0.ɵɵconditional(165, (ctx.secondForm == null ? null : (tmp_27_0 = ctx.secondForm.get(\"state\")) == null ? null : tmp_27_0.hasError(\"required\")) ? 165 : -1);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"value\", \"India\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Algeria\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Brazil\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Poland\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Sri Lanka\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Thailand\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Japan\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(188, (ctx.secondForm == null ? null : (tmp_35_0 = ctx.secondForm.get(\"country\")) == null ? null : tmp_35_0.hasError(\"required\")) ? 188 : -1);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"disabled\", !(ctx.secondForm == null ? null : ctx.secondForm.valid));\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"formGroup\", ctx.thirdForm);\n        i0.ɵɵadvance(9);\n        i0.ɵɵconditional(215, (ctx.thirdForm == null ? null : (tmp_38_0 = ctx.thirdForm.get(\"first\")) == null ? null : tmp_38_0.hasError(\"required\")) ? 215 : -1);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"type\", ctx.hide3 ? \"password\" : \"text\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.hide3 ? \"visibility_off\" : \"visibility\", \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(231, (ctx.thirdForm == null ? null : (tmp_41_0 = ctx.thirdForm.get(\"password\")) == null ? null : tmp_41_0.hasError(\"required\")) ? 231 : -1);\n        i0.ɵɵadvance(9);\n        i0.ɵɵconditional(240, (ctx.thirdForm == null ? null : (tmp_42_0 = ctx.thirdForm.get(\"email\")) == null ? null : tmp_42_0.hasError(\"email\")) && (ctx.thirdForm == null ? null : (tmp_42_0 = ctx.thirdForm.get(\"email\")) == null ? null : tmp_42_0.touched) ? 240 : -1);\n        i0.ɵɵadvance(15);\n        i0.ɵɵconditional(255, (ctx.thirdForm == null ? null : (tmp_43_0 = ctx.thirdForm.get(\"city\")) == null ? null : tmp_43_0.hasError(\"required\")) ? 255 : -1);\n        i0.ɵɵadvance(8);\n        i0.ɵɵconditional(263, (ctx.thirdForm == null ? null : (tmp_44_0 = ctx.thirdForm.get(\"state\")) == null ? null : tmp_44_0.hasError(\"required\")) ? 263 : -1);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"value\", \"India\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Algeria\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Brazil\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Poland\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Sri Lanka\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Thailand\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Japan\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(286, (ctx.thirdForm == null ? null : (tmp_52_0 = ctx.thirdForm.get(\"country\")) == null ? null : tmp_52_0.hasError(\"required\")) ? 286 : -1);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"disabled\", !(ctx.thirdForm == null ? null : ctx.thirdForm.valid));\n      }\n    },\n    dependencies: [BreadcrumbComponent, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, MatFormFieldModule, i2.MatFormField, i2.MatLabel, i2.MatError, i2.MatSuffix, MatInputModule, i3.MatInput, MatIconModule, i4.MatIcon, MatSelectModule, i5.MatSelect, i6.MatOption, MatOptionModule, MatCheckboxModule, i7.MatCheckbox, MatButtonModule, i8.MatButton],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatOptionModule", "MatSelectModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "BreadcrumbComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "FormExamplesComponent", "constructor", "fb", "hide", "hide2", "hide3", "initForm", "initSecondForm", "initThirdForm", "register", "group", "first", "required", "pattern", "last", "password", "email", "<PERSON><PERSON><PERSON><PERSON>", "address", "city", "state", "country", "termcondition", "requiredTrue", "secondForm", "thirdForm", "onRegister", "console", "log", "value", "onsecondFormSubmit", "onThirdFormSubmit", "_", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FormExamplesComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "FormExamplesComponent_Template_form_ngSubmit_11_listener", "ɵɵtemplate", "FormExamplesComponent_Conditional_20_Template", "FormExamplesComponent_Template_mat_icon_click_34_listener", "FormExamplesComponent_Conditional_36_Template", "FormExamplesComponent_Conditional_45_Template", "FormExamplesComponent_Conditional_60_Template", "FormExamplesComponent_Conditional_68_Template", "FormExamplesComponent_Conditional_91_Template", "FormExamplesComponent_Template_form_ngSubmit_108_listener", "FormExamplesComponent_Conditional_117_Template", "FormExamplesComponent_Template_mat_icon_click_131_listener", "FormExamplesComponent_Conditional_133_Template", "FormExamplesComponent_Conditional_142_Template", "FormExamplesComponent_Conditional_157_Template", "FormExamplesComponent_Conditional_165_Template", "FormExamplesComponent_Conditional_188_Template", "FormExamplesComponent_Template_form_ngSubmit_206_listener", "FormExamplesComponent_Conditional_215_Template", "FormExamplesComponent_Template_mat_icon_click_229_listener", "FormExamplesComponent_Conditional_231_Template", "FormExamplesComponent_Conditional_240_Template", "FormExamplesComponent_Conditional_255_Template", "FormExamplesComponent_Conditional_263_Template", "FormExamplesComponent_Conditional_286_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵconditional", "tmp_4_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "tmp_7_0", "tmp_8_0", "touched", "tmp_9_0", "tmp_10_0", "tmp_18_0", "valid", "tmp_21_0", "tmp_24_0", "tmp_25_0", "tmp_26_0", "tmp_27_0", "tmp_35_0", "tmp_38_0", "tmp_41_0", "tmp_42_0", "tmp_43_0", "tmp_44_0", "tmp_52_0", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "i2", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i3", "MatInput", "i4", "MatIcon", "i5", "MatSelect", "i6", "MatOption", "i7", "MatCheckbox", "i8", "MatButton", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\forms\\form-examples\\form-examples.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\forms\\form-examples\\form-examples.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport {\r\n  UntypedFormBuilder,\r\n  UntypedFormGroup,\r\n  Validators,\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n} from '@angular/forms';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatOptionModule } from '@angular/material/core';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n@Component({\r\n  selector: 'app-form-examples',\r\n  templateUrl: './form-examples.component.html',\r\n  styleUrls: ['./form-examples.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    BreadcrumbComponent,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatIconModule,\r\n    MatSelectModule,\r\n    MatOptionModule,\r\n    MatCheckboxModule,\r\n    MatButtonModule,\r\n  ],\r\n})\r\nexport class FormExamplesComponent {\r\n  // Form 1\r\n  register?: UntypedFormGroup;\r\n  hide = true;\r\n  // Form 2\r\n  secondForm?: UntypedFormGroup;\r\n  hide2 = true;\r\n  // Form 3\r\n  thirdForm?: UntypedFormGroup;\r\n  hide3 = true;\r\n  constructor(private fb: UntypedFormBuilder) {\r\n    this.initForm();\r\n    this.initSecondForm();\r\n    this.initThirdForm();\r\n  }\r\n  initForm() {\r\n    this.register = this.fb.group({\r\n      first: ['', [Validators.required, Validators.pattern('[a-zA-Z]+')]],\r\n      last: [''],\r\n      password: ['', [Validators.required]],\r\n      email: [\r\n        '',\r\n        [Validators.required, Validators.email, Validators.minLength(5)],\r\n      ],\r\n      address: [''],\r\n      city: ['', [Validators.required]],\r\n      state: ['', [Validators.required]],\r\n      country: ['', [Validators.required]],\r\n      termcondition: [false, [Validators.requiredTrue]],\r\n    });\r\n  }\r\n  initSecondForm() {\r\n    this.secondForm = this.fb.group({\r\n      first: ['', [Validators.required, Validators.pattern('[a-zA-Z]+')]],\r\n      last: [''],\r\n      password: ['', [Validators.required]],\r\n      email: [\r\n        '',\r\n        [Validators.required, Validators.email, Validators.minLength(5)],\r\n      ],\r\n      address: [''],\r\n      city: ['', [Validators.required]],\r\n      state: ['', [Validators.required]],\r\n      country: ['', [Validators.required]],\r\n      termcondition: [false, [Validators.requiredTrue]],\r\n    });\r\n  }\r\n  initThirdForm() {\r\n    this.thirdForm = this.fb.group({\r\n      first: ['', [Validators.required, Validators.pattern('[a-zA-Z]+')]],\r\n      last: [''],\r\n      password: ['', [Validators.required]],\r\n      email: [\r\n        '',\r\n        [Validators.required, Validators.email, Validators.minLength(5)],\r\n      ],\r\n      address: [''],\r\n      city: ['', [Validators.required]],\r\n      state: ['', [Validators.required]],\r\n      country: ['', [Validators.required]],\r\n      termcondition: [false, [Validators.requiredTrue]],\r\n    });\r\n  }\r\n  onRegister() {\r\n    console.log('Form Value', this.register?.value);\r\n  }\r\n  onsecondFormSubmit() {\r\n    console.log('Form Value', this.secondForm?.value);\r\n  }\r\n  onThirdFormSubmit() {\r\n    console.log('Form Value', this.thirdForm?.value);\r\n  }\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Form Examples'\" [items]=\"['Home','Forms']\" [active_item]=\"'Form Examples'\">\r\n      </app-breadcrumb>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Reactive Forms (Outline)</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <form class=\"register-form m-4\" [formGroup]=\"register!\" (ngSubmit)=\"onRegister()\">\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                    <mat-label>First name</mat-label>\r\n                    <input matInput formControlName=\"first\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>face</mat-icon>\r\n                    @if (register?.get('first')?.hasError('required')) {\r\n                    <mat-error>\r\n                      First name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                    <mat-label>Last name</mat-label>\r\n                    <input matInput formControlName=\"last\">\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>face</mat-icon>\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                    <mat-label>Password</mat-label>\r\n                    <input matInput formControlName=\"password\" [type]=\"hide ? 'password' : 'text'\" required>\r\n                    <mat-icon matSuffix (click)=\"hide = !hide\">\r\n                      {{hide ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n                    @if (register?.get('password')?.hasError('required')) {\r\n                    <mat-error>\r\n                      Password is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                    <mat-label>Email</mat-label>\r\n                    <input matInput formControlName=\"email\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>email</mat-icon>\r\n                    @if (register?.get('email')?.hasError('required') || register?.get('email')?.touched) {\r\n                    <mat-error>\r\n                      Please enter a valid email address\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                    <mat-label>Address</mat-label>\r\n                    <textarea matInput formControlName=\"address\"></textarea>\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                    <mat-label>City</mat-label>\r\n                    <input matInput formControlName=\"city\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>location_city</mat-icon>\r\n                    @if (register?.get('city')?.hasError('required')) {\r\n                    <mat-error>\r\n                      City name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                    <mat-label>State</mat-label>\r\n                    <input matInput formControlName=\"state\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>business</mat-icon>\r\n                    @if (register?.get('state')?.hasError('required')) {\r\n                    <mat-error>\r\n                      State name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"outline\">\r\n                    <mat-label>Country</mat-label>\r\n                    <mat-select formControlName=\"country\" required>\r\n                      <mat-option [value]=\"'India'\">\r\n                        India\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Algeria'\">\r\n                        Algeria\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Brazil'\">\r\n                        Brazil\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Poland'\">\r\n                        Poland\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Sri Lanka'\">\r\n                        Sri Lanka\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Thailand'\">\r\n                        Thailand\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Japan'\">\r\n                        Japan\r\n                      </mat-option>\r\n                    </mat-select>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>flag</mat-icon>\r\n                    @if (register?.get('country')?.hasError('required')) {\r\n                    <mat-error>\r\n                      Country is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-checkbox class=\"example-margin\" formControlName=\"termcondition\">I accept\r\n                    terms\r\n                    and conditions\r\n                  </mat-checkbox>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <button class=\"btn-space\" [disabled]=\"!register?.valid\" mat-raised-button\r\n                    color=\"primary\">Submit</button>\r\n                  <button type=\"button\" mat-raised-button color=\"warn\">Cancel</button>\r\n                </div>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Reactive Forms (Fill)</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <form class=\"m-4\" [formGroup]=\"secondForm!\" (ngSubmit)=\"onsecondFormSubmit()\">\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"fill\">\r\n                    <mat-label>First name</mat-label>\r\n                    <input matInput formControlName=\"first\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>face</mat-icon>\r\n                    @if (secondForm?.get('first')?.hasError('required')) {\r\n                    <mat-error>\r\n                      First name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"fill\">\r\n                    <mat-label>Last name</mat-label>\r\n                    <input matInput formControlName=\"last\">\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>face</mat-icon>\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"fill\">\r\n                    <mat-label>Password</mat-label>\r\n                    <input matInput formControlName=\"password\" [type]=\"hide2 ? 'password' : 'text'\" required>\r\n                    <mat-icon matSuffix (click)=\"hide2 = !hide2\">\r\n                      {{hide2 ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n                    @if (secondForm?.get('password')?.hasError('required')) {\r\n                    <mat-error>\r\n                      Password is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"fill\">\r\n                    <mat-label>Email</mat-label>\r\n                    <input matInput formControlName=\"email\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>email</mat-icon>\r\n                    @if (secondForm?.get('email')?.hasError('email') && secondForm?.get('email')?.touched) {\r\n                    <mat-error>\r\n                      Please enter a valid email address\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"fill\">\r\n                    <mat-label>Address</mat-label>\r\n                    <textarea matInput formControlName=\"address\"></textarea>\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"fill\">\r\n                    <mat-label>City</mat-label>\r\n                    <input matInput formControlName=\"city\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>location_city</mat-icon>\r\n                    @if (secondForm?.get('city')?.hasError('required')) {\r\n                    <mat-error>\r\n                      City name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"fill\">\r\n                    <mat-label>State</mat-label>\r\n                    <input matInput formControlName=\"state\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>business</mat-icon>\r\n                    @if (secondForm?.get('state')?.hasError('required')) {\r\n                    <mat-error>\r\n                      State name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-form-field class=\"example-full-width\" appearance=\"fill\">\r\n                    <mat-label>Country</mat-label>\r\n                    <mat-select formControlName=\"country\" required>\r\n                      <mat-option [value]=\"'India'\">\r\n                        India\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Algeria'\">\r\n                        Algeria\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Brazil'\">\r\n                        Brazil\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Poland'\">\r\n                        Poland\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Sri Lanka'\">\r\n                        Sri Lanka\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Thailand'\">\r\n                        Thailand\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Japan'\">\r\n                        Japan\r\n                      </mat-option>\r\n                    </mat-select>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>flag</mat-icon>\r\n                    @if (secondForm?.get('country')?.hasError('required')) {\r\n                    <mat-error>\r\n                      Country is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <mat-checkbox class=\"example-margin\" formControlName=\"termcondition\">I accept\r\n                    terms\r\n                    and conditions\r\n                  </mat-checkbox>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n                  <button class=\"btn-space\" [disabled]=\"!secondForm?.valid\" mat-raised-button\r\n                    color=\"primary\">Submit</button>\r\n                  <button type=\"button\" mat-raised-button color=\"warn\">Cancel</button>\r\n                </div>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Reactive Forms (Standard)</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <form class=\"m-4\" [formGroup]=\"thirdForm!\" (ngSubmit)=\"onThirdFormSubmit()\">\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <mat-label>First name</mat-label>\r\n                    <input matInput formControlName=\"first\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>face</mat-icon>\r\n                    @if (thirdForm?.get('first')?.hasError('required')) {\r\n                    <mat-error>\r\n                      First name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <mat-label>Last name</mat-label>\r\n                    <input matInput formControlName=\"last\">\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>face</mat-icon>\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <mat-label>Password</mat-label>\r\n                    <input matInput formControlName=\"password\" [type]=\"hide3 ? 'password' : 'text'\" required>\r\n                    <mat-icon matSuffix (click)=\"hide3 = !hide3\">\r\n                      {{hide3 ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n                    @if (thirdForm?.get('password')?.hasError('required')) {\r\n                    <mat-error>\r\n                      Password is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <mat-label>Email</mat-label>\r\n                    <input matInput formControlName=\"email\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>email</mat-icon>\r\n                    @if (thirdForm?.get('email')?.hasError('email') && thirdForm?.get('email')?.touched) {\r\n                    <mat-error>\r\n                      Please enter a valid email address\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <mat-label>Address</mat-label>\r\n                    <textarea matInput formControlName=\"address\"></textarea>\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <mat-label>City</mat-label>\r\n                    <input matInput formControlName=\"city\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>location_city</mat-icon>\r\n                    @if (thirdForm?.get('city')?.hasError('required')) {\r\n                    <mat-error>\r\n                      City name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <mat-label>State</mat-label>\r\n                    <input matInput formControlName=\"state\" required>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>business</mat-icon>\r\n                    @if (thirdForm?.get('state')?.hasError('required')) {\r\n                    <mat-error>\r\n                      State name is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <mat-label>Country</mat-label>\r\n                    <mat-select formControlName=\"country\" required>\r\n                      <mat-option [value]=\"'India'\">\r\n                        India\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Algeria'\">\r\n                        Algeria\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Brazil'\">\r\n                        Brazil\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Poland'\">\r\n                        Poland\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Sri Lanka'\">\r\n                        Sri Lanka\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Thailand'\">\r\n                        Thailand\r\n                      </mat-option>\r\n                      <mat-option [value]=\"'Japan'\">\r\n                        Japan\r\n                      </mat-option>\r\n                    </mat-select>\r\n                    <mat-icon class=\"material-icons-outlined color-icon p-3\" matSuffix>flag</mat-icon>\r\n                    @if (thirdForm?.get('country')?.hasError('required')) {\r\n                    <mat-error>\r\n                      Country is required\r\n                    </mat-error>\r\n                    }\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-checkbox class=\"example-margin\" formControlName=\"termcondition\">I accept\r\n                    terms\r\n                    and conditions\r\n                  </mat-checkbox>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                  <button class=\"btn-space\" [disabled]=\"!thirdForm?.valid\" mat-raised-button\r\n                    color=\"primary\">Submit</button>\r\n                  <button type=\"button\" mat-raised-button color=\"warn\">Cancel</button>\r\n                </div>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AACA,SAGEA,UAAU,EACVC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,mBAAmB,QAAQ,oDAAoD;;;;;;;;;;;;ICOpEC,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAoBZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAYZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAoBZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAkCZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAsCZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAoBZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAYZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAoBZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAkCZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAwCZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAoBZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAYZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAoBZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAkCZH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;ADtYhC,OAAM,MAAOC,qBAAqB;EAUhCC,YAAoBC,EAAsB;IAAtB,KAAAA,EAAE,GAAFA,EAAE;IAPtB,KAAAC,IAAI,GAAG,IAAI;IAGX,KAAAC,KAAK,GAAG,IAAI;IAGZ,KAAAC,KAAK,GAAG,IAAI;IAEV,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,aAAa,EAAE;EACtB;EACAF,QAAQA,CAAA;IACN,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAC5BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;MACnEC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACrCI,KAAK,EAAE,CACL,EAAE,EACF,CAAC/B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,KAAK,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CACjE;MACDC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACjCQ,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MAClCS,OAAO,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACpCU,aAAa,EAAE,CAAC,KAAK,EAAE,CAACrC,UAAU,CAACsC,YAAY,CAAC;KACjD,CAAC;EACJ;EACAhB,cAAcA,CAAA;IACZ,IAAI,CAACiB,UAAU,GAAG,IAAI,CAACtB,EAAE,CAACQ,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;MACnEC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACrCI,KAAK,EAAE,CACL,EAAE,EACF,CAAC/B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,KAAK,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CACjE;MACDC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACjCQ,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MAClCS,OAAO,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACpCU,aAAa,EAAE,CAAC,KAAK,EAAE,CAACrC,UAAU,CAACsC,YAAY,CAAC;KACjD,CAAC;EACJ;EACAf,aAAaA,CAAA;IACX,IAAI,CAACiB,SAAS,GAAG,IAAI,CAACvB,EAAE,CAACQ,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;MACnEC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACrCI,KAAK,EAAE,CACL,EAAE,EACF,CAAC/B,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,KAAK,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,CAAC,CACjE;MACDC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACjCQ,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MAClCS,OAAO,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACpCU,aAAa,EAAE,CAAC,KAAK,EAAE,CAACrC,UAAU,CAACsC,YAAY,CAAC;KACjD,CAAC;EACJ;EACAG,UAAUA,CAAA;IACRC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACnB,QAAQ,EAAEoB,KAAK,CAAC;EACjD;EACAC,kBAAkBA,CAAA;IAChBH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACJ,UAAU,EAAEK,KAAK,CAAC;EACnD;EACAE,iBAAiBA,CAAA;IACfJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACH,SAAS,EAAEI,KAAK,CAAC;EAClD;EAAC,QAAAG,CAAA,G;qBAvEUhC,qBAAqB,EAAAJ,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBpC,qBAAqB;IAAAqC,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA3C,EAAA,CAAA4C,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClClClD,EAAA,CAAAC,cAAA,iBAAyB;QAInBD,EAAA,CAAAoD,SAAA,wBACiB;QACnBpD,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAA0B;QAIdD,EAAA,CAAAE,MAAA,+BAAwB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEnCH,EAAA,CAAAC,cAAA,cAAkB;QACwCD,EAAA,CAAAqD,UAAA,sBAAAC,yDAAA;UAAA,OAAYH,GAAA,CAAArB,UAAA,EAAY;QAAA,EAAC;QAC/E9B,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAoD,SAAA,iBAAiD;QACjDpD,EAAA,CAAAC,cAAA,oBAAmE;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClFH,EAAA,CAAAuD,UAAA,KAAAC,6CAAA,oBAIC;QACHxD,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,eAAwD;QAEzCD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAChCH,EAAA,CAAAoD,SAAA,iBAAuC;QACvCpD,EAAA,CAAAC,cAAA,oBAAmE;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAIxFH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAoD,SAAA,iBAAwF;QACxFpD,EAAA,CAAAC,cAAA,oBAA2C;QAAvBD,EAAA,CAAAqD,UAAA,mBAAAI,0DAAA;UAAA,OAAAN,GAAA,CAAA5C,IAAA,IAAA4C,GAAA,CAAA5C,IAAA;QAAA,EAAsB;QACxCP,EAAA,CAAAE,MAAA,IAA0C;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACvDH,EAAA,CAAAuD,UAAA,KAAAG,6CAAA,oBAIC;QACH1D,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAoD,SAAA,iBAAiD;QACjDpD,EAAA,CAAAC,cAAA,oBAAmE;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnFH,EAAA,CAAAuD,UAAA,KAAAI,6CAAA,oBAIC;QACH3D,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAoD,SAAA,oBAAwD;QAC1DpD,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC3BH,EAAA,CAAAoD,SAAA,iBAAgD;QAChDpD,EAAA,CAAAC,cAAA,oBAAmE;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3FH,EAAA,CAAAuD,UAAA,KAAAK,6CAAA,oBAIC;QACH5D,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,eAAwD;QAEzCD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAoD,SAAA,iBAAiD;QACjDpD,EAAA,CAAAC,cAAA,oBAAmE;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACtFH,EAAA,CAAAuD,UAAA,KAAAM,6CAAA,oBAIC;QACH7D,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,sBAA+C;QAE3CD,EAAA,CAAAE,MAAA,eACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,sBAAgC;QAC9BD,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,sBAA+B;QAC7BD,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,sBAA+B;QAC7BD,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,sBAAkC;QAChCD,EAAA,CAAAE,MAAA,mBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,sBAAiC;QAC/BD,EAAA,CAAAE,MAAA,kBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,sBAA8B;QAC5BD,EAAA,CAAAE,MAAA,eACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAEfH,EAAA,CAAAC,cAAA,oBAAmE;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClFH,EAAA,CAAAuD,UAAA,KAAAO,6CAAA,oBAIC;QACH9D,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAEwDD,EAAA,CAAAE,MAAA,sCAGrE;QAAAF,EAAA,CAAAG,YAAA,EAAe;QAGnBH,EAAA,CAAAC,cAAA,eAAiB;QAGKD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACjCH,EAAA,CAAAC,cAAA,mBAAqD;QAAAD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAOhFH,EAAA,CAAAC,cAAA,eAAmD;QAGzCD,EAAA,CAAAE,MAAA,8BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEhCH,EAAA,CAAAC,cAAA,eAAkB;QAC4BD,EAAA,CAAAqD,UAAA,sBAAAU,0DAAA;UAAA,OAAYZ,GAAA,CAAAjB,kBAAA,EAAoB;QAAA,EAAC;QAC3ElC,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAoD,SAAA,kBAAiD;QACjDpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClFH,EAAA,CAAAuD,UAAA,MAAAS,8CAAA,oBAIC;QACHhE,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,gBAAwD;QAEzCD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAChCH,EAAA,CAAAoD,SAAA,kBAAuC;QACvCpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAIxFH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAoD,SAAA,kBAAyF;QACzFpD,EAAA,CAAAC,cAAA,qBAA6C;QAAzBD,EAAA,CAAAqD,UAAA,mBAAAY,2DAAA;UAAA,OAAAd,GAAA,CAAA3C,KAAA,IAAA2C,GAAA,CAAA3C,KAAA;QAAA,EAAwB;QAC1CR,EAAA,CAAAE,MAAA,KAA2C;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACxDH,EAAA,CAAAuD,UAAA,MAAAW,8CAAA,oBAIC;QACHlE,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAoD,SAAA,kBAAiD;QACjDpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnFH,EAAA,CAAAuD,UAAA,MAAAY,8CAAA,oBAIC;QACHnE,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAoD,SAAA,qBAAwD;QAC1DpD,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC3BH,EAAA,CAAAoD,SAAA,kBAAgD;QAChDpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3FH,EAAA,CAAAuD,UAAA,MAAAa,8CAAA,oBAIC;QACHpE,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,gBAAwD;QAEzCD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAoD,SAAA,kBAAiD;QACjDpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACtFH,EAAA,CAAAuD,UAAA,MAAAc,8CAAA,oBAIC;QACHrE,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,uBAA+C;QAE3CD,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAAgC;QAC9BD,EAAA,CAAAE,MAAA,kBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAA+B;QAC7BD,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAA+B;QAC7BD,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAAkC;QAChCD,EAAA,CAAAE,MAAA,oBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAAiC;QAC/BD,EAAA,CAAAE,MAAA,mBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAA8B;QAC5BD,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAEfH,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClFH,EAAA,CAAAuD,UAAA,MAAAe,8CAAA,oBAIC;QACHtE,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAEwDD,EAAA,CAAAE,MAAA,uCAGrE;QAAAF,EAAA,CAAAG,YAAA,EAAe;QAGnBH,EAAA,CAAAC,cAAA,gBAAiB;QAGKD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACjCH,EAAA,CAAAC,cAAA,mBAAqD;QAAAD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAQlFH,EAAA,CAAAC,cAAA,eAA0B;QAIdD,EAAA,CAAAE,MAAA,kCAAyB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEpCH,EAAA,CAAAC,cAAA,eAAkB;QAC2BD,EAAA,CAAAqD,UAAA,sBAAAkB,0DAAA;UAAA,OAAYpB,GAAA,CAAAhB,iBAAA,EAAmB;QAAA,EAAC;QACzEnC,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAoD,SAAA,kBAAiD;QACjDpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClFH,EAAA,CAAAuD,UAAA,MAAAiB,8CAAA,oBAIC;QACHxE,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,gBAAwD;QAEzCD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAChCH,EAAA,CAAAoD,SAAA,kBAAuC;QACvCpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAIxFH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAoD,SAAA,kBAAyF;QACzFpD,EAAA,CAAAC,cAAA,qBAA6C;QAAzBD,EAAA,CAAAqD,UAAA,mBAAAoB,2DAAA;UAAA,OAAAtB,GAAA,CAAA1C,KAAA,IAAA0C,GAAA,CAAA1C,KAAA;QAAA,EAAwB;QAC1CT,EAAA,CAAAE,MAAA,KAA2C;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACxDH,EAAA,CAAAuD,UAAA,MAAAmB,8CAAA,oBAIC;QACH1E,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAoD,SAAA,kBAAiD;QACjDpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnFH,EAAA,CAAAuD,UAAA,MAAAoB,8CAAA,oBAIC;QACH3E,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAoD,SAAA,qBAAwD;QAC1DpD,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC3BH,EAAA,CAAAoD,SAAA,kBAAgD;QAChDpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3FH,EAAA,CAAAuD,UAAA,MAAAqB,8CAAA,oBAIC;QACH5E,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,gBAAwD;QAEzCD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAoD,SAAA,kBAAiD;QACjDpD,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACtFH,EAAA,CAAAuD,UAAA,MAAAsB,8CAAA,oBAIC;QACH7E,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAGAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC9BH,EAAA,CAAAC,cAAA,uBAA+C;QAE3CD,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAAgC;QAC9BD,EAAA,CAAAE,MAAA,kBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAA+B;QAC7BD,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAA+B;QAC7BD,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAAkC;QAChCD,EAAA,CAAAE,MAAA,oBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAAiC;QAC/BD,EAAA,CAAAE,MAAA,mBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,uBAA8B;QAC5BD,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAEfH,EAAA,CAAAC,cAAA,qBAAmE;QAAAD,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAClFH,EAAA,CAAAuD,UAAA,MAAAuB,8CAAA,oBAIC;QACH9E,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,gBAAiB;QAEwDD,EAAA,CAAAE,MAAA,uCAGrE;QAAAF,EAAA,CAAAG,YAAA,EAAe;QAGnBH,EAAA,CAAAC,cAAA,gBAAiB;QAGKD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACjCH,EAAA,CAAAC,cAAA,mBAAqD;QAAAD,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;;;;;;;;;;;QArbhEH,EAAA,CAAA+E,SAAA,GAAyB;QAAzB/E,EAAA,CAAAgF,UAAA,0BAAyB,UAAAhF,EAAA,CAAAiF,eAAA,KAAAC,GAAA;QAUHlF,EAAA,CAAA+E,SAAA,GAAuB;QAAvB/E,EAAA,CAAAgF,UAAA,cAAA7B,GAAA,CAAAtC,QAAA,CAAuB;QAO/Cb,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,MAAAhC,GAAA,CAAAtC,QAAA,mBAAAuE,OAAA,GAAAjC,GAAA,CAAAtC,QAAA,CAAAwE,GAAA,4BAAAD,OAAA,CAAAE,QAAA,wBAIC;QAe0CtF,EAAA,CAAA+E,SAAA,IAAmC;QAAnC/E,EAAA,CAAAgF,UAAA,SAAA7B,GAAA,CAAA5C,IAAA,uBAAmC;QAE5EP,EAAA,CAAA+E,SAAA,GAA0C;QAA1C/E,EAAA,CAAAuF,kBAAA,MAAApC,GAAA,CAAA5C,IAAA,uCAA0C;QAC5CP,EAAA,CAAA+E,SAAA,EAIC;QAJD/E,EAAA,CAAAmF,aAAA,MAAAhC,GAAA,CAAAtC,QAAA,mBAAA2E,OAAA,GAAArC,GAAA,CAAAtC,QAAA,CAAAwE,GAAA,+BAAAG,OAAA,CAAAF,QAAA,wBAIC;QAUDtF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,MAAAhC,GAAA,CAAAtC,QAAA,mBAAA4E,OAAA,GAAAtC,GAAA,CAAAtC,QAAA,CAAAwE,GAAA,4BAAAI,OAAA,CAAAH,QAAA,kBAAAnC,GAAA,CAAAtC,QAAA,mBAAA4E,OAAA,GAAAtC,GAAA,CAAAtC,QAAA,CAAAwE,GAAA,4BAAAI,OAAA,CAAAC,OAAA,YAIC;QAkBD1F,EAAA,CAAA+E,SAAA,IAIC;QAJD/E,EAAA,CAAAmF,aAAA,MAAAhC,GAAA,CAAAtC,QAAA,mBAAA8E,OAAA,GAAAxC,GAAA,CAAAtC,QAAA,CAAAwE,GAAA,2BAAAM,OAAA,CAAAL,QAAA,wBAIC;QAQDtF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,MAAAhC,GAAA,CAAAtC,QAAA,mBAAA+E,QAAA,GAAAzC,GAAA,CAAAtC,QAAA,CAAAwE,GAAA,4BAAAO,QAAA,CAAAN,QAAA,wBAIC;QASatF,EAAA,CAAA+E,SAAA,GAAiB;QAAjB/E,EAAA,CAAAgF,UAAA,kBAAiB;QAGjBhF,EAAA,CAAA+E,SAAA,GAAmB;QAAnB/E,EAAA,CAAAgF,UAAA,oBAAmB;QAGnBhF,EAAA,CAAA+E,SAAA,GAAkB;QAAlB/E,EAAA,CAAAgF,UAAA,mBAAkB;QAGlBhF,EAAA,CAAA+E,SAAA,GAAkB;QAAlB/E,EAAA,CAAAgF,UAAA,mBAAkB;QAGlBhF,EAAA,CAAA+E,SAAA,GAAqB;QAArB/E,EAAA,CAAAgF,UAAA,sBAAqB;QAGrBhF,EAAA,CAAA+E,SAAA,GAAoB;QAApB/E,EAAA,CAAAgF,UAAA,qBAAoB;QAGpBhF,EAAA,CAAA+E,SAAA,GAAiB;QAAjB/E,EAAA,CAAAgF,UAAA,kBAAiB;QAK/BhF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,MAAAhC,GAAA,CAAAtC,QAAA,mBAAAgF,QAAA,GAAA1C,GAAA,CAAAtC,QAAA,CAAAwE,GAAA,8BAAAQ,QAAA,CAAAP,QAAA,wBAIC;QAcuBtF,EAAA,CAAA+E,SAAA,GAA6B;QAA7B/E,EAAA,CAAAgF,UAAA,eAAA7B,GAAA,CAAAtC,QAAA,kBAAAsC,GAAA,CAAAtC,QAAA,CAAAiF,KAAA,EAA6B;QAe3C9F,EAAA,CAAA+E,SAAA,IAAyB;QAAzB/E,EAAA,CAAAgF,UAAA,cAAA7B,GAAA,CAAAvB,UAAA,CAAyB;QAOnC5B,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAvB,UAAA,mBAAAmE,QAAA,GAAA5C,GAAA,CAAAvB,UAAA,CAAAyD,GAAA,4BAAAU,QAAA,CAAAT,QAAA,yBAIC;QAe0CtF,EAAA,CAAA+E,SAAA,IAAoC;QAApC/E,EAAA,CAAAgF,UAAA,SAAA7B,GAAA,CAAA3C,KAAA,uBAAoC;QAE7ER,EAAA,CAAA+E,SAAA,GAA2C;QAA3C/E,EAAA,CAAAuF,kBAAA,MAAApC,GAAA,CAAA3C,KAAA,uCAA2C;QAC7CR,EAAA,CAAA+E,SAAA,EAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAvB,UAAA,mBAAAoE,QAAA,GAAA7C,GAAA,CAAAvB,UAAA,CAAAyD,GAAA,+BAAAW,QAAA,CAAAV,QAAA,yBAIC;QAUDtF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAvB,UAAA,mBAAAqE,QAAA,GAAA9C,GAAA,CAAAvB,UAAA,CAAAyD,GAAA,4BAAAY,QAAA,CAAAX,QAAA,eAAAnC,GAAA,CAAAvB,UAAA,mBAAAqE,QAAA,GAAA9C,GAAA,CAAAvB,UAAA,CAAAyD,GAAA,4BAAAY,QAAA,CAAAP,OAAA,aAIC;QAkBD1F,EAAA,CAAA+E,SAAA,IAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAvB,UAAA,mBAAAsE,QAAA,GAAA/C,GAAA,CAAAvB,UAAA,CAAAyD,GAAA,2BAAAa,QAAA,CAAAZ,QAAA,yBAIC;QAQDtF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAvB,UAAA,mBAAAuE,QAAA,GAAAhD,GAAA,CAAAvB,UAAA,CAAAyD,GAAA,4BAAAc,QAAA,CAAAb,QAAA,yBAIC;QASatF,EAAA,CAAA+E,SAAA,GAAiB;QAAjB/E,EAAA,CAAAgF,UAAA,kBAAiB;QAGjBhF,EAAA,CAAA+E,SAAA,GAAmB;QAAnB/E,EAAA,CAAAgF,UAAA,oBAAmB;QAGnBhF,EAAA,CAAA+E,SAAA,GAAkB;QAAlB/E,EAAA,CAAAgF,UAAA,mBAAkB;QAGlBhF,EAAA,CAAA+E,SAAA,GAAkB;QAAlB/E,EAAA,CAAAgF,UAAA,mBAAkB;QAGlBhF,EAAA,CAAA+E,SAAA,GAAqB;QAArB/E,EAAA,CAAAgF,UAAA,sBAAqB;QAGrBhF,EAAA,CAAA+E,SAAA,GAAoB;QAApB/E,EAAA,CAAAgF,UAAA,qBAAoB;QAGpBhF,EAAA,CAAA+E,SAAA,GAAiB;QAAjB/E,EAAA,CAAAgF,UAAA,kBAAiB;QAK/BhF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAvB,UAAA,mBAAAwE,QAAA,GAAAjD,GAAA,CAAAvB,UAAA,CAAAyD,GAAA,8BAAAe,QAAA,CAAAd,QAAA,yBAIC;QAcuBtF,EAAA,CAAA+E,SAAA,GAA+B;QAA/B/E,EAAA,CAAAgF,UAAA,eAAA7B,GAAA,CAAAvB,UAAA,kBAAAuB,GAAA,CAAAvB,UAAA,CAAAkE,KAAA,EAA+B;QAiB7C9F,EAAA,CAAA+E,SAAA,IAAwB;QAAxB/E,EAAA,CAAAgF,UAAA,cAAA7B,GAAA,CAAAtB,SAAA,CAAwB;QAOlC7B,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAtB,SAAA,mBAAAwE,QAAA,GAAAlD,GAAA,CAAAtB,SAAA,CAAAwD,GAAA,4BAAAgB,QAAA,CAAAf,QAAA,yBAIC;QAe0CtF,EAAA,CAAA+E,SAAA,IAAoC;QAApC/E,EAAA,CAAAgF,UAAA,SAAA7B,GAAA,CAAA1C,KAAA,uBAAoC;QAE7ET,EAAA,CAAA+E,SAAA,GAA2C;QAA3C/E,EAAA,CAAAuF,kBAAA,MAAApC,GAAA,CAAA1C,KAAA,uCAA2C;QAC7CT,EAAA,CAAA+E,SAAA,EAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAtB,SAAA,mBAAAyE,QAAA,GAAAnD,GAAA,CAAAtB,SAAA,CAAAwD,GAAA,+BAAAiB,QAAA,CAAAhB,QAAA,yBAIC;QAUDtF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAtB,SAAA,mBAAA0E,QAAA,GAAApD,GAAA,CAAAtB,SAAA,CAAAwD,GAAA,4BAAAkB,QAAA,CAAAjB,QAAA,eAAAnC,GAAA,CAAAtB,SAAA,mBAAA0E,QAAA,GAAApD,GAAA,CAAAtB,SAAA,CAAAwD,GAAA,4BAAAkB,QAAA,CAAAb,OAAA,aAIC;QAkBD1F,EAAA,CAAA+E,SAAA,IAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAtB,SAAA,mBAAA2E,QAAA,GAAArD,GAAA,CAAAtB,SAAA,CAAAwD,GAAA,2BAAAmB,QAAA,CAAAlB,QAAA,yBAIC;QAQDtF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAtB,SAAA,mBAAA4E,QAAA,GAAAtD,GAAA,CAAAtB,SAAA,CAAAwD,GAAA,4BAAAoB,QAAA,CAAAnB,QAAA,yBAIC;QASatF,EAAA,CAAA+E,SAAA,GAAiB;QAAjB/E,EAAA,CAAAgF,UAAA,kBAAiB;QAGjBhF,EAAA,CAAA+E,SAAA,GAAmB;QAAnB/E,EAAA,CAAAgF,UAAA,oBAAmB;QAGnBhF,EAAA,CAAA+E,SAAA,GAAkB;QAAlB/E,EAAA,CAAAgF,UAAA,mBAAkB;QAGlBhF,EAAA,CAAA+E,SAAA,GAAkB;QAAlB/E,EAAA,CAAAgF,UAAA,mBAAkB;QAGlBhF,EAAA,CAAA+E,SAAA,GAAqB;QAArB/E,EAAA,CAAAgF,UAAA,sBAAqB;QAGrBhF,EAAA,CAAA+E,SAAA,GAAoB;QAApB/E,EAAA,CAAAgF,UAAA,qBAAoB;QAGpBhF,EAAA,CAAA+E,SAAA,GAAiB;QAAjB/E,EAAA,CAAAgF,UAAA,kBAAiB;QAK/BhF,EAAA,CAAA+E,SAAA,GAIC;QAJD/E,EAAA,CAAAmF,aAAA,OAAAhC,GAAA,CAAAtB,SAAA,mBAAA6E,QAAA,GAAAvD,GAAA,CAAAtB,SAAA,CAAAwD,GAAA,8BAAAqB,QAAA,CAAApB,QAAA,yBAIC;QAcuBtF,EAAA,CAAA+E,SAAA,GAA8B;QAA9B/E,EAAA,CAAAgF,UAAA,eAAA7B,GAAA,CAAAtB,SAAA,kBAAAsB,GAAA,CAAAtB,SAAA,CAAAiE,KAAA,EAA8B;;;mBDjatE/F,mBAAmB,EACnBT,WAAW,EAAAgD,EAAA,CAAAqE,aAAA,EAAArE,EAAA,CAAAsE,oBAAA,EAAAtE,EAAA,CAAAuE,eAAA,EAAAvE,EAAA,CAAAwE,oBAAA,EAAAxE,EAAA,CAAAyE,iBAAA,EACXxH,mBAAmB,EAAA+C,EAAA,CAAA0E,kBAAA,EAAA1E,EAAA,CAAA2E,eAAA,EACnBnH,kBAAkB,EAAAoH,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBzH,cAAc,EAAA0H,EAAA,CAAAC,QAAA,EACd5H,aAAa,EAAA6H,EAAA,CAAAC,OAAA,EACb/H,eAAe,EAAAgI,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACfpI,eAAe,EACfD,iBAAiB,EAAAsI,EAAA,CAAAC,WAAA,EACjBxI,eAAe,EAAAyI,EAAA,CAAAC,SAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}