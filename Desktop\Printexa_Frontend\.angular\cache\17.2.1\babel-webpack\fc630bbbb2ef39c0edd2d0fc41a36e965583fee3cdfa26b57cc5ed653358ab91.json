{"ast": null, "code": "import interval from \"./interval.js\";\nimport { durationMinute, durationSecond } from \"./duration.js\";\nvar minute = interval(function (date) {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, function (date, step) {\n  date.setTime(+date + step * durationMinute);\n}, function (start, end) {\n  return (end - start) / durationMinute;\n}, function (date) {\n  return date.getMinutes();\n});\nexport default minute;\nexport var minutes = minute.range;", "map": {"version": 3, "names": ["interval", "durationMinute", "durationSecond", "minute", "date", "setTime", "getMilliseconds", "getSeconds", "step", "start", "end", "getMinutes", "minutes", "range"], "sources": ["C:/Users/<USER>/Desktop/mian/node_modules/d3-time-format/node_modules/d3-time/src/minute.js"], "sourcesContent": ["import interval from \"./interval.js\";\nimport {durationMinute, durationSecond} from \"./duration.js\";\n\nvar minute = interval(function(date) {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, function(date, step) {\n  date.setTime(+date + step * durationMinute);\n}, function(start, end) {\n  return (end - start) / durationMinute;\n}, function(date) {\n  return date.getMinutes();\n});\n\nexport default minute;\nexport var minutes = minute.range;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,SAAQC,cAAc,EAAEC,cAAc,QAAO,eAAe;AAE5D,IAAIC,MAAM,GAAGH,QAAQ,CAAC,UAASI,IAAI,EAAE;EACnCA,IAAI,CAACC,OAAO,CAACD,IAAI,GAAGA,IAAI,CAACE,eAAe,CAAC,CAAC,GAAGF,IAAI,CAACG,UAAU,CAAC,CAAC,GAAGL,cAAc,CAAC;AAClF,CAAC,EAAE,UAASE,IAAI,EAAEI,IAAI,EAAE;EACtBJ,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,GAAGI,IAAI,GAAGP,cAAc,CAAC;AAC7C,CAAC,EAAE,UAASQ,KAAK,EAAEC,GAAG,EAAE;EACtB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIR,cAAc;AACvC,CAAC,EAAE,UAASG,IAAI,EAAE;EAChB,OAAOA,IAAI,CAACO,UAAU,CAAC,CAAC;AAC1B,CAAC,CAAC;AAEF,eAAeR,MAAM;AACrB,OAAO,IAAIS,OAAO,GAAGT,MAAM,CAACU,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}