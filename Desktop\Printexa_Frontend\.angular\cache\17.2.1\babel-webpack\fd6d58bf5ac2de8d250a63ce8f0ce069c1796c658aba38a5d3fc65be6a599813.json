{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class LeaveBalanceService extends UnsubscribeOnDestroyAdapter {\n  constructor(httpClient) {\n    super();\n    this.httpClient = httpClient;\n    this.API_URL = 'assets/data/leave-balance.json';\n    this.isTblLoading = true;\n    this.dataChange = new BehaviorSubject([]);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  /** CRUD METHODS */\n  getAllLeavess() {\n    this.subs.sink = this.httpClient.get(this.API_URL).subscribe({\n      next: data => {\n        this.isTblLoading = false;\n        this.dataChange.next(data);\n      },\n      error: error => {\n        this.isTblLoading = false;\n        console.log(error.name + ' ' + error.message);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function LeaveBalanceService_Factory(t) {\n    return new (t || LeaveBalanceService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LeaveBalanceService,\n    factory: LeaveBalanceService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "UnsubscribeOnDestroyAdapter", "LeaveBalanceService", "constructor", "httpClient", "API_URL", "isTblLoading", "dataChange", "data", "value", "getDialogData", "dialogData", "getAllLeavess", "subs", "sink", "get", "subscribe", "next", "error", "console", "log", "name", "message", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\leaves\\leave-balance\\leave-balance.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { LeaveBalance } from './leave-balance.model';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class LeaveBalanceService extends UnsubscribeOnDestroyAdapter {\r\n  private readonly API_URL = 'assets/data/leave-balance.json';\r\n  isTblLoading = true;\r\n  dataChange: BehaviorSubject<LeaveBalance[]> = new BehaviorSubject<\r\n    LeaveBalance[]\r\n  >([]);\r\n  // Temporarily stores data from dialogs\r\n  dialogData!: LeaveBalance;\r\n  constructor(private httpClient: HttpClient) {\r\n    super();\r\n  }\r\n  get data(): LeaveBalance[] {\r\n    return this.dataChange.value;\r\n  }\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n  /** CRUD METHODS */\r\n  getAllLeavess(): void {\r\n    this.subs.sink = this.httpClient\r\n      .get<LeaveBalance[]>(this.API_URL)\r\n      .subscribe({\r\n        next: (data) => {\r\n          this.isTblLoading = false;\r\n          this.dataChange.next(data);\r\n        },\r\n        error: (error: HttpErrorResponse) => {\r\n          this.isTblLoading = false;\r\n          console.log(error.name + ' ' + error.message);\r\n        },\r\n      });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;AAGtC,SAASC,2BAA2B,QAAQ,SAAS;;;AAKrD,OAAM,MAAOC,mBAAoB,SAAQD,2BAA2B;EAQlEE,YAAoBC,UAAsB;IACxC,KAAK,EAAE;IADW,KAAAA,UAAU,GAAVA,UAAU;IAPb,KAAAC,OAAO,GAAG,gCAAgC;IAC3D,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,UAAU,GAAoC,IAAIP,eAAe,CAE/D,EAAE,CAAC;EAKL;EACA,IAAIQ,IAAIA,CAAA;IACN,OAAO,IAAI,CAACD,UAAU,CAACE,KAAK;EAC9B;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EACA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACV,UAAU,CAC7BW,GAAG,CAAiB,IAAI,CAACV,OAAO,CAAC,CACjCW,SAAS,CAAC;MACTC,IAAI,EAAGT,IAAI,IAAI;QACb,IAAI,CAACF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,UAAU,CAACU,IAAI,CAACT,IAAI,CAAC;MAC5B,CAAC;MACDU,KAAK,EAAGA,KAAwB,IAAI;QAClC,IAAI,CAACZ,YAAY,GAAG,KAAK;QACzBa,OAAO,CAACC,GAAG,CAACF,KAAK,CAACG,IAAI,GAAG,GAAG,GAAGH,KAAK,CAACI,OAAO,CAAC;MAC/C;KACD,CAAC;EACN;EAAC,QAAAC,CAAA,G;qBA/BUrB,mBAAmB,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAnB1B,mBAAmB;IAAA2B,OAAA,EAAnB3B,mBAAmB,CAAA4B,IAAA;IAAAC,UAAA,EAFlB;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}