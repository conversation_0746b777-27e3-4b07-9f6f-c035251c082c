{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError, tap, catchError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AdminService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/admin/';\n    this.currentAdminSubject = new BehaviorSubject(this.getAdminFromStorage());\n    this.currentAdmin$ = this.currentAdminSubject.asObservable();\n  }\n  getAdminFromStorage() {\n    const adminData = localStorage.getItem('currentAdmin');\n    return adminData ? JSON.parse(adminData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  register(adminData) {\n    const registrationDto = {\n      Username: adminData.username,\n      Email: adminData.email,\n      Password: adminData.password,\n      FullName: adminData.fullName || ''\n    };\n    return this.http.post(`${this.baseUrl}register`, registrationDto, {\n      headers: this.getHeaders()\n    }).pipe(tap(response => {\n      if (response.success) {\n        localStorage.setItem('auth_token', response.token);\n        this.getAdminById(response.adminId).subscribe();\n      }\n    }), catchError(this.handleError));\n  }\n  login(credentials) {\n    const loginDto = {\n      Username: credentials.username,\n      Password: credentials.password\n    };\n    return this.http.post(`${this.baseUrl}login`, loginDto, {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    }).pipe(tap(response => {\n      if (response.success) {\n        localStorage.setItem('auth_token', response.token);\n        this.currentAdminSubject.next({\n          Id: response.adminId,\n          Username: response.username,\n          Email: response.email,\n          Role: response.Role\n          // Ajoutez les autres propriétés nécessaires\n        });\n      }\n    }), catchError(error => {\n      console.error('Login error:', error);\n      return throwError(() => new Error(error.error?.error || 'Échec de l\\'authentification'));\n    }));\n  }\n  getAllAdmins() {\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  getAdminById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(admin => {\n      this.currentAdminSubject.next(admin);\n      localStorage.setItem('currentAdmin', JSON.stringify(admin));\n    }), catchError(this.handleError));\n  }\n  updateAdmin(id, adminData) {\n    return this.http.put(`${this.baseUrl}${id}`, adminData, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.getAdminById(id).subscribe(); // Refresh admin data\n    }), catchError(this.handleError));\n  }\n  deleteAdmin(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    console.error('AdminService error:', error);\n    let errorMessage = 'Une erreur est survenue';\n    if (error.status === 400) {\n      // Handle validation errors\n      if (error.error && typeof error.error === 'object') {\n        const errors = [];\n        if (error.error.errors) {\n          for (const key in error.error.errors) {\n            errors.push(...error.error.errors[key]);\n          }\n        }\n        errorMessage = errors.join('\\n') || 'Données invalides';\n      } else {\n        errorMessage = error.error?.message || 'Requête incorrecte';\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Non autorisé';\n      this.logout();\n    } else if (error.status === 409) {\n      errorMessage = error.error || 'Conflit (données déjà existantes)';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  logout() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('currentAdmin');\n    this.currentAdminSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n  isAuthenticated() {\n    return !!localStorage.getItem('auth_token');\n  }\n  static #_ = this.ɵfac = function AdminService_Factory(t) {\n    return new (t || AdminService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AdminService,\n    factory: AdminService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "tap", "catchError", "AdminService", "constructor", "http", "router", "baseUrl", "currentAdminSubject", "getAdminFromStorage", "currentAdmin$", "asObservable", "adminData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "register", "registrationDto", "Username", "username", "Email", "email", "Password", "password", "FullName", "fullName", "post", "headers", "pipe", "response", "success", "setItem", "getAdminById", "adminId", "subscribe", "handleError", "login", "credentials", "loginDto", "next", "Id", "Role", "error", "console", "Error", "getAllAdmins", "get", "id", "admin", "stringify", "updateAdmin", "put", "deleteAdmin", "delete", "errorMessage", "status", "errors", "key", "push", "join", "message", "logout", "removeItem", "navigate", "isAuthenticated", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\admin.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError } from 'rxjs';\r\nimport { Admin, AuthResult } from '../Model/Admin';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AdminService {\r\n  private baseUrl: string = 'https://localhost:5001/api/admin/';\r\n  private currentAdminSubject: BehaviorSubject<Admin | null>;\r\n  public currentAdmin$: Observable<Admin | null>;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentAdminSubject = new BehaviorSubject<Admin | null>(this.getAdminFromStorage());\r\n    this.currentAdmin$ = this.currentAdminSubject.asObservable();\r\n  }\r\n\r\n  private getAdminFromStorage(): Admin | null {\r\n    const adminData = localStorage.getItem('currentAdmin');\r\n    return adminData ? JSON.parse(adminData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  register(adminData: {\r\n    username: string;\r\n    email: string;\r\n    password: string;\r\n    fullName?: string;\r\n  }): Observable<AuthResult> {\r\n    const registrationDto = {\r\n      Username: adminData.username,\r\n      Email: adminData.email,\r\n      Password: adminData.password,\r\n      FullName: adminData.fullName || ''\r\n    };\r\n\r\n    return this.http.post<AuthResult>(\r\n      `${this.baseUrl}register`,\r\n      registrationDto,\r\n      { headers: this.getHeaders() }\r\n    ).pipe(\r\n      tap(response => {\r\n        if (response.success) {\r\n          localStorage.setItem('auth_token', response.token);\r\n          this.getAdminById(response.adminId).subscribe();\r\n        }\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\nlogin(credentials: { username: string; password: string }): Observable<AuthResult> {\r\n  const loginDto = {\r\n    Username: credentials.username,\r\n    Password: credentials.password\r\n  };\r\n\r\n  return this.http.post<AuthResult>(\r\n    `${this.baseUrl}login`,\r\n    loginDto,\r\n    { \r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json'\r\n      })\r\n    }\r\n  ).pipe(\r\n    tap(response => {\r\n      if (response.success) {\r\n        localStorage.setItem('auth_token', response.token);\r\n        this.currentAdminSubject.next({\r\n          Id: response.adminId,\r\n          Username: response.username,\r\n          Email: response.email,\r\n          Role: response.Role\r\n          // Ajoutez les autres propriétés nécessaires\r\n        });\r\n      }\r\n    }),\r\n    catchError(error => {\r\n      console.error('Login error:', error);\r\n      return throwError(() => new Error(\r\n        error.error?.error || 'Échec de l\\'authentification'\r\n      ));\r\n    })\r\n  );\r\n}\r\n  getAllAdmins(): Observable<Admin[]> {\r\n    return this.http.get<Admin[]>(\r\n      this.baseUrl,\r\n      { headers: this.getHeaders() }\r\n    ).pipe(\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  getAdminById(id: number): Observable<Admin> {\r\n    return this.http.get<Admin>(\r\n      `${this.baseUrl}${id}`,\r\n      { headers: this.getHeaders() }\r\n    ).pipe(\r\n      tap(admin => {\r\n        this.currentAdminSubject.next(admin);\r\n        localStorage.setItem('currentAdmin', JSON.stringify(admin));\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  updateAdmin(id: number, adminData: Partial<Admin>): Observable<void> {\r\n    return this.http.put<void>(\r\n      `${this.baseUrl}${id}`,\r\n      adminData,\r\n      { headers: this.getHeaders() }\r\n    ).pipe(\r\n      tap(() => {\r\n        this.getAdminById(id).subscribe(); // Refresh admin data\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  deleteAdmin(id: number): Observable<void> {\r\n    return this.http.delete<void>(\r\n      `${this.baseUrl}${id}`,\r\n      { headers: this.getHeaders() }\r\n    ).pipe(\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    console.error('AdminService error:', error);\r\n    \r\n    let errorMessage = 'Une erreur est survenue';\r\n    \r\n    if (error.status === 400) {\r\n      // Handle validation errors\r\n      if (error.error && typeof error.error === 'object') {\r\n        const errors = [];\r\n        if (error.error.errors) {\r\n          for (const key in error.error.errors) {\r\n            errors.push(...error.error.errors[key]);\r\n          }\r\n        }\r\n        errorMessage = errors.join('\\n') || 'Données invalides';\r\n      } else {\r\n        errorMessage = error.error?.message || 'Requête incorrecte';\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Non autorisé';\r\n      this.logout();\r\n    } else if (error.status === 409) {\r\n      errorMessage = error.error || 'Conflit (données déjà existantes)';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  logout() {\r\n    localStorage.removeItem('auth_token');\r\n    localStorage.removeItem('currentAdmin');\r\n    this.currentAdminSubject.next(null);\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    return !!localStorage.getItem('auth_token');\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAcC,UAAU,EAAEC,GAAG,EAAEC,UAAU,QAAQ,MAAM;;;;AAM/E,OAAM,MAAOC,YAAY;EAKvBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAJ5C,KAAAC,OAAO,GAAW,mCAAmC;IAK3D,IAAI,CAACC,mBAAmB,GAAG,IAAIT,eAAe,CAAe,IAAI,CAACU,mBAAmB,EAAE,CAAC;IACxF,IAAI,CAACC,aAAa,GAAG,IAAI,CAACF,mBAAmB,CAACG,YAAY,EAAE;EAC9D;EAEQF,mBAAmBA,CAAA;IACzB,MAAMG,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACtD,OAAOF,SAAS,GAAGG,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC,GAAG,IAAI;EACjD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIhB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIoB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEAC,QAAQA,CAACP,SAKR;IACC,MAAMQ,eAAe,GAAG;MACtBC,QAAQ,EAAET,SAAS,CAACU,QAAQ;MAC5BC,KAAK,EAAEX,SAAS,CAACY,KAAK;MACtBC,QAAQ,EAAEb,SAAS,CAACc,QAAQ;MAC5BC,QAAQ,EAAEf,SAAS,CAACgB,QAAQ,IAAI;KACjC;IAED,OAAO,IAAI,CAACvB,IAAI,CAACwB,IAAI,CACnB,GAAG,IAAI,CAACtB,OAAO,UAAU,EACzBa,eAAe,EACf;MAAEU,OAAO,EAAE,IAAI,CAACb,UAAU;IAAE,CAAE,CAC/B,CAACc,IAAI,CACJ9B,GAAG,CAAC+B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBpB,YAAY,CAACqB,OAAO,CAAC,YAAY,EAAEF,QAAQ,CAACd,KAAK,CAAC;QAClD,IAAI,CAACiB,YAAY,CAACH,QAAQ,CAACI,OAAO,CAAC,CAACC,SAAS,EAAE;;IAEnD,CAAC,CAAC,EACFnC,UAAU,CAAC,IAAI,CAACoC,WAAW,CAAC,CAC7B;EACH;EAEFC,KAAKA,CAACC,WAAmD;IACvD,MAAMC,QAAQ,GAAG;MACfpB,QAAQ,EAAEmB,WAAW,CAAClB,QAAQ;MAC9BG,QAAQ,EAAEe,WAAW,CAACd;KACvB;IAED,OAAO,IAAI,CAACrB,IAAI,CAACwB,IAAI,CACnB,GAAG,IAAI,CAACtB,OAAO,OAAO,EACtBkC,QAAQ,EACR;MACEX,OAAO,EAAE,IAAIhC,WAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF,CACF,CAACiC,IAAI,CACJ9B,GAAG,CAAC+B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBpB,YAAY,CAACqB,OAAO,CAAC,YAAY,EAAEF,QAAQ,CAACd,KAAK,CAAC;QAClD,IAAI,CAACV,mBAAmB,CAACkC,IAAI,CAAC;UAC5BC,EAAE,EAAEX,QAAQ,CAACI,OAAO;UACpBf,QAAQ,EAAEW,QAAQ,CAACV,QAAQ;UAC3BC,KAAK,EAAES,QAAQ,CAACR,KAAK;UACrBoB,IAAI,EAAEZ,QAAQ,CAACY;UACf;SACD,CAAC;;IAEN,CAAC,CAAC,EACF1C,UAAU,CAAC2C,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO7C,UAAU,CAAC,MAAM,IAAI+C,KAAK,CAC/BF,KAAK,CAACA,KAAK,EAAEA,KAAK,IAAI,8BAA8B,CACrD,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EACEG,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC3C,IAAI,CAAC4C,GAAG,CAClB,IAAI,CAAC1C,OAAO,EACZ;MAAEuB,OAAO,EAAE,IAAI,CAACb,UAAU;IAAE,CAAE,CAC/B,CAACc,IAAI,CACJ7B,UAAU,CAAC,IAAI,CAACoC,WAAW,CAAC,CAC7B;EACH;EAEAH,YAAYA,CAACe,EAAU;IACrB,OAAO,IAAI,CAAC7C,IAAI,CAAC4C,GAAG,CAClB,GAAG,IAAI,CAAC1C,OAAO,GAAG2C,EAAE,EAAE,EACtB;MAAEpB,OAAO,EAAE,IAAI,CAACb,UAAU;IAAE,CAAE,CAC/B,CAACc,IAAI,CACJ9B,GAAG,CAACkD,KAAK,IAAG;MACV,IAAI,CAAC3C,mBAAmB,CAACkC,IAAI,CAACS,KAAK,CAAC;MACpCtC,YAAY,CAACqB,OAAO,CAAC,cAAc,EAAEnB,IAAI,CAACqC,SAAS,CAACD,KAAK,CAAC,CAAC;IAC7D,CAAC,CAAC,EACFjD,UAAU,CAAC,IAAI,CAACoC,WAAW,CAAC,CAC7B;EACH;EAEAe,WAAWA,CAACH,EAAU,EAAEtC,SAAyB;IAC/C,OAAO,IAAI,CAACP,IAAI,CAACiD,GAAG,CAClB,GAAG,IAAI,CAAC/C,OAAO,GAAG2C,EAAE,EAAE,EACtBtC,SAAS,EACT;MAAEkB,OAAO,EAAE,IAAI,CAACb,UAAU;IAAE,CAAE,CAC/B,CAACc,IAAI,CACJ9B,GAAG,CAAC,MAAK;MACP,IAAI,CAACkC,YAAY,CAACe,EAAE,CAAC,CAACb,SAAS,EAAE,CAAC,CAAC;IACrC,CAAC,CAAC,EACFnC,UAAU,CAAC,IAAI,CAACoC,WAAW,CAAC,CAC7B;EACH;EAEAiB,WAAWA,CAACL,EAAU;IACpB,OAAO,IAAI,CAAC7C,IAAI,CAACmD,MAAM,CACrB,GAAG,IAAI,CAACjD,OAAO,GAAG2C,EAAE,EAAE,EACtB;MAAEpB,OAAO,EAAE,IAAI,CAACb,UAAU;IAAE,CAAE,CAC/B,CAACc,IAAI,CACJ7B,UAAU,CAAC,IAAI,CAACoC,WAAW,CAAC,CAC7B;EACH;EAEQA,WAAWA,CAACO,KAAwB;IAC1CC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAE3C,IAAIY,YAAY,GAAG,yBAAyB;IAE5C,IAAIZ,KAAK,CAACa,MAAM,KAAK,GAAG,EAAE;MACxB;MACA,IAAIb,KAAK,CAACA,KAAK,IAAI,OAAOA,KAAK,CAACA,KAAK,KAAK,QAAQ,EAAE;QAClD,MAAMc,MAAM,GAAG,EAAE;QACjB,IAAId,KAAK,CAACA,KAAK,CAACc,MAAM,EAAE;UACtB,KAAK,MAAMC,GAAG,IAAIf,KAAK,CAACA,KAAK,CAACc,MAAM,EAAE;YACpCA,MAAM,CAACE,IAAI,CAAC,GAAGhB,KAAK,CAACA,KAAK,CAACc,MAAM,CAACC,GAAG,CAAC,CAAC;;;QAG3CH,YAAY,GAAGE,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,mBAAmB;OACxD,MAAM;QACLL,YAAY,GAAGZ,KAAK,CAACA,KAAK,EAAEkB,OAAO,IAAI,oBAAoB;;KAE9D,MAAM,IAAIlB,KAAK,CAACa,MAAM,KAAK,GAAG,EAAE;MAC/BD,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACO,MAAM,EAAE;KACd,MAAM,IAAInB,KAAK,CAACa,MAAM,KAAK,GAAG,EAAE;MAC/BD,YAAY,GAAGZ,KAAK,CAACA,KAAK,IAAI,mCAAmC;;IAGnE,OAAO7C,UAAU,CAAC,MAAM,IAAI+C,KAAK,CAACU,YAAY,CAAC,CAAC;EAClD;EAEAO,MAAMA,CAAA;IACJnD,YAAY,CAACoD,UAAU,CAAC,YAAY,CAAC;IACrCpD,YAAY,CAACoD,UAAU,CAAC,cAAc,CAAC;IACvC,IAAI,CAACzD,mBAAmB,CAACkC,IAAI,CAAC,IAAI,CAAC;IACnC,IAAI,CAACpC,MAAM,CAAC4D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,eAAeA,CAAA;IACb,OAAO,CAAC,CAACtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAC7C;EAAC,QAAAsD,CAAA,G;qBAvKUjE,YAAY,EAAAkE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAZxE,YAAY;IAAAyE,OAAA,EAAZzE,YAAY,CAAA0E,IAAA;IAAAC,UAAA,EAFX;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}