{"ast": null, "code": "function propertyRemove(name) {\n  return function () {\n    delete this[name];\n  };\n}\nfunction propertyConstant(name, value) {\n  return function () {\n    this[name] = value;\n  };\n}\nfunction propertyFunction(name, value) {\n  return function () {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];else this[name] = v;\n  };\n}\nexport default function (name, value) {\n  return arguments.length > 1 ? this.each((value == null ? propertyRemove : typeof value === \"function\" ? propertyFunction : propertyConstant)(name, value)) : this.node()[name];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}