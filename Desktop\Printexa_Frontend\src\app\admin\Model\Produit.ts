export interface Produit {
  id: string;
  type: string;
  description: string;
  prixUnitaireHT: number;
  prixUnitaireTTC: number;
  tva: number;
  codeProd?: string;
}

export class ProduitModel {
  id: string;
  type: string;
  description: string;
  prixUnitaireHT: number;
  prixUnitaireTTC: number;
  tva: number;
  codeProd?: string;

  constructor(produit: Partial<Produit>) {
    this.id = produit.id || this.getRandomID();
    this.type = produit.type || '';
    this.description = produit.description || '';
    this.prixUnitaireHT = produit.prixUnitaireHT || 0;
    this.prixUnitaireTTC = produit.prixUnitaireTTC || 0;
    this.tva = produit.tva || 20;
    this.codeProd = produit.codeProd || '';
  }

  public getRandomID(): string {
    const S4 = () => {
      return ((1 + Math.random()) * 0x10000) | 0;
    };
    return (S4() + S4()).toString();
  }
}
// DTOs correspondants
export interface ProduitDTO {
  id: string;
  type: string;
  description: string;
  prixUnitaireHT: number;
  prixUnitaireTTC: number;
  tva: number;
  codeProd?: string;
}

export interface CreateProduitDTO {
  type: string;
  description: string;
  prixUnitaireHT: number;
  tva: number;
  codeProd?: string;
}

export interface UpdateProduitDTO {
  type?: string;
  description?: string;
  prixUnitaireHT?: number;
  tva?: number;
  codeProd?: string;
}