{"ast": null, "code": "import { AddClientComponent } from \"./add-client/add-client.component\";\nimport { EditClientComponent } from \"./edit-client/edit-client.component\";\nimport { Page404Component } from \"../../authentication/page404/page404.component\";\nimport { AllclientComponent } from \"./all-clients/all-clients.component\";\nexport const ADMIN_CLIENT_ROUTE = [{\n  path: \"all-clients\",\n  component: AllclientComponent\n}, {\n  path: \"add-client\",\n  component: AddClientComponent\n}, {\n  path: \"edit-client\",\n  component: EditClientComponent\n}, {\n  path: \"**\",\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["AddClientComponent", "EditClientComponent", "Page404Component", "AllclientComponent", "ADMIN_CLIENT_ROUTE", "path", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\clients\\clients.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { AddClientComponent } from \"./add-client/add-client.component\";\r\nimport { EditClientComponent } from \"./edit-client/edit-client.component\";\r\nimport { Page404Component } from \"../../authentication/page404/page404.component\";\r\nimport { AllclientComponent } from \"./all-clients/all-clients.component\";\r\nexport const ADMIN_CLIENT_ROUTE: Route[] = [\r\n  {\r\n    path: \"all-clients\",\r\n    component: AllclientComponent,\r\n  },\r\n  {\r\n    path: \"add-client\",\r\n    component: AddClientComponent,\r\n  },\r\n  {\r\n    path: \"edit-client\",\r\n    component: EditClientComponent,\r\n  },\r\n  { path: \"**\", component: Page404Component },\r\n];\r\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,gBAAgB,QAAQ,gDAAgD;AACjF,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,OAAO,MAAMC,kBAAkB,GAAY,CACzC;EACEC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEL;CACZ,EACD;EAAEI,IAAI,EAAE,IAAI;EAAEC,SAAS,EAAEJ;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}