{"ast": null, "code": "import shuffleSelf from './_shuffleSelf.js';\nimport values from './values.js';\n\n/**\n * The base implementation of `_.shuffle`.\n *\n * @private\n * @param {Array|Object} collection The collection to shuffle.\n * @returns {Array} Returns the new shuffled array.\n */\nfunction baseShuffle(collection) {\n  return shuffleSelf(values(collection));\n}\nexport default baseShuffle;", "map": {"version": 3, "names": ["shuffleSelf", "values", "baseShuffle", "collection"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/lodash-es/_baseShuffle.js"], "sourcesContent": ["import shuffleSelf from './_shuffleSelf.js';\nimport values from './values.js';\n\n/**\n * The base implementation of `_.shuffle`.\n *\n * @private\n * @param {Array|Object} collection The collection to shuffle.\n * @returns {Array} Returns the new shuffled array.\n */\nfunction baseShuffle(collection) {\n  return shuffleSelf(values(collection));\n}\n\nexport default baseShuffle;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,EAAE;EAC/B,OAAOH,WAAW,CAACC,MAAM,CAACE,UAAU,CAAC,CAAC;AACxC;AAEA,eAAeD,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}