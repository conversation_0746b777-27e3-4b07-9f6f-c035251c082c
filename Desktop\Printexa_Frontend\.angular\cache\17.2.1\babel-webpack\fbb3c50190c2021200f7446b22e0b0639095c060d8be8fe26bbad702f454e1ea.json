{"ast": null, "code": "import { Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';\nimport { MatOptionModule } from '@angular/material/core';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/form-field\";\nimport * as i3 from \"@angular/material/input\";\nimport * as i4 from \"@angular/material/datepicker\";\nimport * as i5 from \"@angular/material/select\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/button\";\nfunction EditClientComponent_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditClientComponent_Conditional_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Company Name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditClientComponent_Conditional_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditClientComponent_Conditional_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please select date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditClientComponent_Conditional_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Select Any Billing Method \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = () => [\"Clients\"];\nexport class EditClientComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.formdata = {\n      name: 'Pooja Sarma',\n      mobile: '*********',\n      email: '<EMAIL>',\n      date: '1987-02-17T14:22:18Z',\n      company_name: 'ABC Infotech',\n      currency: 'rupee',\n      billing_method: 'Fixed Price',\n      uploadImg: ''\n    };\n    this.clientForm = this.createContactForm();\n  }\n  onSubmit() {\n    console.log('Form Value', this.clientForm.value);\n  }\n  createContactForm() {\n    return this.fb.group({\n      name: [this.formdata.name, [Validators.required]],\n      mobile: [this.formdata.mobile, [Validators.required]],\n      email: [this.formdata.email, [Validators.required, Validators.email, Validators.minLength(5)]],\n      date: [this.formdata.date, [Validators.required]],\n      company_name: [this.formdata.company_name],\n      currency: [this.formdata.currency],\n      billing_method: [this.formdata.billing_method],\n      uploadImg: [this.formdata.uploadImg]\n    });\n  }\n  static #_ = this.ɵfac = function EditClientComponent_Factory(t) {\n    return new (t || EditClientComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EditClientComponent,\n    selectors: [[\"app-edit-client\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 76,\n    vars: 16,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\", \"clearfix\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\"], [1, \"card\"], [1, \"header\"], [1, \"body\"], [1, \"m-4\", 3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\", \"mb-3\"], [\"appearance\", \"outline\", 1, \"example-full-width\", \"mb-3\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"company_name\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"mobile\", \"type\", \"number\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"date\", \"required\", \"\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"picker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"currency\", \"type\", \"text\", \"required\", \"\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mb-3\"], [\"formControlName\", \"billing_method\", \"required\", \"\"], [3, \"value\"], [\"formControlName\", \"uploadImg\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"btn-space\", 3, \"disabled\"], [\"type\", \"button\", \"mat-raised-button\", \"\", \"color\", \"warn\"]],\n    template: function EditClientComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Edit Client\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function EditClientComponent_Template_form_ngSubmit_11_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-form-field\", 12)(15, \"mat-label\");\n        i0.ɵɵtext(16, \"Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"input\", 13);\n        i0.ɵɵtemplate(18, EditClientComponent_Conditional_18_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 11)(20, \"mat-form-field\", 12)(21, \"mat-label\");\n        i0.ɵɵtext(22, \"Company Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(23, \"input\", 14);\n        i0.ɵɵtemplate(24, EditClientComponent_Conditional_24_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(25, \"div\", 10)(26, \"div\", 11)(27, \"mat-form-field\", 12)(28, \"mat-label\");\n        i0.ɵɵtext(29, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(30, \"input\", 15);\n        i0.ɵɵtemplate(31, EditClientComponent_Conditional_31_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 11)(33, \"mat-form-field\", 12)(34, \"mat-label\");\n        i0.ɵɵtext(35, \"Mobile\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(36, \"input\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(37, \"div\", 10)(38, \"div\", 11)(39, \"mat-form-field\", 12)(40, \"mat-label\");\n        i0.ɵɵtext(41, \"Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(42, \"input\", 17)(43, \"mat-datepicker-toggle\", 18)(44, \"mat-datepicker\", null, 19);\n        i0.ɵɵtemplate(46, EditClientComponent_Conditional_46_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(47, \"div\", 11)(48, \"mat-form-field\", 12)(49, \"mat-label\");\n        i0.ɵɵtext(50, \"Currency\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(51, \"input\", 20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(52, \"div\", 10)(53, \"div\", 21)(54, \"mat-form-field\", 12)(55, \"mat-label\");\n        i0.ɵɵtext(56, \"Billing Method\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"mat-select\", 22)(58, \"mat-option\", 23);\n        i0.ɵɵtext(59, \" Fixed Price \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"mat-option\", 23);\n        i0.ɵɵtext(61, \" Hourly User Rate \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"mat-option\", 23);\n        i0.ɵɵtext(63, \" Hourly Job Rate \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(64, EditClientComponent_Conditional_64_Template, 2, 0, \"mat-error\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(65, \"div\", 10)(66, \"div\", 21)(67, \"mat-label\");\n        i0.ɵɵtext(68, \"Upload Image\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(69, \"app-file-upload\", 24);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(70, \"div\", 10)(71, \"div\", 21)(72, \"button\", 25);\n        i0.ɵɵtext(73, \"Submit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"button\", 26);\n        i0.ɵɵtext(75, \"Cancel\");\n        i0.ɵɵelementEnd()()()()()()()()()();\n      }\n      if (rf & 2) {\n        const _r3 = i0.ɵɵreference(45);\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_6_0;\n        let tmp_9_0;\n        let tmp_13_0;\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Edit Client\")(\"items\", i0.ɵɵpureFunction0(15, _c0))(\"active_item\", \"Edit Client\");\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"formGroup\", ctx.clientForm);\n        i0.ɵɵadvance(7);\n        i0.ɵɵconditional(18, ((tmp_4_0 = ctx.clientForm.get(\"name\")) == null ? null : tmp_4_0.hasError(\"required\")) ? 18 : -1);\n        i0.ɵɵadvance(6);\n        i0.ɵɵconditional(24, ((tmp_5_0 = ctx.clientForm.get(\"company_name\")) == null ? null : tmp_5_0.hasError(\"required\")) ? 24 : -1);\n        i0.ɵɵadvance(7);\n        i0.ɵɵconditional(31, ((tmp_6_0 = ctx.clientForm.get(\"email\")) == null ? null : tmp_6_0.hasError(\"email\")) && ((tmp_6_0 = ctx.clientForm.get(\"email\")) == null ? null : tmp_6_0.touched) ? 31 : -1);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"matDatepicker\", _r3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"for\", _r3);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(46, ((tmp_9_0 = ctx.clientForm.get(\"date\")) == null ? null : tmp_9_0.hasError(\"required\")) ? 46 : -1);\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"value\", \"Fixed Price\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Hourly User Rate\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", \"Hourly Job Rate\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(64, ((tmp_13_0 = ctx.clientForm.get(\"billing_method\")) == null ? null : tmp_13_0.hasError(\"required\")) ? 64 : -1);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"disabled\", !ctx.clientForm.valid);\n      }\n    },\n    dependencies: [BreadcrumbComponent, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, MatFormFieldModule, i2.MatFormField, i2.MatLabel, i2.MatError, i2.MatSuffix, MatInputModule, i3.MatInput, MatDatepickerModule, i4.MatDatepicker, i4.MatDatepickerInput, i4.MatDatepickerToggle, MatSelectModule, i5.MatSelect, i6.MatOption, MatOptionModule, FileUploadComponent, MatButtonModule, i7.MatButton],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "FileUploadComponent", "MatOptionModule", "MatSelectModule", "MatDatepickerModule", "MatInputModule", "MatFormFieldModule", "BreadcrumbComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "EditClientComponent", "constructor", "fb", "formdata", "name", "mobile", "email", "date", "company_name", "currency", "billing_method", "uploadImg", "clientForm", "createContactForm", "onSubmit", "console", "log", "value", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "_", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "EditClientComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "EditClientComponent_Template_form_ngSubmit_11_listener", "ɵɵtemplate", "EditClientComponent_Conditional_18_Template", "EditClientComponent_Conditional_24_Template", "EditClientComponent_Conditional_31_Template", "EditClientComponent_Conditional_46_Template", "EditClientComponent_Conditional_64_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵconditional", "tmp_4_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_5_0", "tmp_6_0", "touched", "_r3", "tmp_9_0", "tmp_13_0", "valid", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "i2", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i3", "MatInput", "i4", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i5", "MatSelect", "i6", "MatOption", "i7", "MatButton", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\clients\\edit-client\\edit-client.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\clients\\edit-client\\edit-client.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport {\r\n  UntypedFormBuilder,\r\n  UntypedFormGroup,\r\n  Validators,\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n} from '@angular/forms';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';\r\nimport { MatOptionModule } from '@angular/material/core';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatDatepickerModule } from '@angular/material/datepicker';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n@Component({\r\n  selector: 'app-edit-client',\r\n  templateUrl: './edit-client.component.html',\r\n  styleUrls: ['./edit-client.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    BreadcrumbComponent,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatDatepickerModule,\r\n    MatSelectModule,\r\n    MatOptionModule,\r\n    FileUploadComponent,\r\n    MatButtonModule,\r\n  ],\r\n})\r\nexport class EditClientComponent {\r\n  clientForm: UntypedFormGroup;\r\n  formdata = {\r\n    name: 'Pooja Sarma',\r\n    mobile: '*********',\r\n    email: '<EMAIL>',\r\n    date: '1987-02-17T14:22:18Z',\r\n    company_name: 'ABC Infotech',\r\n    currency: 'rupee',\r\n    billing_method: 'Fixed Price',\r\n    uploadImg: '',\r\n  };\r\n\r\n  constructor(private fb: UntypedFormBuilder) {\r\n    this.clientForm = this.createContactForm();\r\n  }\r\n  onSubmit() {\r\n    console.log('Form Value', this.clientForm.value);\r\n  }\r\n  createContactForm(): UntypedFormGroup {\r\n    return this.fb.group({\r\n      name: [this.formdata.name, [Validators.required]],\r\n      mobile: [this.formdata.mobile, [Validators.required]],\r\n      email: [\r\n        this.formdata.email,\r\n        [Validators.required, Validators.email, Validators.minLength(5)],\r\n      ],\r\n      date: [this.formdata.date, [Validators.required]],\r\n      company_name: [this.formdata.company_name],\r\n      currency: [this.formdata.currency],\r\n      billing_method: [this.formdata.billing_method],\r\n      uploadImg: [this.formdata.uploadImg],\r\n    });\r\n  }\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Edit Client'\" [items]=\"['Clients']\" [active_item]=\"'Edit Client'\">\r\n      </app-breadcrumb>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Edit Client</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <form class=\"m-4\" [formGroup]=\"clientForm\" (ngSubmit)=\"onSubmit()\">\r\n              <div class=\"row\">\r\n                <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                  <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n                    <mat-label>Name</mat-label>\r\n                    <input matInput formControlName=\"name\" required>\r\n                      @if (clientForm.get('name')?.hasError('required')) {\r\n                        <mat-error>\r\n                          Name is required\r\n                        </mat-error>\r\n                      }\r\n                    </mat-form-field>\r\n                  </div>\r\n                  <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                    <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n                      <mat-label>Company Name</mat-label>\r\n                      <input matInput formControlName=\"company_name\" required>\r\n                        @if (clientForm.get('company_name')?.hasError('required')) {\r\n                          <mat-error>\r\n                            Company Name is required\r\n                          </mat-error>\r\n                        }\r\n                      </mat-form-field>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"row\">\r\n                    <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                      <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n                        <mat-label>Email</mat-label>\r\n                        <input matInput formControlName=\"email\" required>\r\n                          @if (clientForm.get('email')?.hasError('email') && clientForm.get('email')?.touched) {\r\n                            <mat-error>\r\n                              Please enter a valid email address\r\n                            </mat-error>\r\n                          }\r\n                        </mat-form-field>\r\n                      </div>\r\n                      <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                        <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n                          <mat-label>Mobile</mat-label>\r\n                          <input matInput formControlName=\"mobile\" type=\"number\" required>\r\n                          </mat-form-field>\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"row\">\r\n                        <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                          <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n                            <mat-label>Date</mat-label>\r\n                            <input matInput [matDatepicker]=\"picker\" formControlName=\"date\" required>\r\n                              <mat-datepicker-toggle matSuffix [for]=\"picker\"></mat-datepicker-toggle>\r\n                              <mat-datepicker #picker></mat-datepicker>\r\n                              @if (clientForm.get('date')?.hasError('required')) {\r\n                                <mat-error>\r\n                                  Please select date\r\n                                </mat-error>\r\n                              }\r\n                            </mat-form-field>\r\n                          </div>\r\n                          <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-3\">\r\n                            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n                              <mat-label>Currency</mat-label>\r\n                              <input matInput formControlName=\"currency\" type=\"text\" required>\r\n                              </mat-form-field>\r\n                            </div>\r\n                          </div>\r\n                          <div class=\"row\">\r\n                            <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                              <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n                                <mat-label>Billing Method</mat-label>\r\n                                <mat-select formControlName=\"billing_method\" required>\r\n                                  <mat-option [value]=\"'Fixed Price'\">\r\n                                    Fixed Price\r\n                                  </mat-option>\r\n                                  <mat-option [value]=\"'Hourly User Rate'\">\r\n                                    Hourly User Rate\r\n                                  </mat-option>\r\n                                  <mat-option [value]=\"'Hourly Job Rate'\">\r\n                                    Hourly Job Rate\r\n                                  </mat-option>\r\n                                </mat-select>\r\n                                @if (clientForm.get('billing_method')?.hasError('required')) {\r\n                                  <mat-error>\r\n                                    Select Any Billing Method\r\n                                  </mat-error>\r\n                                }\r\n                              </mat-form-field>\r\n                            </div>\r\n                          </div>\r\n                          <div class=\"row\">\r\n                            <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                              <mat-label>Upload Image</mat-label>\r\n                              <app-file-upload formControlName=\"uploadImg\"></app-file-upload>\r\n                            </div>\r\n                          </div>\r\n                          <div class=\"row\">\r\n                            <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-3\">\r\n                              <button class=\"btn-space\" [disabled]=\"!clientForm.valid \" mat-raised-button\r\n                              color=\"primary\">Submit</button>\r\n                              <button type=\"button\" mat-raised-button color=\"warn\">Cancel</button>\r\n                            </div>\r\n                          </div>\r\n                        </form>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </section>\r\n            "], "mappings": "AACA,SAGEA,UAAU,EACVC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,sDAAsD;AAC1F,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,mBAAmB,QAAQ,oDAAoD;;;;;;;;;;;ICMhEC,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IASVH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWVH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAmBRH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA2BVH,EAAA,CAAAC,cAAA,gBAAW;IACTD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;AD/D9C,OAAM,MAAOC,mBAAmB;EAa9BC,YAAoBC,EAAsB;IAAtB,KAAAA,EAAE,GAAFA,EAAE;IAXtB,KAAAC,QAAQ,GAAG;MACTC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAE,WAAW;MACnBC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE,sBAAsB;MAC5BC,YAAY,EAAE,cAAc;MAC5BC,QAAQ,EAAE,OAAO;MACjBC,cAAc,EAAE,aAAa;MAC7BC,SAAS,EAAE;KACZ;IAGC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,iBAAiB,EAAE;EAC5C;EACAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACJ,UAAU,CAACK,KAAK,CAAC;EAClD;EACAJ,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACX,EAAE,CAACgB,KAAK,CAAC;MACnBd,IAAI,EAAE,CAAC,IAAI,CAACD,QAAQ,CAACC,IAAI,EAAE,CAACnB,UAAU,CAACkC,QAAQ,CAAC,CAAC;MACjDd,MAAM,EAAE,CAAC,IAAI,CAACF,QAAQ,CAACE,MAAM,EAAE,CAACpB,UAAU,CAACkC,QAAQ,CAAC,CAAC;MACrDb,KAAK,EAAE,CACL,IAAI,CAACH,QAAQ,CAACG,KAAK,EACnB,CAACrB,UAAU,CAACkC,QAAQ,EAAElC,UAAU,CAACqB,KAAK,EAAErB,UAAU,CAACmC,SAAS,CAAC,CAAC,CAAC,CAAC,CACjE;MACDb,IAAI,EAAE,CAAC,IAAI,CAACJ,QAAQ,CAACI,IAAI,EAAE,CAACtB,UAAU,CAACkC,QAAQ,CAAC,CAAC;MACjDX,YAAY,EAAE,CAAC,IAAI,CAACL,QAAQ,CAACK,YAAY,CAAC;MAC1CC,QAAQ,EAAE,CAAC,IAAI,CAACN,QAAQ,CAACM,QAAQ,CAAC;MAClCC,cAAc,EAAE,CAAC,IAAI,CAACP,QAAQ,CAACO,cAAc,CAAC;MAC9CC,SAAS,EAAE,CAAC,IAAI,CAACR,QAAQ,CAACQ,SAAS;KACpC,CAAC;EACJ;EAAC,QAAAU,CAAA,G;qBAjCUrB,mBAAmB,EAAAJ,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBzB,mBAAmB;IAAA0B,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAhC,EAAA,CAAAiC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClChCvC,EAAA,CAAAC,cAAA,iBAAyB;QAInBD,EAAA,CAAAyC,SAAA,wBACiB;QACnBzC,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAA0B;QAIdD,EAAA,CAAAE,MAAA,kBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEtBH,EAAA,CAAAC,cAAA,cAAkB;QAC2BD,EAAA,CAAA0C,UAAA,sBAAAC,uDAAA;UAAA,OAAYH,GAAA,CAAAtB,QAAA,EAAU;QAAA,EAAC;QAChElB,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC3BH,EAAA,CAAAyC,SAAA,iBAAgD;QAC9CzC,EAAA,CAAA4C,UAAA,KAAAC,2CAAA,oBAIC;QACH7C,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,eAAwD;QAEzCD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACnCH,EAAA,CAAAyC,SAAA,iBAAwD;QACtDzC,EAAA,CAAA4C,UAAA,KAAAE,2CAAA,oBAIC;QACH9C,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC5BH,EAAA,CAAAyC,SAAA,iBAAiD;QAC/CzC,EAAA,CAAA4C,UAAA,KAAAG,2CAAA,oBAIC;QACH/C,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,eAAwD;QAEzCD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC7BH,EAAA,CAAAyC,SAAA,iBAAgE;QAChEzC,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC3BH,EAAA,CAAAyC,SAAA,iBAAyE;QAGvEzC,EAAA,CAAA4C,UAAA,KAAAI,2CAAA,oBAIC;QACHhD,EAAA,CAAAG,YAAA,EAAiB;QAEnBH,EAAA,CAAAC,cAAA,eAAwD;QAEzCD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAyC,SAAA,iBAAgE;QAChEzC,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAGAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACrCH,EAAA,CAAAC,cAAA,sBAAsD;QAElDD,EAAA,CAAAE,MAAA,qBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,sBAAyC;QACvCD,EAAA,CAAAE,MAAA,0BACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAC,cAAA,sBAAwC;QACtCD,EAAA,CAAAE,MAAA,yBACF;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAEfH,EAAA,CAAA4C,UAAA,KAAAK,2CAAA,oBAIC;QACHjD,EAAA,CAAAG,YAAA,EAAiB;QAGrBH,EAAA,CAAAC,cAAA,eAAiB;QAEFD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACnCH,EAAA,CAAAyC,SAAA,2BAA+D;QACjEzC,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAC,cAAA,eAAiB;QAGGD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC/BH,EAAA,CAAAC,cAAA,kBAAqD;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;QA5G5EH,EAAA,CAAAkD,SAAA,GAAuB;QAAvBlD,EAAA,CAAAmD,UAAA,wBAAuB,UAAAnD,EAAA,CAAAoD,eAAA,KAAAC,GAAA;QAUfrD,EAAA,CAAAkD,SAAA,GAAwB;QAAxBlD,EAAA,CAAAmD,UAAA,cAAAX,GAAA,CAAAxB,UAAA,CAAwB;QAMhChB,EAAA,CAAAkD,SAAA,GAIC;QAJDlD,EAAA,CAAAsD,aAAA,OAAAC,OAAA,GAAAf,GAAA,CAAAxB,UAAA,CAAAwC,GAAA,2BAAAD,OAAA,CAAAE,QAAA,wBAIC;QAOCzD,EAAA,CAAAkD,SAAA,GAIC;QAJDlD,EAAA,CAAAsD,aAAA,OAAAI,OAAA,GAAAlB,GAAA,CAAAxB,UAAA,CAAAwC,GAAA,mCAAAE,OAAA,CAAAD,QAAA,wBAIC;QASCzD,EAAA,CAAAkD,SAAA,GAIC;QAJDlD,EAAA,CAAAsD,aAAA,OAAAK,OAAA,GAAAnB,GAAA,CAAAxB,UAAA,CAAAwC,GAAA,4BAAAG,OAAA,CAAAF,QAAA,gBAAAE,OAAA,GAAAnB,GAAA,CAAAxB,UAAA,CAAAwC,GAAA,4BAAAG,OAAA,CAAAC,OAAA,YAIC;QAciB5D,EAAA,CAAAkD,SAAA,IAAwB;QAAxBlD,EAAA,CAAAmD,UAAA,kBAAAU,GAAA,CAAwB;QACL7D,EAAA,CAAAkD,SAAA,EAAc;QAAdlD,EAAA,CAAAmD,UAAA,QAAAU,GAAA,CAAc;QAE/C7D,EAAA,CAAAkD,SAAA,GAIC;QAJDlD,EAAA,CAAAsD,aAAA,OAAAQ,OAAA,GAAAtB,GAAA,CAAAxB,UAAA,CAAAwC,GAAA,2BAAAM,OAAA,CAAAL,QAAA,wBAIC;QAeezD,EAAA,CAAAkD,SAAA,IAAuB;QAAvBlD,EAAA,CAAAmD,UAAA,wBAAuB;QAGvBnD,EAAA,CAAAkD,SAAA,GAA4B;QAA5BlD,EAAA,CAAAmD,UAAA,6BAA4B;QAG5BnD,EAAA,CAAAkD,SAAA,GAA2B;QAA3BlD,EAAA,CAAAmD,UAAA,4BAA2B;QAIzCnD,EAAA,CAAAkD,SAAA,GAIC;QAJDlD,EAAA,CAAAsD,aAAA,OAAAS,QAAA,GAAAvB,GAAA,CAAAxB,UAAA,CAAAwC,GAAA,qCAAAO,QAAA,CAAAN,QAAA,wBAIC;QAYuBzD,EAAA,CAAAkD,SAAA,GAA+B;QAA/BlD,EAAA,CAAAmD,UAAA,cAAAX,GAAA,CAAAxB,UAAA,CAAAgD,KAAA,CAA+B;;;mBDxFnFjE,mBAAmB,EACnBT,WAAW,EAAAqC,EAAA,CAAAsC,aAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,mBAAA,EAAAxC,EAAA,CAAAyC,eAAA,EAAAzC,EAAA,CAAA0C,oBAAA,EAAA1C,EAAA,CAAA2C,iBAAA,EACX/E,mBAAmB,EAAAoC,EAAA,CAAA4C,kBAAA,EAAA5C,EAAA,CAAA6C,eAAA,EACnB1E,kBAAkB,EAAA2E,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,SAAA,EAClBhF,cAAc,EAAAiF,EAAA,CAAAC,QAAA,EACdnF,mBAAmB,EAAAoF,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,kBAAA,EAAAF,EAAA,CAAAG,mBAAA,EACnBxF,eAAe,EAAAyF,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACf7F,eAAe,EACfD,mBAAmB,EACnBD,eAAe,EAAAgG,EAAA,CAAAC,SAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}