{"ast": null, "code": "import { FeatherModule } from 'angular-feather';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"angular-feather\";\nexport class FeatherIconsComponent {\n  constructor() {\n    // constructor\n  }\n  static #_ = this.ɵfac = function FeatherIconsComponent_Factory(t) {\n    return new (t || FeatherIconsComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FeatherIconsComponent,\n    selectors: [[\"app-feather-icons\"]],\n    inputs: {\n      icon: \"icon\",\n      class: \"class\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 1,\n    vars: 3,\n    consts: [[3, \"name\"]],\n    template: function FeatherIconsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"i-feather\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.class);\n        i0.ɵɵproperty(\"name\", ctx.icon);\n      }\n    },\n    dependencies: [FeatherModule, i1.FeatherComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["FeatherModule", "FeatherIconsComponent", "constructor", "_", "_2", "selectors", "inputs", "icon", "class", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeatherIconsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵclassMap", "ɵɵproperty", "i1", "FeatherComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Rapport_PFE\\kuber\\source\\mian\\src\\app\\shared\\components\\feather-icons\\feather-icons.component.ts", "C:\\Users\\<USER>\\Desktop\\Rapport_PFE\\kuber\\source\\mian\\src\\app\\shared\\components\\feather-icons\\feather-icons.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { FeatherModule } from 'angular-feather';\r\n\r\n@Component({\r\n    selector: 'app-feather-icons',\r\n    templateUrl: './feather-icons.component.html',\r\n    styleUrls: ['./feather-icons.component.scss'],\r\n    standalone: true,\r\n    imports: [FeatherModule],\r\n})\r\nexport class FeatherIconsComponent {\r\n  @Input() public icon?: string;\r\n  @Input() public class?: string;\r\n  constructor() {\r\n    // constructor\r\n  }\r\n}\r\n", "<i-feather [name]=\"icon!\" [class]=\"class\"></i-feather>\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,iBAAiB;;;AAS/C,OAAM,MAAOC,qBAAqB;EAGhCC,YAAA;IACE;EAAA;EACD,QAAAC,CAAA,G;qBALUF,qBAAqB;EAAA;EAAA,QAAAG,EAAA,G;UAArBH,qBAAqB;IAAAI,SAAA;IAAAC,MAAA;MAAAC,IAAA;MAAAC,KAAA;IAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVlCP,EAAA,CAAAS,SAAA,mBAAsD;;;QAA5BT,EAAA,CAAAU,UAAA,CAAAF,GAAA,CAAAX,KAAA,CAAe;QAA9BG,EAAA,CAAAW,UAAA,SAAAH,GAAA,CAAAZ,IAAA,CAAc;;;mBDQXP,aAAa,EAAAuB,EAAA,CAAAC,gBAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}