{"ast": null, "code": "import baseMean from './_baseMean.js';\nimport identity from './identity.js';\n\n/**\n * Computes the mean of the values in `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {number} Returns the mean.\n * @example\n *\n * _.mean([4, 2, 8, 6]);\n * // => 5\n */\nfunction mean(array) {\n  return baseMean(array, identity);\n}\nexport default mean;", "map": {"version": 3, "names": ["baseMean", "identity", "mean", "array"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/lodash-es/mean.js"], "sourcesContent": ["import baseMean from './_baseMean.js';\nimport identity from './identity.js';\n\n/**\n * Computes the mean of the values in `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {number} Returns the mean.\n * @example\n *\n * _.mean([4, 2, 8, 6]);\n * // => 5\n */\nfunction mean(array) {\n  return baseMean(array, identity);\n}\n\nexport default mean;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,OAAOH,QAAQ,CAACG,KAAK,EAAEF,QAAQ,CAAC;AAClC;AAEA,eAAeC,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}