{"ast": null, "code": "import { CardsComponent } from \"./cards/cards.component\";\nimport { DialogsComponent } from \"./dialogs/dialogs.component\";\nimport { LabelsComponent } from \"./labels/labels.component\";\nimport { ListGroupComponent } from \"./list-group/list-group.component\";\nimport { PreloadersComponent } from \"./preloaders/preloaders.component\";\nimport { TabsComponent } from \"./tabs/tabs.component\";\nimport { TypographyComponent } from \"./typography/typography.component\";\nimport { HelperClassesComponent } from \"./helper-classes/helper-classes.component\";\nimport { AlertsComponent } from \"./alerts/alerts.component\";\nimport { BadgeComponent } from \"./badge/badge.component\";\nimport { ButtonsComponent } from \"./buttons/buttons.component\";\nimport { ProgressbarsComponent } from \"./progressbars/progressbars.component\";\nimport { ModalComponent } from \"./modal/modal.component\";\nimport { ChipsComponent } from \"./chips/chips.component\";\nimport { BottomSheetComponent } from \"./bottom-sheet/bottom-sheet.component\";\nimport { SnackbarComponent } from \"./snackbar/snackbar.component\";\nimport { ExpansionPanelComponent } from \"./expansion-panel/expansion-panel.component\";\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\nexport const UI_ROUTE = [{\n  path: \"alerts\",\n  component: AlertsComponent\n}, {\n  path: \"badges\",\n  component: BadgeComponent\n}, {\n  path: \"chips\",\n  component: ChipsComponent\n}, {\n  path: \"buttons\",\n  component: ButtonsComponent\n}, {\n  path: \"cards\",\n  component: CardsComponent\n}, {\n  path: \"expansion-panel\",\n  component: ExpansionPanelComponent\n}, {\n  path: \"bottom-sheet\",\n  component: BottomSheetComponent\n}, {\n  path: \"dialogs\",\n  component: DialogsComponent\n}, {\n  path: \"labels\",\n  component: LabelsComponent\n}, {\n  path: \"list-group\",\n  component: ListGroupComponent\n}, {\n  path: \"modal\",\n  component: ModalComponent\n}, {\n  path: \"snackbar\",\n  component: SnackbarComponent\n}, {\n  path: \"preloaders\",\n  component: PreloadersComponent\n}, {\n  path: \"progressbars\",\n  component: ProgressbarsComponent\n}, {\n  path: \"tabs\",\n  component: TabsComponent\n}, {\n  path: \"typography\",\n  component: TypographyComponent\n}, {\n  path: \"helper-classes\",\n  component: HelperClassesComponent\n}, {\n  path: '**',\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["CardsComponent", "DialogsComponent", "LabelsComponent", "ListGroupComponent", "PreloadersComponent", "TabsComponent", "TypographyComponent", "HelperClassesComponent", "AlertsComponent", "BadgeComponent", "ButtonsComponent", "ProgressbarsComponent", "ModalComponent", "ChipsComponent", "BottomSheetComponent", "SnackbarComponent", "ExpansionPanelComponent", "Page404Component", "UI_ROUTE", "path", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\mian\\src\\app\\ui\\ui.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { CardsComponent } from \"./cards/cards.component\";\r\nimport { DialogsComponent } from \"./dialogs/dialogs.component\";\r\nimport { LabelsComponent } from \"./labels/labels.component\";\r\nimport { ListGroupComponent } from \"./list-group/list-group.component\";\r\nimport { PreloadersComponent } from \"./preloaders/preloaders.component\";\r\nimport { TabsComponent } from \"./tabs/tabs.component\";\r\nimport { TypographyComponent } from \"./typography/typography.component\";\r\nimport { HelperClassesComponent } from \"./helper-classes/helper-classes.component\";\r\nimport { AlertsComponent } from \"./alerts/alerts.component\";\r\nimport { BadgeComponent } from \"./badge/badge.component\";\r\nimport { ButtonsComponent } from \"./buttons/buttons.component\";\r\nimport { ProgressbarsComponent } from \"./progressbars/progressbars.component\";\r\nimport { ModalComponent } from \"./modal/modal.component\";\r\nimport { ChipsComponent } from \"./chips/chips.component\";\r\nimport { BottomSheetComponent } from \"./bottom-sheet/bottom-sheet.component\";\r\nimport { SnackbarComponent } from \"./snackbar/snackbar.component\";\r\nimport { ExpansionPanelComponent } from \"./expansion-panel/expansion-panel.component\";\r\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\r\nexport const UI_ROUTE: Route[] = [\r\n  {\r\n    path: \"alerts\",\r\n    component: AlertsComponent,\r\n  },\r\n  {\r\n    path: \"badges\",\r\n    component: BadgeComponent,\r\n  },\r\n  {\r\n    path: \"chips\",\r\n    component: ChipsComponent,\r\n  },\r\n  {\r\n    path: \"buttons\",\r\n    component: ButtonsComponent,\r\n  },\r\n  {\r\n    path: \"cards\",\r\n    component: CardsComponent,\r\n  },\r\n  {\r\n    path: \"expansion-panel\",\r\n    component: ExpansionPanelComponent,\r\n  },\r\n  {\r\n    path: \"bottom-sheet\",\r\n    component: BottomSheetComponent,\r\n  },\r\n  {\r\n    path: \"dialogs\",\r\n    component: DialogsComponent,\r\n  },\r\n  {\r\n    path: \"labels\",\r\n    component: LabelsComponent,\r\n  },\r\n  {\r\n    path: \"list-group\",\r\n    component: ListGroupComponent,\r\n  },\r\n  {\r\n    path: \"modal\",\r\n    component: ModalComponent,\r\n  },\r\n  {\r\n    path: \"snackbar\",\r\n    component: SnackbarComponent,\r\n  },\r\n  {\r\n    path: \"preloaders\",\r\n    component: PreloadersComponent,\r\n  },\r\n  {\r\n    path: \"progressbars\",\r\n    component: ProgressbarsComponent,\r\n  },\r\n  {\r\n    path: \"tabs\",\r\n    component: TabsComponent,\r\n  },\r\n  {\r\n    path: \"typography\",\r\n    component: TypographyComponent,\r\n  },\r\n  {\r\n    path: \"helper-classes\",\r\n    component: HelperClassesComponent,\r\n  },\r\n  { path: '**', component: Page404Component },\r\n];\r\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,OAAO,MAAMC,QAAQ,GAAY,CAC/B;EACEC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEZ;CACZ,EACD;EACEW,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEX;CACZ,EACD;EACEU,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEP;CACZ,EACD;EACEM,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEV;CACZ,EACD;EACES,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEpB;CACZ,EACD;EACEmB,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEnB;CACZ,EACD;EACEkB,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAElB;CACZ,EACD;EACEiB,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEjB;CACZ,EACD;EACEgB,IAAI,EAAE,OAAO;EACbC,SAAS,EAAER;CACZ,EACD;EACEO,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEhB;CACZ,EACD;EACEe,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAET;CACZ,EACD;EACEQ,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEf;CACZ,EACD;EACEc,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEd;CACZ,EACD;EACEa,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEb;CACZ,EACD;EAAEY,IAAI,EAAE,IAAI;EAAEC,SAAS,EAAEH;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}