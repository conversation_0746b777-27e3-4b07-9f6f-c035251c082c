{"ast": null, "code": "export var Role;\n(function (Role) {\n  Role[\"All\"] = \"All\";\n  Role[\"Admin\"] = \"Admin\";\n  Role[\"Employee\"] = \"Employee\";\n  Role[\"Client\"] = \"Client\";\n})(Role || (Role = {}));", "map": {"version": 3, "names": ["Role"], "sources": ["C:\\Users\\<USER>\\mian\\src\\app\\core\\models\\role.ts"], "sourcesContent": ["export enum Role {\r\n  All = \"All\",\r\n  Admin = \"Admin\",\r\n  Employee = \"Employee\",\r\n  Client = \"Client\",\r\n}\r\n"], "mappings": "AAAA,WAAYA,IAKX;AALD,WAAYA,IAAI;EACdA,IAAA,eAAW;EACXA,IAAA,mBAAe;EACfA,IAAA,yBAAqB;EACrBA,IAAA,qBAAiB;AACnB,CAAC,EALWA,IAAI,KAAJA,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}