{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ProduitDeleteComponent {\n  static #_ = this.ɵfac = function ProduitDeleteComponent_Factory(t) {\n    return new (t || ProduitDeleteComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProduitDeleteComponent,\n    selectors: [[\"app-produit-delete\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 2,\n    vars: 0,\n    template: function ProduitDeleteComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \"produit-delete works!\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["ProduitDeleteComponent", "_", "_2", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "ProduitDeleteComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit-delete\\produit-delete.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit-delete\\produit-delete.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-produit-delete',\r\n  standalone: true,\r\n  imports: [],\r\n  templateUrl: './produit-delete.component.html',\r\n  styleUrl: './produit-delete.component.scss'\r\n})\r\nexport class ProduitDeleteComponent {\r\n\r\n}\r\n", "<p>produit-delete works!</p>\r\n"], "mappings": ";AASA,OAAM,MAAOA,sBAAsB;EAAA,QAAAC,CAAA,G;qBAAtBD,sBAAsB;EAAA;EAAA,QAAAE,EAAA,G;UAAtBF,sBAAsB;IAAAG,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTnCN,EAAA,CAAAQ,cAAA,QAAG;QAAAR,EAAA,CAAAS,MAAA,4BAAqB;QAAAT,EAAA,CAAAU,YAAA,EAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}