{"ast": null, "code": "import { MatBottomSheetModule } from '@angular/material/bottom-sheet';\nimport { MatLineModule } from '@angular/material/core';\nimport { MatListModule } from '@angular/material/list';\nimport { MatButtonModule } from '@angular/material/button';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/bottom-sheet\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/list\";\nimport * as i4 from \"@angular/material/core\";\nconst _c0 = () => [\"Home\", \"UI\"];\nexport class BottomSheetComponent {\n  constructor(_bottomSheet) {\n    this._bottomSheet = _bottomSheet;\n    this.breadscrums = [{\n      title: 'Bottom Sheet',\n      items: ['UI'],\n      active: 'Bottom Sheet'\n    }];\n  }\n  openBottomSheet() {\n    this._bottomSheet.open(BottomSheetOverviewExampleSheetComponent);\n  }\n  static #_ = this.ɵfac = function BottomSheetComponent_Factory(t) {\n    return new (t || BottomSheetComponent)(i0.ɵɵdirectiveInject(i1.MatBottomSheet));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BottomSheetComponent,\n    selectors: [[\"app-bottom-sheet\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 15,\n    vars: 4,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\"], [1, \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-12\"], [1, \"card\"], [1, \"header\"], [1, \"body\"], [\"mat-raised-button\", \"\", 3, \"click\"]],\n    template: function BottomSheetComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Bottom Sheet Overview\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"p\");\n        i0.ɵɵtext(12, \"You have received a file called \\\"cat-picture.jpeg\\\".\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function BottomSheetComponent_Template_button_click_13_listener() {\n          return ctx.openBottomSheet();\n        });\n        i0.ɵɵtext(14, \"Open file\");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Bottom Sheet\")(\"items\", i0.ɵɵpureFunction0(3, _c0))(\"active_item\", \"Bottom Sheet\");\n      }\n    },\n    dependencies: [BreadcrumbComponent, MatButtonModule, i2.MatButton, MatBottomSheetModule],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport class BottomSheetOverviewExampleSheetComponent {\n  constructor(_bottomSheetRef) {\n    this._bottomSheetRef = _bottomSheetRef;\n  }\n  openLink(event) {\n    this._bottomSheetRef.dismiss();\n    event.preventDefault();\n  }\n  static #_ = this.ɵfac = function BottomSheetOverviewExampleSheetComponent_Factory(t) {\n    return new (t || BottomSheetOverviewExampleSheetComponent)(i0.ɵɵdirectiveInject(i1.MatBottomSheetRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BottomSheetOverviewExampleSheetComponent,\n    selectors: [[\"app-bottom-sheet-overview-example-sheet\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 21,\n    vars: 0,\n    consts: [[\"href\", \"https://keep.google.com/\", \"mat-list-item\", \"\", 3, \"click\"], [\"mat-line\", \"\"], [\"href\", \"https://docs.google.com/\", \"mat-list-item\", \"\", 3, \"click\"], [\"href\", \"https://plus.google.com/\", \"mat-list-item\", \"\", 3, \"click\"], [\"href\", \"https://hangouts.google.com/\", \"mat-list-item\", \"\", 3, \"click\"]],\n    template: function BottomSheetOverviewExampleSheetComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-nav-list\")(1, \"a\", 0);\n        i0.ɵɵlistener(\"click\", function BottomSheetOverviewExampleSheetComponent_Template_a_click_1_listener($event) {\n          return ctx.openLink($event);\n        });\n        i0.ɵɵelementStart(2, \"span\", 1);\n        i0.ɵɵtext(3, \"Google Keep\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"span\", 1);\n        i0.ɵɵtext(5, \"Add to a note\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"a\", 2);\n        i0.ɵɵlistener(\"click\", function BottomSheetOverviewExampleSheetComponent_Template_a_click_6_listener($event) {\n          return ctx.openLink($event);\n        });\n        i0.ɵɵelementStart(7, \"span\", 1);\n        i0.ɵɵtext(8, \"Google Docs\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"span\", 1);\n        i0.ɵɵtext(10, \"Embed in a document\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"a\", 3);\n        i0.ɵɵlistener(\"click\", function BottomSheetOverviewExampleSheetComponent_Template_a_click_11_listener($event) {\n          return ctx.openLink($event);\n        });\n        i0.ɵɵelementStart(12, \"span\", 1);\n        i0.ɵɵtext(13, \"Google Plus\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"span\", 1);\n        i0.ɵɵtext(15, \"Share with your friends\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"a\", 4);\n        i0.ɵɵlistener(\"click\", function BottomSheetOverviewExampleSheetComponent_Template_a_click_16_listener($event) {\n          return ctx.openLink($event);\n        });\n        i0.ɵɵelementStart(17, \"span\", 1);\n        i0.ɵɵtext(18, \"Google Hangouts\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"span\", 1);\n        i0.ɵɵtext(20, \"Show to your coworkers\");\n        i0.ɵɵelementEnd()()();\n      }\n    },\n    dependencies: [MatListModule, i3.MatNavList, i3.MatListItem, MatLineModule, i4.MatLine],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MatBottomSheetModule", "MatLineModule", "MatListModule", "MatButtonModule", "BreadcrumbComponent", "BottomSheetComponent", "constructor", "_bottomSheet", "breadscrums", "title", "items", "active", "openBottomSheet", "open", "BottomSheetOverviewExampleSheetComponent", "_", "i0", "ɵɵdirectiveInject", "i1", "MatBottomSheet", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BottomSheetComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "BottomSheetComponent_Template_button_click_13_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "i2", "MatButton", "styles", "_bottomSheetRef", "openLink", "event", "dismiss", "preventDefault", "MatBottomSheetRef", "BottomSheetOverviewExampleSheetComponent_Template", "BottomSheetOverviewExampleSheetComponent_Template_a_click_1_listener", "$event", "BottomSheetOverviewExampleSheetComponent_Template_a_click_6_listener", "BottomSheetOverviewExampleSheetComponent_Template_a_click_11_listener", "BottomSheetOverviewExampleSheetComponent_Template_a_click_16_listener", "i3", "MatNavList", "MatListItem", "i4", "MatLine", "encapsulation"], "sources": ["C:\\Users\\<USER>\\Desktop\\Rapport_PFE\\kuber\\source\\mian\\src\\app\\ui\\bottom-sheet\\bottom-sheet.component.ts", "C:\\Users\\<USER>\\Desktop\\Rapport_PFE\\kuber\\source\\mian\\src\\app\\ui\\bottom-sheet\\bottom-sheet.component.html", "C:\\Users\\<USER>\\Desktop\\Rapport_PFE\\kuber\\source\\mian\\src\\app\\ui\\bottom-sheet\\bottom-sheet-overview-example-sheet.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport {\r\n  MatBottomSheet,\r\n  MatBottomSheetModule,\r\n  MatBottomSheetRef,\r\n} from '@angular/material/bottom-sheet';\r\nimport { MatLineModule } from '@angular/material/core';\r\nimport { MatListModule } from '@angular/material/list';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n\r\n@Component({\r\n  selector: 'app-bottom-sheet',\r\n  templateUrl: './bottom-sheet.component.html',\r\n  styleUrls: ['./bottom-sheet.component.scss'],\r\n  standalone: true,\r\n  imports: [BreadcrumbComponent, MatButtonModule, MatBottomSheetModule],\r\n})\r\nexport class BottomSheetComponent {\r\n  breadscrums = [\r\n    {\r\n      title: 'Bottom Sheet',\r\n      items: ['UI'],\r\n      active: 'Bottom Sheet',\r\n    },\r\n  ];\r\n  constructor(private _bottomSheet: MatBottomSheet) { }\r\n\r\n  openBottomSheet(): void {\r\n    this._bottomSheet.open(BottomSheetOverviewExampleSheetComponent);\r\n  }\r\n}\r\n@Component({\r\n  selector: 'app-bottom-sheet-overview-example-sheet',\r\n  templateUrl: 'bottom-sheet-overview-example-sheet.html',\r\n  standalone: true,\r\n  imports: [MatListModule, MatLineModule],\r\n})\r\nexport class BottomSheetOverviewExampleSheetComponent {\r\n  constructor(\r\n    private _bottomSheetRef: MatBottomSheetRef<BottomSheetOverviewExampleSheetComponent>\r\n  ) { }\r\n\r\n  openLink(event: MouseEvent): void {\r\n    this._bottomSheetRef.dismiss();\r\n    event.preventDefault();\r\n  }\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Bottom Sheet'\" [items]=\"['Home','UI']\" [active_item]=\"'Bottom Sheet'\"></app-breadcrumb>\r\n    </div>\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-12 col-md-12 col-sm-12 col-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Bottom Sheet Overview</h2>\r\n\r\n          </div>\r\n          <div class=\"body\">\r\n            <p>You have received a file called \"cat-picture.jpeg\".</p>\r\n            <button mat-raised-button (click)=\"openBottomSheet()\">Open file</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>", "<mat-nav-list>\r\n  <a href=\"https://keep.google.com/\" mat-list-item (click)=\"openLink($event)\">\r\n    <span mat-line>Google Keep</span>\r\n    <span mat-line>Add to a note</span>\r\n  </a>\r\n  <a href=\"https://docs.google.com/\" mat-list-item (click)=\"openLink($event)\">\r\n    <span mat-line>Google Docs</span>\r\n    <span mat-line>Embed in a document</span>\r\n  </a>\r\n  <a href=\"https://plus.google.com/\" mat-list-item (click)=\"openLink($event)\">\r\n    <span mat-line>Google Plus</span>\r\n    <span mat-line>Share with your friends</span>\r\n  </a>\r\n  <a href=\"https://hangouts.google.com/\" mat-list-item (click)=\"openLink($event)\">\r\n    <span mat-line>Google Hangouts</span>\r\n    <span mat-line>Show to your coworkers</span>\r\n  </a>\r\n</mat-nav-list>\r\n"], "mappings": "AACA,SAEEA,oBAAoB,QAEf,gCAAgC;AACvC,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,oDAAoD;;;;;;;AASxF,OAAM,MAAOC,oBAAoB;EAQ/BC,YAAoBC,YAA4B;IAA5B,KAAAA,YAAY,GAAZA,YAAY;IAPhC,KAAAC,WAAW,GAAG,CACZ;MACEC,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,CAAC,IAAI,CAAC;MACbC,MAAM,EAAE;KACT,CACF;EACmD;EAEpDC,eAAeA,CAAA;IACb,IAAI,CAACL,YAAY,CAACM,IAAI,CAACC,wCAAwC,CAAC;EAClE;EAAC,QAAAC,CAAA,G;qBAZUV,oBAAoB,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBf,oBAAoB;IAAAgB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAP,EAAA,CAAAQ,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClBjCd,EAAA,CAAAgB,cAAA,iBAAyB;QAInBhB,EAAA,CAAAiB,SAAA,wBAAiH;QACnHjB,EAAA,CAAAkB,YAAA,EAAM;QACNlB,EAAA,CAAAgB,cAAA,aAAiB;QAILhB,EAAA,CAAAmB,MAAA,4BAAqB;QAAAnB,EAAA,CAAAkB,YAAA,EAAK;QAGhClB,EAAA,CAAAgB,cAAA,cAAkB;QACbhB,EAAA,CAAAmB,MAAA,6DAAmD;QAAAnB,EAAA,CAAAkB,YAAA,EAAI;QAC1DlB,EAAA,CAAAgB,cAAA,iBAAsD;QAA5BhB,EAAA,CAAAoB,UAAA,mBAAAC,uDAAA;UAAA,OAASN,GAAA,CAAAnB,eAAA,EAAiB;QAAA,EAAC;QAACI,EAAA,CAAAmB,MAAA,iBAAS;QAAAnB,EAAA,CAAAkB,YAAA,EAAS;;;QAX9DlB,EAAA,CAAAsB,SAAA,GAAwB;QAAxBtB,EAAA,CAAAuB,UAAA,yBAAwB,UAAAvB,EAAA,CAAAwB,eAAA,IAAAC,GAAA;;;mBDYlCrC,mBAAmB,EAAED,eAAe,EAAAuC,EAAA,CAAAC,SAAA,EAAE3C,oBAAoB;IAAA4C,MAAA;EAAA;;AAsBtE,OAAM,MAAO9B,wCAAwC;EACnDR,YACUuC,eAA4E;IAA5E,KAAAA,eAAe,GAAfA,eAAe;EACrB;EAEJC,QAAQA,CAACC,KAAiB;IACxB,IAAI,CAACF,eAAe,CAACG,OAAO,EAAE;IAC9BD,KAAK,CAACE,cAAc,EAAE;EACxB;EAAC,QAAAlC,CAAA,G;qBARUD,wCAAwC,EAAAE,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAgC,iBAAA;EAAA;EAAA,QAAA9B,EAAA,G;UAAxCN,wCAAwC;IAAAO,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAP,EAAA,CAAAQ,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAuB,kDAAArB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QEtCrDd,EAAA,CAAAgB,cAAA,mBAAc;QACqChB,EAAA,CAAAoB,UAAA,mBAAAgB,qEAAAC,MAAA;UAAA,OAAStB,GAAA,CAAAe,QAAA,CAAAO,MAAA,CAAgB;QAAA,EAAC;QACzErC,EAAA,CAAAgB,cAAA,cAAe;QAAAhB,EAAA,CAAAmB,MAAA,kBAAW;QAAAnB,EAAA,CAAAkB,YAAA,EAAO;QACjClB,EAAA,CAAAgB,cAAA,cAAe;QAAAhB,EAAA,CAAAmB,MAAA,oBAAa;QAAAnB,EAAA,CAAAkB,YAAA,EAAO;QAErClB,EAAA,CAAAgB,cAAA,WAA4E;QAA3BhB,EAAA,CAAAoB,UAAA,mBAAAkB,qEAAAD,MAAA;UAAA,OAAStB,GAAA,CAAAe,QAAA,CAAAO,MAAA,CAAgB;QAAA,EAAC;QACzErC,EAAA,CAAAgB,cAAA,cAAe;QAAAhB,EAAA,CAAAmB,MAAA,kBAAW;QAAAnB,EAAA,CAAAkB,YAAA,EAAO;QACjClB,EAAA,CAAAgB,cAAA,cAAe;QAAAhB,EAAA,CAAAmB,MAAA,2BAAmB;QAAAnB,EAAA,CAAAkB,YAAA,EAAO;QAE3ClB,EAAA,CAAAgB,cAAA,YAA4E;QAA3BhB,EAAA,CAAAoB,UAAA,mBAAAmB,sEAAAF,MAAA;UAAA,OAAStB,GAAA,CAAAe,QAAA,CAAAO,MAAA,CAAgB;QAAA,EAAC;QACzErC,EAAA,CAAAgB,cAAA,eAAe;QAAAhB,EAAA,CAAAmB,MAAA,mBAAW;QAAAnB,EAAA,CAAAkB,YAAA,EAAO;QACjClB,EAAA,CAAAgB,cAAA,eAAe;QAAAhB,EAAA,CAAAmB,MAAA,+BAAuB;QAAAnB,EAAA,CAAAkB,YAAA,EAAO;QAE/ClB,EAAA,CAAAgB,cAAA,YAAgF;QAA3BhB,EAAA,CAAAoB,UAAA,mBAAAoB,sEAAAH,MAAA;UAAA,OAAStB,GAAA,CAAAe,QAAA,CAAAO,MAAA,CAAgB;QAAA,EAAC;QAC7ErC,EAAA,CAAAgB,cAAA,eAAe;QAAAhB,EAAA,CAAAmB,MAAA,uBAAe;QAAAnB,EAAA,CAAAkB,YAAA,EAAO;QACrClB,EAAA,CAAAgB,cAAA,eAAe;QAAAhB,EAAA,CAAAmB,MAAA,8BAAsB;QAAAnB,EAAA,CAAAkB,YAAA,EAAO;;;mBFqBpChC,aAAa,EAAAuD,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,WAAA,EAAE1D,aAAa,EAAA2D,EAAA,CAAAC,OAAA;IAAAC,aAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}