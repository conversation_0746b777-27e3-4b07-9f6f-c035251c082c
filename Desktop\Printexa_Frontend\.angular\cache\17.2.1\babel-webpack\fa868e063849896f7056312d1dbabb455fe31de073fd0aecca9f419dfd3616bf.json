{"ast": null, "code": "import { UntypedFormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/tabs\";\nimport * as i2 from \"@angular/material/icon\";\nimport * as i3 from \"@angular/material/form-field\";\nimport * as i4 from \"@angular/material/input\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/checkbox\";\nfunction TabsComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 25);\n    i0.ɵɵtext(1, \"thumb_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \" First \");\n  }\n}\nfunction TabsComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 25);\n    i0.ɵɵtext(1, \"thumb_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \" Second \");\n  }\n}\nfunction TabsComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 25);\n    i0.ɵɵtext(1, \"thumb_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \" Third \");\n  }\n}\nfunction TabsComponent_For_92_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab\", 26);\n    i0.ɵɵelement(1, \"br\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TabsComponent_For_92_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const index_r6 = restoredCtx.$index;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.removeTab(index_r6));\n    });\n    i0.ɵɵtext(4, \" Delete Tab \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tab_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", tab_r5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Contents for \", tab_r5, \" tab \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.tabs.length === 1);\n  }\n}\nconst _c0 = () => [\"Home\", \"UI\"];\nexport class TabsComponent {\n  constructor() {\n    this.tabs = ['First', 'Second', 'Third'];\n    this.selected = new UntypedFormControl(0);\n  }\n  addTab(selectAfterAdding) {\n    this.tabs.push('New');\n    if (selectAfterAdding) {\n      this.selected.setValue(this.tabs.length - 1);\n    }\n  }\n  removeTab(index) {\n    this.tabs.splice(index, 1);\n  }\n  static #_ = this.ɵfac = function TabsComponent_Factory(t) {\n    return new (t || TabsComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TabsComponent,\n    selectors: [[\"app-tabs\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 121,\n    vars: 6,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\"], [1, \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"card\"], [1, \"header\"], [1, \"body\"], [\"label\", \"First\"], [\"label\", \"Second\"], [\"label\", \"Third\"], [\"mat-tab-label\", \"\"], [\"dynamicHeight\", \"\"], [\"label\", \"Short tab\"], [1, \"example-small-box\", \"mat-elevation-z4\"], [\"label\", \"Long tab\"], [1, \"example-large-box\", \"mat-elevation-z4\"], [\"matInput\", \"\", \"type\", \"number\", 3, \"formControl\"], [1, \"mb-3\"], [\"mat-raised-button\", \"\", 1, \"example-add-tab-button\", \"msr-2\", 3, \"click\"], [\"selectAfterAdding\", \"\"], [3, \"selectedIndex\", \"selectedIndexChange\"], [\"headerPosition\", \"below\"], [\"mat-stretch-tabs\", \"\", 1, \"example-stretched-tabs\", \"mat-elevation-z4\"], [1, \"example-tab-icon\", \"msr-2\"], [3, \"label\"], [\"mat-raised-button\", \"\", 1, \"example-delete-tab-button\", 3, \"disabled\", \"click\"]],\n    template: function TabsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r12 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Basic Tab Examples\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"mat-tab-group\")(12, \"mat-tab\", 9);\n        i0.ɵɵelement(13, \"br\");\n        i0.ɵɵelementStart(14, \"b\");\n        i0.ɵɵtext(15, \"Home Content\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"p\");\n        i0.ɵɵtext(17, \" Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit mediocritatem an. Pri ut tation electram moderatius. Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent aliquid pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere gubergren sadipscing mel. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"mat-tab\", 10);\n        i0.ɵɵelement(19, \"br\");\n        i0.ɵɵelementStart(20, \"b\");\n        i0.ɵɵtext(21, \"Profile Content\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"p\");\n        i0.ɵɵtext(23, \" Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit mediocritatem an. Pri ut tation electram moderatius. Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent aliquid pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere gubergren sadipscing mel. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"mat-tab\", 11);\n        i0.ɵɵelement(25, \"br\");\n        i0.ɵɵelementStart(26, \"b\");\n        i0.ɵɵtext(27, \"Message Content\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"p\");\n        i0.ɵɵtext(29, \" Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit mediocritatem an. Pri ut tation electram moderatius. Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent aliquid pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere gubergren sadipscing mel. \");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(30, \"div\", 4)(31, \"div\", 5)(32, \"div\", 6)(33, \"div\", 7)(34, \"h2\");\n        i0.ɵɵtext(35, \"Tab With Icons\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"div\", 8)(37, \"mat-tab-group\")(38, \"mat-tab\");\n        i0.ɵɵtemplate(39, TabsComponent_ng_template_39_Template, 3, 0, \"ng-template\", 12);\n        i0.ɵɵelement(40, \"br\");\n        i0.ɵɵelementStart(41, \"b\");\n        i0.ɵɵtext(42, \"Home Content\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"p\");\n        i0.ɵɵtext(44, \" Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit mediocritatem an. Pri ut tation electram moderatius. Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent aliquid pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere gubergren sadipscing mel. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"mat-tab\");\n        i0.ɵɵtemplate(46, TabsComponent_ng_template_46_Template, 3, 0, \"ng-template\", 12);\n        i0.ɵɵelement(47, \"br\");\n        i0.ɵɵelementStart(48, \"b\");\n        i0.ɵɵtext(49, \"Profile Content\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"p\");\n        i0.ɵɵtext(51, \" Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit mediocritatem an. Pri ut tation electram moderatius. Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent aliquid pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere gubergren sadipscing mel. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(52, \"mat-tab\");\n        i0.ɵɵtemplate(53, TabsComponent_ng_template_53_Template, 3, 0, \"ng-template\", 12);\n        i0.ɵɵelement(54, \"br\");\n        i0.ɵɵelementStart(55, \"b\");\n        i0.ɵɵtext(56, \"Message Content\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"p\");\n        i0.ɵɵtext(58, \" Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit mediocritatem an. Pri ut tation electram moderatius. Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent aliquid pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere gubergren sadipscing mel. \");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(59, \"div\", 4)(60, \"div\", 5)(61, \"div\", 6)(62, \"div\", 7)(63, \"h2\");\n        i0.ɵɵtext(64, \"Tab group with dynamic height based on tab contents\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(65, \"div\", 8)(66, \"mat-tab-group\", 13)(67, \"mat-tab\", 14)(68, \"div\", 15);\n        i0.ɵɵtext(69, \" Small content \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(70, \"mat-tab\", 16)(71, \"div\", 17);\n        i0.ɵɵtext(72, \" Large content \");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(73, \"div\", 4)(74, \"div\", 5)(75, \"div\", 6)(76, \"div\", 7)(77, \"h2\");\n        i0.ɵɵtext(78, \"Tab group with dynamically changing tabs\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(79, \"div\", 8)(80, \"mat-form-field\")(81, \"mat-label\");\n        i0.ɵɵtext(82, \"Selected tab index\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(83, \"input\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(84, \"div\", 19)(85, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function TabsComponent_Template_button_click_85_listener() {\n          i0.ɵɵrestoreView(_r12);\n          const _r3 = i0.ɵɵreference(88);\n          return i0.ɵɵresetView(ctx.addTab(_r3.checked));\n        });\n        i0.ɵɵtext(86, \" Add new tab \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(87, \"mat-checkbox\", null, 21);\n        i0.ɵɵtext(89, \" Select tab after adding \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(90, \"mat-tab-group\", 22);\n        i0.ɵɵlistener(\"selectedIndexChange\", function TabsComponent_Template_mat_tab_group_selectedIndexChange_90_listener($event) {\n          return ctx.selected.setValue($event);\n        });\n        i0.ɵɵrepeaterCreate(91, TabsComponent_For_92_Template, 5, 3, \"mat-tab\", 26, i0.ɵɵrepeaterTrackByIdentity);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(93, \"div\", 4)(94, \"div\", 5)(95, \"div\", 6)(96, \"div\", 7)(97, \"h2\");\n        i0.ɵɵtext(98, \"Tab group with the headers on the bottom \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(99, \"div\", 8)(100, \"mat-tab-group\", 23)(101, \"mat-tab\", 9);\n        i0.ɵɵtext(102, \" Content 1 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"mat-tab\", 10);\n        i0.ɵɵtext(104, \" Content 2 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(105, \"mat-tab\", 11);\n        i0.ɵɵtext(106, \" Content 3 \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(107, \"div\", 4)(108, \"div\", 5)(109, \"div\", 6)(110, \"div\", 7)(111, \"h2\");\n        i0.ɵɵtext(112, \"Tab group with stretched labels \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(113, \"div\", 8)(114, \"mat-tab-group\", 24)(115, \"mat-tab\", 9);\n        i0.ɵɵtext(116, \" Content 1 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(117, \"mat-tab\", 10);\n        i0.ɵɵtext(118, \" Content 2 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(119, \"mat-tab\", 11);\n        i0.ɵɵtext(120, \" Content 3 \");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Tabs\")(\"items\", i0.ɵɵpureFunction0(5, _c0))(\"active_item\", \"Tabs\");\n        i0.ɵɵadvance(80);\n        i0.ɵɵproperty(\"formControl\", ctx.selected);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"selectedIndex\", ctx.selected.value);\n        i0.ɵɵadvance();\n        i0.ɵɵrepeater(ctx.tabs);\n      }\n    },\n    dependencies: [BreadcrumbComponent, MatTabsModule, i1.MatTabLabel, i1.MatTab, i1.MatTabGroup, MatIconModule, i2.MatIcon, MatFormFieldModule, i3.MatFormField, i3.MatLabel, MatInputModule, i4.MatInput, FormsModule, i5.DefaultValueAccessor, i5.NumberValueAccessor, i5.NgControlStatus, ReactiveFormsModule, i5.FormControlDirective, MatButtonModule, i6.MatButton, MatCheckboxModule, i7.MatCheckbox],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormControl", "FormsModule", "ReactiveFormsModule", "MatCheckboxModule", "MatButtonModule", "MatInputModule", "MatFormFieldModule", "MatIconModule", "MatTabsModule", "BreadcrumbComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "TabsComponent_For_92_Template_button_click_3_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "index_r6", "$index", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "removeTab", "ɵɵproperty", "tab_r5", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r4", "tabs", "length", "TabsComponent", "constructor", "selected", "addTab", "selectAfterAdding", "push", "setValue", "index", "splice", "_", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TabsComponent_Template", "rf", "ctx", "ɵɵtemplate", "TabsComponent_ng_template_39_Template", "TabsComponent_ng_template_46_Template", "TabsComponent_ng_template_53_Template", "TabsComponent_Template_button_click_85_listener", "_r12", "_r3", "ɵɵreference", "checked", "TabsComponent_Template_mat_tab_group_selectedIndexChange_90_listener", "$event", "ɵɵrepeaterCreate", "TabsComponent_For_92_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵpureFunction0", "_c0", "value", "ɵɵrepeater", "i1", "MatTab<PERSON><PERSON><PERSON>", "Mat<PERSON><PERSON>", "MatTabGroup", "i2", "MatIcon", "i3", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "i4", "MatInput", "i5", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "FormControlDirective", "i6", "MatButton", "i7", "MatCheckbox", "styles"], "sources": ["C:\\Users\\<USER>\\mian\\src\\app\\ui\\tabs\\tabs.component.ts", "C:\\Users\\<USER>\\mian\\src\\app\\ui\\tabs\\tabs.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport {\r\n  UntypedFormControl,\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n} from '@angular/forms';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n@Component({\r\n  selector: 'app-tabs',\r\n  templateUrl: './tabs.component.html',\r\n  styleUrls: ['./tabs.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    BreadcrumbComponent,\r\n    MatTabsModule,\r\n    MatIconModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    MatButtonModule,\r\n    MatCheckboxModule,\r\n  ],\r\n})\r\nexport class TabsComponent {\r\n  tabs = ['First', 'Second', 'Third'];\r\n  selected = new UntypedFormControl(0);\r\n  addTab(selectAfterAdding: boolean) {\r\n    this.tabs.push('New');\r\n    if (selectAfterAdding) {\r\n      this.selected.setValue(this.tabs.length - 1);\r\n    }\r\n  }\r\n  removeTab(index: number) {\r\n    this.tabs.splice(index, 1);\r\n  }\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Tabs'\" [items]=\"['Home','UI']\" [active_item]=\"'Tabs'\"></app-breadcrumb>\r\n    </div>\r\n    <!-- Example Tab -->\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Basic Tab Examples</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <mat-tab-group>\r\n              <mat-tab label=\"First\">\r\n                <br>\r\n                  <b>Home Content</b>\r\n                  <p>\r\n                    Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit\r\n                    mediocritatem an. Pri ut tation electram moderatius.\r\n                    Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent\r\n                    aliquid\r\n                    pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere\r\n                    gubergren\r\n                    sadipscing mel.\r\n                  </p>\r\n                </mat-tab>\r\n                <mat-tab label=\"Second\">\r\n                  <br>\r\n                    <b>Profile Content</b>\r\n                    <p>\r\n                      Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit\r\n                      mediocritatem an. Pri ut tation electram moderatius.\r\n                      Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent\r\n                      aliquid\r\n                      pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere\r\n                      gubergren\r\n                      sadipscing mel.\r\n                    </p>\r\n                  </mat-tab>\r\n                  <mat-tab label=\"Third\">\r\n                    <br>\r\n                      <b>Message Content</b>\r\n                      <p>\r\n                        Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit\r\n                        mediocritatem an. Pri ut tation electram moderatius.\r\n                        Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent\r\n                        aliquid\r\n                        pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere\r\n                        gubergren\r\n                        sadipscing mel.\r\n                      </p>\r\n                    </mat-tab>\r\n                  </mat-tab-group>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row\">\r\n            <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n              <div class=\"card\">\r\n                <div class=\"header\">\r\n                  <h2>Tab With Icons</h2>\r\n                </div>\r\n                <div class=\"body\">\r\n                  <mat-tab-group>\r\n                    <mat-tab>\r\n                      <ng-template mat-tab-label>\r\n                        <mat-icon class=\"example-tab-icon msr-2\">thumb_up</mat-icon>\r\n                        First\r\n                      </ng-template>\r\n                      <br>\r\n                        <b>Home Content</b>\r\n                        <p>\r\n                          Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit\r\n                          mediocritatem an. Pri ut tation electram moderatius.\r\n                          Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent\r\n                          aliquid\r\n                          pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere\r\n                          gubergren\r\n                          sadipscing mel.\r\n                        </p>\r\n                      </mat-tab>\r\n                      <mat-tab>\r\n                        <ng-template mat-tab-label>\r\n                          <mat-icon class=\"example-tab-icon msr-2\">thumb_up</mat-icon>\r\n                          Second\r\n                        </ng-template>\r\n                        <br>\r\n                          <b>Profile Content</b>\r\n                          <p>\r\n                            Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit\r\n                            mediocritatem an. Pri ut tation electram moderatius.\r\n                            Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent\r\n                            aliquid\r\n                            pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere\r\n                            gubergren\r\n                            sadipscing mel.\r\n                          </p>\r\n                        </mat-tab>\r\n                        <mat-tab>\r\n                          <ng-template mat-tab-label>\r\n                            <mat-icon class=\"example-tab-icon msr-2\">thumb_up</mat-icon>\r\n                            Third\r\n                          </ng-template>\r\n                          <br>\r\n                            <b>Message Content</b>\r\n                            <p>\r\n                              Lorem ipsum dolor sit amet, ut duo atqui exerci dicunt, ius impedit\r\n                              mediocritatem an. Pri ut tation electram moderatius.\r\n                              Per te suavitate democritum. Duis nemore probatus ne quo, ad liber essent\r\n                              aliquid\r\n                              pro. Et eos nusquam accumsan, vide mentitum fabellas ne est, eu munere\r\n                              gubergren\r\n                              sadipscing mel.\r\n                            </p>\r\n                          </mat-tab>\r\n                        </mat-tab-group>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                  <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n                    <div class=\"card\">\r\n                      <div class=\"header\">\r\n                        <h2>Tab group with dynamic height based on tab contents</h2>\r\n                      </div>\r\n                      <div class=\"body\">\r\n                        <mat-tab-group dynamicHeight>\r\n                          <mat-tab label=\"Short tab\">\r\n                            <div class=\"example-small-box mat-elevation-z4\">\r\n                              Small content\r\n                            </div>\r\n                          </mat-tab>\r\n                          <mat-tab label=\"Long tab\">\r\n                            <div class=\"example-large-box mat-elevation-z4\">\r\n                              Large content\r\n                            </div>\r\n                          </mat-tab>\r\n                        </mat-tab-group>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                  <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n                    <div class=\"card\">\r\n                      <div class=\"header\">\r\n                        <h2>Tab group with dynamically changing tabs</h2>\r\n                      </div>\r\n                      <div class=\"body\">\r\n                        <mat-form-field>\r\n                          <mat-label>Selected tab index</mat-label>\r\n                          <input matInput type=\"number\" [formControl]=\"selected\">\r\n                          </mat-form-field>\r\n                          <div class=\"mb-3\">\r\n                            <button mat-raised-button class=\"example-add-tab-button msr-2\"\r\n                              (click)=\"addTab(selectAfterAdding.checked)\">\r\n                              Add new tab\r\n                            </button>\r\n                            <mat-checkbox #selectAfterAdding> Select tab after adding </mat-checkbox>\r\n                          </div>\r\n                          <mat-tab-group [selectedIndex]=\"selected.value\" (selectedIndexChange)=\"selected.setValue($event)\">\r\n                            @for (tab of tabs; track tab; let index = $index) {\r\n                              <mat-tab [label]=\"tab\">\r\n                                <br>\r\n                                  Contents for {{tab}} tab\r\n                                  <button mat-raised-button class=\"example-delete-tab-button\" [disabled]=\"tabs.length === 1\"\r\n                                    (click)=\"removeTab(index)\">\r\n                                    Delete Tab\r\n                                  </button>\r\n                                </mat-tab>\r\n                              }\r\n                            </mat-tab-group>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"row\">\r\n                      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n                        <div class=\"card\">\r\n                          <div class=\"header\">\r\n                            <h2>Tab group with the headers on the bottom\r\n                            </h2>\r\n                          </div>\r\n                          <div class=\"body\">\r\n                            <mat-tab-group headerPosition=\"below\">\r\n                              <mat-tab label=\"First\"> Content 1 </mat-tab>\r\n                              <mat-tab label=\"Second\"> Content 2 </mat-tab>\r\n                              <mat-tab label=\"Third\"> Content 3 </mat-tab>\r\n                            </mat-tab-group>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"row\">\r\n                      <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n                        <div class=\"card\">\r\n                          <div class=\"header\">\r\n                            <h2>Tab group with stretched labels\r\n                            </h2>\r\n                          </div>\r\n                          <div class=\"body\">\r\n                            <mat-tab-group mat-stretch-tabs class=\"example-stretched-tabs mat-elevation-z4\">\r\n                              <mat-tab label=\"First\"> Content 1 </mat-tab>\r\n                              <mat-tab label=\"Second\"> Content 2 </mat-tab>\r\n                              <mat-tab label=\"Third\"> Content 3 </mat-tab>\r\n                            </mat-tab-group>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </section>"], "mappings": "AACA,SACEA,kBAAkB,EAClBC,WAAW,EACXC,mBAAmB,QACd,gBAAgB;AACvB,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,mBAAmB,QAAQ,oDAAoD;;;;;;;;;;;ICyDhEC,EAAA,CAAAC,cAAA,mBAAyC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5DH,EAAA,CAAAE,MAAA,cACF;;;;;IAeIF,EAAA,CAAAC,cAAA,mBAAyC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5DH,EAAA,CAAAE,MAAA,eACF;;;;;IAeIF,EAAA,CAAAC,cAAA,mBAAyC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5DH,EAAA,CAAAE,MAAA,cACF;;;;;;IA6DIF,EAAA,CAAAC,cAAA,kBAAuB;IACrBD,EAAA,CAAAI,SAAA,SAAI;IACFJ,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAC6B;IAA3BD,EAAA,CAAAK,UAAA,mBAAAC,sDAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,MAAA;MAAA,MAAAC,OAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,OAAA,CAAAG,SAAA,CAAAL,QAAA,CAAgB;IAAA,EAAC;IAC1BV,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IANJH,EAAA,CAAAgB,UAAA,UAAAC,MAAA,CAAa;IAElBjB,EAAA,CAAAkB,SAAA,GACA;IADAlB,EAAA,CAAAmB,kBAAA,mBAAAF,MAAA,UACA;IAA4DjB,EAAA,CAAAkB,SAAA,EAA8B;IAA9BlB,EAAA,CAAAgB,UAAA,aAAAI,MAAA,CAAAC,IAAA,CAAAC,MAAA,OAA8B;;;;AD3I5H,OAAM,MAAOC,aAAa;EAjB1BC,YAAA;IAkBE,KAAAH,IAAI,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;IACnC,KAAAI,QAAQ,GAAG,IAAInC,kBAAkB,CAAC,CAAC,CAAC;;EACpCoC,MAAMA,CAACC,iBAA0B;IAC/B,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,KAAK,CAAC;IACrB,IAAID,iBAAiB,EAAE;MACrB,IAAI,CAACF,QAAQ,CAACI,QAAQ,CAAC,IAAI,CAACR,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;;EAEhD;EACAP,SAASA,CAACe,KAAa;IACrB,IAAI,CAACT,IAAI,CAACU,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EAC5B;EAAC,QAAAE,CAAA,G;qBAXUT,aAAa;EAAA;EAAA,QAAAU,EAAA,G;UAAbV,aAAa;IAAAW,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAApC,EAAA,CAAAqC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QC9B1B3C,EAAA,CAAAC,cAAA,iBAAyB;QAInBD,EAAA,CAAAI,SAAA,wBAAiG;QACnGJ,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAAiB;QAILD,EAAA,CAAAE,MAAA,yBAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE7BH,EAAA,CAAAC,cAAA,cAAkB;QAGZD,EAAA,CAAAI,SAAA,UAAI;QACFJ,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACnBH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,qTAOF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAC,cAAA,mBAAwB;QACtBD,EAAA,CAAAI,SAAA,UAAI;QACFJ,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACtBH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,qTAOF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAC,cAAA,mBAAuB;QACrBD,EAAA,CAAAI,SAAA,UAAI;QACFJ,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACtBH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,qTAOF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAOhBH,EAAA,CAAAC,cAAA,cAAiB;QAILD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEzBH,EAAA,CAAAC,cAAA,cAAkB;QAGZD,EAAA,CAAA6C,UAAA,KAAAC,qCAAA,0BAGc;QACd9C,EAAA,CAAAI,SAAA,UAAI;QACFJ,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACnBH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,qTAOF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAC,cAAA,eAAS;QACPD,EAAA,CAAA6C,UAAA,KAAAE,qCAAA,0BAGc;QACd/C,EAAA,CAAAI,SAAA,UAAI;QACFJ,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACtBH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,qTAOF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAC,cAAA,eAAS;QACPD,EAAA,CAAA6C,UAAA,KAAAG,qCAAA,0BAGc;QACdhD,EAAA,CAAAI,SAAA,UAAI;QACFJ,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACtBH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,qTAOF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAOhBH,EAAA,CAAAC,cAAA,cAAiB;QAILD,EAAA,CAAAE,MAAA,2DAAmD;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE9DH,EAAA,CAAAC,cAAA,cAAkB;QAIVD,EAAA,CAAAE,MAAA,uBACF;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAC,cAAA,mBAA0B;QAEtBD,EAAA,CAAAE,MAAA,uBACF;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAOlBH,EAAA,CAAAC,cAAA,cAAiB;QAILD,EAAA,CAAAE,MAAA,gDAAwC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEnDH,EAAA,CAAAC,cAAA,cAAkB;QAEHD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAY;QACzCH,EAAA,CAAAI,SAAA,iBAAuD;QACvDJ,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAC,cAAA,eAAkB;QAEdD,EAAA,CAAAK,UAAA,mBAAA4C,gDAAA;UAAAjD,EAAA,CAAAQ,aAAA,CAAA0C,IAAA;UAAA,MAAAC,GAAA,GAAAnD,EAAA,CAAAoD,WAAA;UAAA,OAASpD,EAAA,CAAAc,WAAA,CAAA8B,GAAA,CAAAlB,MAAA,CAAAyB,GAAA,CAAAE,OAAA,CAAiC;QAAA,EAAC;QAC3CrD,EAAA,CAAAE,MAAA,qBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,8BAAiC;QAACD,EAAA,CAAAE,MAAA,iCAAwB;QAAAF,EAAA,CAAAG,YAAA,EAAe;QAE3EH,EAAA,CAAAC,cAAA,yBAAkG;QAAlDD,EAAA,CAAAK,UAAA,iCAAAiD,qEAAAC,MAAA;UAAA,OAAuBX,GAAA,CAAAnB,QAAA,CAAAI,QAAA,CAAA0B,MAAA,CAAyB;QAAA,EAAC;QAC/FvD,EAAA,CAAAwD,gBAAA,KAAAC,6BAAA,uBAAAzD,EAAA,CAAA0D,yBAAA,CASG;QACH1D,EAAA,CAAAG,YAAA,EAAgB;QAKxBH,EAAA,CAAAC,cAAA,cAAiB;QAILD,EAAA,CAAAE,MAAA,iDACJ;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEPH,EAAA,CAAAC,cAAA,cAAkB;QAEUD,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAU;QAC5CH,EAAA,CAAAC,cAAA,oBAAwB;QAACD,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAU;QAC7CH,EAAA,CAAAC,cAAA,oBAAuB;QAACD,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAU;QAMtDH,EAAA,CAAAC,cAAA,eAAiB;QAILD,EAAA,CAAAE,MAAA,yCACJ;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEPH,EAAA,CAAAC,cAAA,eAAkB;QAEUD,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAU;QAC5CH,EAAA,CAAAC,cAAA,oBAAwB;QAACD,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAU;QAC7CH,EAAA,CAAAC,cAAA,oBAAuB;QAACD,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAU;;;QA5MpDH,EAAA,CAAAkB,SAAA,GAAgB;QAAhBlB,EAAA,CAAAgB,UAAA,iBAAgB,UAAAhB,EAAA,CAAA2D,eAAA,IAAAC,GAAA;QAuJkB5D,EAAA,CAAAkB,SAAA,IAAwB;QAAxBlB,EAAA,CAAAgB,UAAA,gBAAA4B,GAAA,CAAAnB,QAAA,CAAwB;QASvCzB,EAAA,CAAAkB,SAAA,GAAgC;QAAhClB,EAAA,CAAAgB,UAAA,kBAAA4B,GAAA,CAAAnB,QAAA,CAAAoC,KAAA,CAAgC;QAC7C7D,EAAA,CAAAkB,SAAA,EASG;QATHlB,EAAA,CAAA8D,UAAA,CAAAlB,GAAA,CAAAvB,IAAA,CASG;;;mBD3J3BtB,mBAAmB,EACnBD,aAAa,EAAAiE,EAAA,CAAAC,WAAA,EAAAD,EAAA,CAAAE,MAAA,EAAAF,EAAA,CAAAG,WAAA,EACbrE,aAAa,EAAAsE,EAAA,CAAAC,OAAA,EACbxE,kBAAkB,EAAAyE,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAClB5E,cAAc,EAAA6E,EAAA,CAAAC,QAAA,EACdlF,WAAW,EAAAmF,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EACXrF,mBAAmB,EAAAkF,EAAA,CAAAI,oBAAA,EACnBpF,eAAe,EAAAqF,EAAA,CAAAC,SAAA,EACfvF,iBAAiB,EAAAwF,EAAA,CAAAC,WAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}