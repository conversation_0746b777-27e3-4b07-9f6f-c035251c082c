{"ast": null, "code": "export const ROUTES = [{\n  path: '',\n  title: 'MENUITEMS.MAIN.TEXT',\n  iconType: '',\n  icon: '',\n  class: '',\n  groupTitle: true,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n},\n// Admin Modules\n{\n  path: '',\n  title: 'MENUITEMS.DASHBOARD.TEXT',\n  iconType: 'feather',\n  icon: 'monitor',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/dashboard/main',\n    title: 'MENUITEMS.DASHBOARD.LIST.DASHBOARD1',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/dashboard/dashboard2',\n    title: 'MENUITEMS.DASHBOARD.LIST.DASHBOARD2',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n},\n// produit Modules\n{\n  path: '',\n  title: 'Gestion Produits',\n  iconType: 'feather',\n  icon: 'monitor',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: './admin/gestion_Produit/produit/allProduits',\n    title: 'List Produits',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: './admin/gestion_Produit/produit/',\n    title: 'List Produits',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.PROJECTS.TEXT',\n  iconType: 'feather',\n  icon: 'book',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/projects/allProjects',\n    title: 'MENUITEMS.PROJECTS.LIST.ALL-PROJECTS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/projects/addProject',\n    title: 'MENUITEMS.PROJECTS.LIST.ADD-PROJECT',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/projects/estimates',\n    title: 'MENUITEMS.PROJECTS.LIST.ESTIMATES',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/projects/projectDetails',\n    title: 'MENUITEMS.PROJECTS.LIST.PROJECT-DETAILS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.EMPLOYEES.TEXT',\n  iconType: 'feather',\n  icon: 'users',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/employees/allEmployees',\n    title: 'MENUITEMS.EMPLOYEES.LIST.ALL-EMPLOYEE',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/employees/add-employee',\n    title: 'MENUITEMS.EMPLOYEES.LIST.ADD-EMPLOYEE',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/employees/edit-employee',\n    title: 'MENUITEMS.EMPLOYEES.LIST.EDIT-EMPLOYEE',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/employees/employee-profile',\n    title: 'MENUITEMS.EMPLOYEES.LIST.PROFILE',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.LEAVES.TEXT',\n  iconType: 'feather',\n  icon: 'trello',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/leaves/leave-requests',\n    title: 'MENUITEMS.LEAVES.LIST.LEAVE-REQUESTS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }, {\n    path: '/admin/leaves/leave-balance',\n    title: 'MENUITEMS.LEAVES.LIST.LEAVE-BALANCE',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }, {\n    path: '/admin/leaves/leave-types',\n    title: 'MENUITEMS.LEAVES.LIST.LEAVE-TYPES',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.HOLIDAYS.TEXT',\n  iconType: 'feather',\n  icon: 'coffee',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/holidays/all-holidays',\n    title: 'MENUITEMS.HOLIDAYS.LIST.ALL-HOLIDAYS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }, {\n    path: '/admin/holidays/add-holiday',\n    title: 'MENUITEMS.HOLIDAYS.LIST.ADD-HOLIDAY',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }, {\n    path: '/admin/holidays/edit-holiday',\n    title: 'MENUITEMS.HOLIDAYS.LIST.EDIT-HOLIDAY',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.ATTENDANCE.TEXT',\n  iconType: 'feather',\n  icon: 'edit',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/attendance/today',\n    title: 'MENUITEMS.ATTENDANCE.LIST.TODAY',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }, {\n    path: '/admin/attendance/employee',\n    title: 'MENUITEMS.ATTENDANCE.LIST.EMPLOYEE',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }, {\n    path: '/admin/attendance/attendance-sheet',\n    title: 'MENUITEMS.ATTENDANCE.LIST.SHEET',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: ['All'],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.CLIENTS.TEXT',\n  iconType: 'feather',\n  icon: 'user',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/clients/all-clients',\n    title: 'MENUITEMS.CLIENTS.LIST.ALL-CLIENTS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/clients/add-client',\n    title: 'MENUITEMS.CLIENTS.LIST.ADD-CLIENT',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/clients/edit-client',\n    title: 'MENUITEMS.CLIENTS.LIST.EDIT-CLIENT',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.ACCOUNTS.TEXT',\n  iconType: 'feather',\n  icon: 'book-open',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/accounts/all-payment',\n    title: 'MENUITEMS.ACCOUNTS.LIST.ALL-PAYMENTS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/accounts/add-payment',\n    title: 'MENUITEMS.ACCOUNTS.LIST.ADD-PAYMENT',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/accounts/invoice',\n    title: 'MENUITEMS.ACCOUNTS.LIST.INVOICE',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.PAYROLL.TEXT',\n  iconType: 'feather',\n  icon: 'clipboard',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/payroll/employee-salary',\n    title: 'MENUITEMS.PAYROLL.LIST.EMPLOYEE_SALARY',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/payroll/payslip',\n    title: 'MENUITEMS.PAYROLL.LIST.PAYSLIP',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '/admin/leads',\n  title: 'MENUITEMS.LEADERS.TEXT',\n  iconType: 'feather',\n  icon: 'users',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '',\n  title: 'MENUITEMS.JOBS.TEXT',\n  iconType: 'feather',\n  icon: 'award',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/jobs/jobs-list',\n    title: 'MENUITEMS.JOBS.LIST.JOBS_LIST',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/jobs/resumes',\n    title: 'MENUITEMS.JOBS.LIST.RESUMES',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/jobs/candidates',\n    title: 'MENUITEMS.JOBS.LIST.CANDIDATES',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/jobs/shortlist',\n    title: 'MENUITEMS.JOBS.LIST.SHORTLIST_CANDIDATES',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.REPORTS.TEXT',\n  iconType: 'feather',\n  icon: 'file-text',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/admin/reports/leave-report',\n    title: 'MENUITEMS.REPORTS.LIST.LEAVE_REPORT',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/admin/reports/expense-report',\n    title: 'MENUITEMS.REPORTS.LIST.EXPENSE_REPORT',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n},\n// Employee Modules\n{\n  path: '/employee/dashboard',\n  title: 'MENUITEMS.EMPLOYEE.DASHBOARD',\n  iconType: 'feather',\n  icon: 'airplay',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/employee/attendance',\n  title: 'MENUITEMS.EMPLOYEE.ATTENDANCE',\n  iconType: 'feather',\n  icon: 'edit',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/employee/myleaves',\n  title: 'MENUITEMS.EMPLOYEE.MY-LEAVES',\n  iconType: 'feather',\n  icon: 'file-text',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/employee/myteam',\n  title: 'MENUITEMS.EMPLOYEE.MYTEAM',\n  iconType: 'feather',\n  icon: 'users',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/employee/myprojects',\n  title: 'MENUITEMS.EMPLOYEE.MYPROJECTS',\n  iconType: 'feather',\n  icon: 'database',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/employee/mytasks',\n  title: 'MENUITEMS.EMPLOYEE.MYTASKS',\n  iconType: 'feather',\n  icon: 'command',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/employee/settings',\n  title: 'MENUITEMS.EMPLOYEE.SETTINGS',\n  iconType: 'feather',\n  icon: 'settings',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/employee/chat',\n  title: 'MENUITEMS.EMPLOYEE.CHAT',\n  iconType: 'feather',\n  icon: 'message-square',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n},\n// Client Modules\n{\n  path: '/client/dashboard',\n  title: 'MENUITEMS.CLIENT.DASHBOARD',\n  iconType: 'feather',\n  icon: 'airplay',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '',\n  title: 'MENUITEMS.CLIENT.PROJECTS.TEXT',\n  iconType: 'feather',\n  icon: 'book',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/client/projects/myProjects',\n    title: 'MENUITEMS.CLIENT.PROJECTS.LIST.MY-PROJECTS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/client/projects/projectDetails',\n    title: 'MENUITEMS.CLIENT.PROJECTS.LIST.PROJECT-DETAILS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'MENUITEMS.CLIENT.SUPPORTS.TEXT',\n  iconType: 'feather',\n  icon: 'slack',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/client/supports/tickets',\n    title: 'MENUITEMS.CLIENT.SUPPORTS.LIST.TICKETS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/client/supports/ticketDetails',\n    title: 'MENUITEMS.CLIENT.SUPPORTS.LIST.TICKET-DETAILS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '/client/billing',\n  title: 'MENUITEMS.CLIENT.BILLING',\n  iconType: 'feather',\n  icon: 'file-text',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/client/chat',\n  title: 'MENUITEMS.CLIENT.CHAT',\n  iconType: 'feather',\n  icon: 'message-circle',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '/client/settings',\n  title: 'MENUITEMS.CLIENT.SETTINGS',\n  iconType: 'feather',\n  icon: 'settings',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n},\n// Common Modules\n{\n  path: '',\n  title: 'Apps',\n  iconType: '',\n  icon: '',\n  class: '',\n  groupTitle: true,\n  badge: '',\n  badgeClass: '',\n  role: ['All', 'All'],\n  submenu: []\n}, {\n  path: 'calendar',\n  title: 'Calendar',\n  iconType: 'feather',\n  icon: 'calendar',\n  class: '',\n  groupTitle: false,\n  badge: 'New',\n  badgeClass: 'badge bg-blue sidebar-badge float-end',\n  role: ['All', 'All'],\n  submenu: []\n}, {\n  path: 'task',\n  title: 'Task',\n  iconType: 'feather',\n  icon: 'check-circle',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: 'contacts',\n  title: 'Contacts',\n  iconType: 'feather',\n  icon: 'user-plus',\n  class: '',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All', 'All'],\n  submenu: []\n}, {\n  path: '',\n  title: 'Email',\n  iconType: 'feather',\n  icon: 'mail',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All', 'All'],\n  submenu: [{\n    path: '/email/inbox',\n    title: 'Inbox',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/email/compose',\n    title: 'Compose',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/email/read-mail',\n    title: 'Read Email',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'More Apps',\n  iconType: 'feather',\n  icon: 'star',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '4',\n  badgeClass: 'badge bg-orange sidebar-badge float-end',\n  role: ['All'],\n  submenu: [{\n    path: '/apps/chat',\n    title: 'Chat',\n    iconType: 'feather',\n    icon: 'chat',\n    class: '',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/apps/dragdrop',\n    title: 'Drag & Drop',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/apps/contact-grid',\n    title: 'Contact Grid',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/apps/support',\n    title: 'Support',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Widgets',\n  iconType: 'feather',\n  icon: 'gift',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/widget/chart-widget',\n    title: 'Chart Widget',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/widget/data-widget',\n    title: 'Data Widget',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Components',\n  iconType: '',\n  icon: '',\n  class: '',\n  groupTitle: true,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: []\n}, {\n  path: '',\n  title: 'User Interface (UI)',\n  iconType: 'feather',\n  icon: 'copy',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/ui/alerts',\n    title: 'Alerts',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/badges',\n    title: 'Badges',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/chips',\n    title: 'Chips',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/modal',\n    title: 'Modal',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/buttons',\n    title: 'Buttons',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/expansion-panel',\n    title: 'Expansion Panel',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/bottom-sheet',\n    title: 'Bottom Sheet',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/dialogs',\n    title: 'Dialogs',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/cards',\n    title: 'Cards',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/labels',\n    title: 'Labels',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/list-group',\n    title: 'List Group',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/snackbar',\n    title: 'Snackbar',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/preloaders',\n    title: 'Preloaders',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/progressbars',\n    title: 'Progress Bars',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/tabs',\n    title: 'Tabs',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/typography',\n    title: 'Typography',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/ui/helper-classes',\n    title: 'Helper Classes',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Forms',\n  iconType: 'feather',\n  icon: 'layout',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/forms/form-controls',\n    title: 'Form Controls',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/forms/advance-controls',\n    title: 'Advanced Controls',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/forms/form-example',\n    title: 'Form Examples',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/forms/form-validation',\n    title: 'Form Validation',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/forms/wizard',\n    title: 'Form Wizard',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/forms/editors',\n    title: 'Editors',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Tables',\n  iconType: 'feather',\n  icon: 'grid',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/tables/basic-tables',\n    title: 'Basic Tables',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/tables/material-tables',\n    title: 'Material Tables',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/tables/ngx-datatable',\n    title: 'ngx-datatable',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Charts',\n  iconType: 'feather',\n  icon: 'pie-chart',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '7',\n  badgeClass: 'badge bg-green sidebar-badge float-end',\n  role: ['All'],\n  submenu: [{\n    path: '/charts/echart',\n    title: 'Echart',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/charts/apex',\n    title: 'Apex',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/charts/chartjs',\n    title: 'ChartJS',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/charts/ngx-charts',\n    title: 'Ngx-Charts',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/charts/gauge',\n    title: 'Gauge',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Timeline',\n  iconType: 'feather',\n  icon: 'git-merge',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/timeline/timeline1',\n    title: 'Timeline 1',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/timeline/timeline2',\n    title: 'Timeline 2',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Icons',\n  iconType: 'feather',\n  icon: 'feather',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/icons/material',\n    title: 'Material Icons',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/icons/font-awesome',\n    title: 'Font Awesome',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Authentication',\n  iconType: 'feather',\n  icon: 'user-check',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/authentication/signin',\n    title: 'Sign In',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/authentication/signup',\n    title: 'Sign Up',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/authentication/forgot-password',\n    title: 'Forgot Password',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/authentication/locked',\n    title: 'Locked',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/authentication/page404',\n    title: '404 - Not Found',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/authentication/page500',\n    title: '500 - Server Error',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Extra Pages',\n  iconType: 'feather',\n  icon: 'anchor',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/extra-pages/profile',\n    title: 'Profile',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/extra-pages/pricing',\n    title: 'Pricing',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/extra-pages/invoice',\n    title: 'Invoice',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/extra-pages/faqs',\n    title: 'Faqs',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/extra-pages/blank',\n    title: 'Blank Page',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Maps',\n  iconType: 'feather',\n  icon: 'map-pin',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/maps/google',\n    title: 'Google Map',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}, {\n  path: '',\n  title: 'Multi level Menu',\n  iconType: 'feather',\n  icon: 'chevrons-down',\n  class: 'menu-toggle',\n  groupTitle: false,\n  badge: '',\n  badgeClass: '',\n  role: ['All'],\n  submenu: [{\n    path: '/multilevel/first1',\n    title: 'First',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }, {\n    path: '/',\n    title: 'Second',\n    iconType: '',\n    icon: '',\n    class: 'ml-sub-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: [{\n      path: '/multilevel/secondlevel/second1',\n      title: 'Second 1',\n      iconType: '',\n      icon: '',\n      class: 'ml-menu2',\n      groupTitle: false,\n      badge: '',\n      badgeClass: '',\n      role: [''],\n      submenu: []\n    }, {\n      path: '/',\n      title: 'Second 2',\n      iconType: '',\n      icon: '',\n      class: 'ml-sub-menu2',\n      groupTitle: false,\n      badge: '',\n      badgeClass: '',\n      role: [''],\n      submenu: [{\n        path: '/multilevel/thirdlevel/third1',\n        title: 'third 1',\n        iconType: '',\n        icon: '',\n        class: 'ml-menu3',\n        groupTitle: false,\n        badge: '',\n        badgeClass: '',\n        role: [''],\n        submenu: []\n      }]\n    }]\n  }, {\n    path: '/multilevel/first3',\n    title: 'Third',\n    iconType: '',\n    icon: '',\n    class: 'ml-menu',\n    groupTitle: false,\n    badge: '',\n    badgeClass: '',\n    role: [''],\n    submenu: []\n  }]\n}];", "map": {"version": 3, "names": ["ROUTES", "path", "title", "iconType", "icon", "class", "groupTitle", "badge", "badgeClass", "role", "submenu"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\layout\\sidebar\\sidebar-items.ts"], "sourcesContent": ["import { RouteInfo } from './sidebar.metadata';\r\nexport const ROUTES: RouteInfo[] = [\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.MAIN.TEXT',\r\n    iconType: '',\r\n    icon: '',\r\n    class: '',\r\n    groupTitle: true,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n\r\n  // Admin Modules\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.DASHBOARD.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'monitor',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/dashboard/main',\r\n        title: 'MENUITEMS.DASHBOARD.LIST.DASHBOARD1',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/dashboard/dashboard2',\r\n        title: 'MENUITEMS.DASHBOARD.LIST.DASHBOARD2',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n\r\n  // produit Modules\r\n  {\r\n    path: '',\r\n    title: 'Gestion Produits',\r\n    iconType: 'feather',\r\n    icon: 'monitor',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: './admin/gestion_Produit/produit/allProduits',\r\n        title: 'List Produits',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: './admin/gestion_Produit/produit/',\r\n        title: 'List Produits',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.PROJECTS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'book',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/projects/allProjects',\r\n        title: 'MENUITEMS.PROJECTS.LIST.ALL-PROJECTS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/projects/addProject',\r\n        title: 'MENUITEMS.PROJECTS.LIST.ADD-PROJECT',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/projects/estimates',\r\n        title: 'MENUITEMS.PROJECTS.LIST.ESTIMATES',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/projects/projectDetails',\r\n        title: 'MENUITEMS.PROJECTS.LIST.PROJECT-DETAILS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.EMPLOYEES.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'users',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/employees/allEmployees',\r\n        title: 'MENUITEMS.EMPLOYEES.LIST.ALL-EMPLOYEE',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/employees/add-employee',\r\n        title: 'MENUITEMS.EMPLOYEES.LIST.ADD-EMPLOYEE',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/employees/edit-employee',\r\n        title: 'MENUITEMS.EMPLOYEES.LIST.EDIT-EMPLOYEE',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/employees/employee-profile',\r\n        title: 'MENUITEMS.EMPLOYEES.LIST.PROFILE',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.LEAVES.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'trello',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/leaves/leave-requests',\r\n        title: 'MENUITEMS.LEAVES.LIST.LEAVE-REQUESTS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/leaves/leave-balance',\r\n        title: 'MENUITEMS.LEAVES.LIST.LEAVE-BALANCE',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/leaves/leave-types',\r\n        title: 'MENUITEMS.LEAVES.LIST.LEAVE-TYPES',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.HOLIDAYS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'coffee',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/holidays/all-holidays',\r\n        title: 'MENUITEMS.HOLIDAYS.LIST.ALL-HOLIDAYS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/holidays/add-holiday',\r\n        title: 'MENUITEMS.HOLIDAYS.LIST.ADD-HOLIDAY',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/holidays/edit-holiday',\r\n        title: 'MENUITEMS.HOLIDAYS.LIST.EDIT-HOLIDAY',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.ATTENDANCE.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'edit',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/attendance/today',\r\n        title: 'MENUITEMS.ATTENDANCE.LIST.TODAY',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/attendance/employee',\r\n        title: 'MENUITEMS.ATTENDANCE.LIST.EMPLOYEE',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/attendance/attendance-sheet',\r\n        title: 'MENUITEMS.ATTENDANCE.LIST.SHEET',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: ['All'],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.CLIENTS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'user',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/clients/all-clients',\r\n        title: 'MENUITEMS.CLIENTS.LIST.ALL-CLIENTS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/clients/add-client',\r\n        title: 'MENUITEMS.CLIENTS.LIST.ADD-CLIENT',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/clients/edit-client',\r\n        title: 'MENUITEMS.CLIENTS.LIST.EDIT-CLIENT',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.ACCOUNTS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'book-open',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/accounts/all-payment',\r\n        title: 'MENUITEMS.ACCOUNTS.LIST.ALL-PAYMENTS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/accounts/add-payment',\r\n        title: 'MENUITEMS.ACCOUNTS.LIST.ADD-PAYMENT',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/accounts/invoice',\r\n        title: 'MENUITEMS.ACCOUNTS.LIST.INVOICE',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.PAYROLL.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'clipboard',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/payroll/employee-salary',\r\n        title: 'MENUITEMS.PAYROLL.LIST.EMPLOYEE_SALARY',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/payroll/payslip',\r\n        title: 'MENUITEMS.PAYROLL.LIST.PAYSLIP',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n\r\n  {\r\n    path: '/admin/leads',\r\n    title: 'MENUITEMS.LEADERS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'users',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.JOBS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'award',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/jobs/jobs-list',\r\n        title: 'MENUITEMS.JOBS.LIST.JOBS_LIST',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/jobs/resumes',\r\n        title: 'MENUITEMS.JOBS.LIST.RESUMES',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/jobs/candidates',\r\n        title: 'MENUITEMS.JOBS.LIST.CANDIDATES',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/jobs/shortlist',\r\n        title: 'MENUITEMS.JOBS.LIST.SHORTLIST_CANDIDATES',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.REPORTS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'file-text',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/admin/reports/leave-report',\r\n        title: 'MENUITEMS.REPORTS.LIST.LEAVE_REPORT',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/admin/reports/expense-report',\r\n        title: 'MENUITEMS.REPORTS.LIST.EXPENSE_REPORT',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n\r\n  // Employee Modules\r\n  {\r\n    path: '/employee/dashboard',\r\n    title: 'MENUITEMS.EMPLOYEE.DASHBOARD',\r\n    iconType: 'feather',\r\n    icon: 'airplay',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/employee/attendance',\r\n    title: 'MENUITEMS.EMPLOYEE.ATTENDANCE',\r\n    iconType: 'feather',\r\n    icon: 'edit',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/employee/myleaves',\r\n    title: 'MENUITEMS.EMPLOYEE.MY-LEAVES',\r\n    iconType: 'feather',\r\n    icon: 'file-text',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/employee/myteam',\r\n    title: 'MENUITEMS.EMPLOYEE.MYTEAM',\r\n    iconType: 'feather',\r\n    icon: 'users',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/employee/myprojects',\r\n    title: 'MENUITEMS.EMPLOYEE.MYPROJECTS',\r\n    iconType: 'feather',\r\n    icon: 'database',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/employee/mytasks',\r\n    title: 'MENUITEMS.EMPLOYEE.MYTASKS',\r\n    iconType: 'feather',\r\n    icon: 'command',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/employee/settings',\r\n    title: 'MENUITEMS.EMPLOYEE.SETTINGS',\r\n    iconType: 'feather',\r\n    icon: 'settings',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/employee/chat',\r\n    title: 'MENUITEMS.EMPLOYEE.CHAT',\r\n    iconType: 'feather',\r\n    icon: 'message-square',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  // Client Modules\r\n  {\r\n    path: '/client/dashboard',\r\n    title: 'MENUITEMS.CLIENT.DASHBOARD',\r\n    iconType: 'feather',\r\n    icon: 'airplay',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.CLIENT.PROJECTS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'book',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/client/projects/myProjects',\r\n        title: 'MENUITEMS.CLIENT.PROJECTS.LIST.MY-PROJECTS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/client/projects/projectDetails',\r\n        title: 'MENUITEMS.CLIENT.PROJECTS.LIST.PROJECT-DETAILS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'MENUITEMS.CLIENT.SUPPORTS.TEXT',\r\n    iconType: 'feather',\r\n    icon: 'slack',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/client/supports/tickets',\r\n        title: 'MENUITEMS.CLIENT.SUPPORTS.LIST.TICKETS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/client/supports/ticketDetails',\r\n        title: 'MENUITEMS.CLIENT.SUPPORTS.LIST.TICKET-DETAILS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n\r\n  {\r\n    path: '/client/billing',\r\n    title: 'MENUITEMS.CLIENT.BILLING',\r\n    iconType: 'feather',\r\n    icon: 'file-text',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/client/chat',\r\n    title: 'MENUITEMS.CLIENT.CHAT',\r\n    iconType: 'feather',\r\n    icon: 'message-circle',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '/client/settings',\r\n    title: 'MENUITEMS.CLIENT.SETTINGS',\r\n    iconType: 'feather',\r\n    icon: 'settings',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n\r\n  // Common Modules\r\n\r\n  {\r\n    path: '',\r\n    title: 'Apps',\r\n    iconType: '',\r\n    icon: '',\r\n    class: '',\r\n    groupTitle: true,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All', 'All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: 'calendar',\r\n    title: 'Calendar',\r\n    iconType: 'feather',\r\n    icon: 'calendar',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: 'New',\r\n    badgeClass: 'badge bg-blue sidebar-badge float-end',\r\n    role: ['All', 'All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: 'task',\r\n    title: 'Task',\r\n    iconType: 'feather',\r\n    icon: 'check-circle',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: 'contacts',\r\n    title: 'Contacts',\r\n    iconType: 'feather',\r\n    icon: 'user-plus',\r\n    class: '',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All', 'All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Email',\r\n    iconType: 'feather',\r\n    icon: 'mail',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All', 'All'],\r\n    submenu: [\r\n      {\r\n        path: '/email/inbox',\r\n        title: 'Inbox',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/email/compose',\r\n        title: 'Compose',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/email/read-mail',\r\n        title: 'Read Email',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'More Apps',\r\n    iconType: 'feather',\r\n    icon: 'star',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '4',\r\n    badgeClass: 'badge bg-orange sidebar-badge float-end',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/apps/chat',\r\n        title: 'Chat',\r\n        iconType: 'feather',\r\n        icon: 'chat',\r\n        class: '',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/apps/dragdrop',\r\n        title: 'Drag & Drop',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/apps/contact-grid',\r\n        title: 'Contact Grid',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/apps/support',\r\n        title: 'Support',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Widgets',\r\n    iconType: 'feather',\r\n    icon: 'gift',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/widget/chart-widget',\r\n        title: 'Chart Widget',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/widget/data-widget',\r\n        title: 'Data Widget',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Components',\r\n    iconType: '',\r\n    icon: '',\r\n    class: '',\r\n    groupTitle: true,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'User Interface (UI)',\r\n    iconType: 'feather',\r\n    icon: 'copy',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/ui/alerts',\r\n        title: 'Alerts',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/badges',\r\n        title: 'Badges',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/chips',\r\n        title: 'Chips',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/modal',\r\n        title: 'Modal',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/buttons',\r\n        title: 'Buttons',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/expansion-panel',\r\n        title: 'Expansion Panel',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/bottom-sheet',\r\n        title: 'Bottom Sheet',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/dialogs',\r\n        title: 'Dialogs',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/cards',\r\n        title: 'Cards',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/labels',\r\n        title: 'Labels',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/list-group',\r\n        title: 'List Group',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/snackbar',\r\n        title: 'Snackbar',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/preloaders',\r\n        title: 'Preloaders',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/progressbars',\r\n        title: 'Progress Bars',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/tabs',\r\n        title: 'Tabs',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/typography',\r\n        title: 'Typography',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/ui/helper-classes',\r\n        title: 'Helper Classes',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Forms',\r\n    iconType: 'feather',\r\n    icon: 'layout',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/forms/form-controls',\r\n        title: 'Form Controls',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/forms/advance-controls',\r\n        title: 'Advanced Controls',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/forms/form-example',\r\n        title: 'Form Examples',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/forms/form-validation',\r\n        title: 'Form Validation',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/forms/wizard',\r\n        title: 'Form Wizard',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/forms/editors',\r\n        title: 'Editors',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Tables',\r\n    iconType: 'feather',\r\n    icon: 'grid',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/tables/basic-tables',\r\n        title: 'Basic Tables',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/tables/material-tables',\r\n        title: 'Material Tables',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/tables/ngx-datatable',\r\n        title: 'ngx-datatable',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Charts',\r\n    iconType: 'feather',\r\n    icon: 'pie-chart',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '7',\r\n    badgeClass: 'badge bg-green sidebar-badge float-end',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/charts/echart',\r\n        title: 'Echart',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/charts/apex',\r\n        title: 'Apex',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/charts/chartjs',\r\n        title: 'ChartJS',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/charts/ngx-charts',\r\n        title: 'Ngx-Charts',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/charts/gauge',\r\n        title: 'Gauge',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Timeline',\r\n    iconType: 'feather',\r\n    icon: 'git-merge',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/timeline/timeline1',\r\n        title: 'Timeline 1',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/timeline/timeline2',\r\n        title: 'Timeline 2',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Icons',\r\n    iconType: 'feather',\r\n    icon: 'feather',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/icons/material',\r\n        title: 'Material Icons',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/icons/font-awesome',\r\n        title: 'Font Awesome',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Authentication',\r\n    iconType: 'feather',\r\n    icon: 'user-check',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/authentication/signin',\r\n        title: 'Sign In',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/authentication/signup',\r\n        title: 'Sign Up',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/authentication/forgot-password',\r\n        title: 'Forgot Password',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/authentication/locked',\r\n        title: 'Locked',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/authentication/page404',\r\n        title: '404 - Not Found',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/authentication/page500',\r\n        title: '500 - Server Error',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Extra Pages',\r\n    iconType: 'feather',\r\n    icon: 'anchor',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/extra-pages/profile',\r\n        title: 'Profile',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/extra-pages/pricing',\r\n        title: 'Pricing',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/extra-pages/invoice',\r\n        title: 'Invoice',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/extra-pages/faqs',\r\n        title: 'Faqs',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/extra-pages/blank',\r\n        title: 'Blank Page',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Maps',\r\n    iconType: 'feather',\r\n    icon: 'map-pin',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/maps/google',\r\n        title: 'Google Map',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: '',\r\n    title: 'Multi level Menu',\r\n    iconType: 'feather',\r\n    icon: 'chevrons-down',\r\n    class: 'menu-toggle',\r\n    groupTitle: false,\r\n    badge: '',\r\n    badgeClass: '',\r\n    role: ['All'],\r\n    submenu: [\r\n      {\r\n        path: '/multilevel/first1',\r\n        title: 'First',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n      {\r\n        path: '/',\r\n        title: 'Second',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-sub-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [\r\n          {\r\n            path: '/multilevel/secondlevel/second1',\r\n            title: 'Second 1',\r\n            iconType: '',\r\n            icon: '',\r\n            class: 'ml-menu2',\r\n            groupTitle: false,\r\n            badge: '',\r\n            badgeClass: '',\r\n            role: [''],\r\n            submenu: [],\r\n          },\r\n          {\r\n            path: '/',\r\n            title: 'Second 2',\r\n            iconType: '',\r\n            icon: '',\r\n            class: 'ml-sub-menu2',\r\n            groupTitle: false,\r\n            badge: '',\r\n            badgeClass: '',\r\n            role: [''],\r\n            submenu: [\r\n              {\r\n                path: '/multilevel/thirdlevel/third1',\r\n                title: 'third 1',\r\n                iconType: '',\r\n                icon: '',\r\n                class: 'ml-menu3',\r\n                groupTitle: false,\r\n                badge: '',\r\n                badgeClass: '',\r\n                role: [''],\r\n                submenu: [],\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        path: '/multilevel/first3',\r\n        title: 'Third',\r\n        iconType: '',\r\n        icon: '',\r\n        class: 'ml-menu',\r\n        groupTitle: false,\r\n        badge: '',\r\n        badgeClass: '',\r\n        role: [''],\r\n        submenu: [],\r\n      },\r\n    ],\r\n  },\r\n];\r\n"], "mappings": "AACA,OAAO,MAAMA,MAAM,GAAgB,CACjC;EACEC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,EAAE;EACZC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,IAAI;EAChBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV;AAED;AACA;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,0BAA0B;EACjCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,6BAA6B;IACnCC,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ;AAED;AACA;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,kBAAkB;EACzBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,6CAA6C;IACnDC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,kCAAkC;IACxCC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EAYD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,yBAAyB;EAChCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,6BAA6B;IACnCC,KAAK,EAAE,sCAAsC;IAC7CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,4BAA4B;IAClCC,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,2BAA2B;IACjCC,KAAK,EAAE,mCAAmC;IAC1CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gCAAgC;IACtCC,KAAK,EAAE,yCAAyC;IAChDC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,0BAA0B;EACjCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,+BAA+B;IACrCC,KAAK,EAAE,uCAAuC;IAC9CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,+BAA+B;IACrCC,KAAK,EAAE,uCAAuC;IAC9CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gCAAgC;IACtCC,KAAK,EAAE,wCAAwC;IAC/CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,mCAAmC;IACzCC,KAAK,EAAE,kCAAkC;IACzCC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,uBAAuB;EAC9BC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,8BAA8B;IACpCC,KAAK,EAAE,sCAAsC;IAC7CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,6BAA6B;IACnCC,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,2BAA2B;IACjCC,KAAK,EAAE,mCAAmC;IAC1CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,yBAAyB;EAChCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,8BAA8B;IACpCC,KAAK,EAAE,sCAAsC;IAC7CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,6BAA6B;IACnCC,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,8BAA8B;IACpCC,KAAK,EAAE,sCAAsC;IAC7CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,2BAA2B;EAClCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE,iCAAiC;IACxCC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,4BAA4B;IAClCC,KAAK,EAAE,oCAAoC;IAC3CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,oCAAoC;IAC1CC,KAAK,EAAE,iCAAiC;IACxCC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,KAAK,CAAC;IACbC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,wBAAwB;EAC/BC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,4BAA4B;IAClCC,KAAK,EAAE,oCAAoC;IAC3CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,2BAA2B;IACjCC,KAAK,EAAE,mCAAmC;IAC1CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,4BAA4B;IAClCC,KAAK,EAAE,oCAAoC;IAC3CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,yBAAyB;EAChCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,6BAA6B;IACnCC,KAAK,EAAE,sCAAsC;IAC7CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,6BAA6B;IACnCC,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE,iCAAiC;IACxCC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,wBAAwB;EAC/BC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,gCAAgC;IACtCC,KAAK,EAAE,wCAAwC;IAC/CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,gCAAgC;IACvCC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EAED;EACET,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,wBAAwB;EAC/BC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE,+BAA+B;IACtCC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,6BAA6B;IACpCC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,gCAAgC;IACvCC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE,0CAA0C;IACjDC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,wBAAwB;EAC/BC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,6BAA6B;IACnCC,KAAK,EAAE,qCAAqC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,+BAA+B;IACrCC,KAAK,EAAE,uCAAuC;IAC9CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ;AAED;AACA;EACET,IAAI,EAAE,qBAAqB;EAC3BC,KAAK,EAAE,8BAA8B;EACrCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,sBAAsB;EAC5BC,KAAK,EAAE,+BAA+B;EACtCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE,8BAA8B;EACrCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,2BAA2B;EAClCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,sBAAsB;EAC5BC,KAAK,EAAE,+BAA+B;EACtCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,4BAA4B;EACnCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE,6BAA6B;EACpCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE,yBAAyB;EAChCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV;AACD;AACA;EACET,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,4BAA4B;EACnCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,gCAAgC;EACvCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,6BAA6B;IACnCC,KAAK,EAAE,4CAA4C;IACnDC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,iCAAiC;IACvCC,KAAK,EAAE,gDAAgD;IACvDC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,gCAAgC;EACvCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,0BAA0B;IAChCC,KAAK,EAAE,wCAAwC;IAC/CC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gCAAgC;IACtCC,KAAK,EAAE,+CAA+C;IACtDC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EAED;EACET,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,0BAA0B;EACjCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,uBAAuB;EAC9BC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,2BAA2B;EAClCC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV;AAED;AAEA;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,EAAE;EACZC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,IAAI;EAChBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACpBC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,UAAU;EACjBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,KAAK;EACZC,UAAU,EAAE,uCAAuC;EACnDC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACpBC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,UAAU;EACjBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACpBC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACpBC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,WAAW;EAClBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,GAAG;EACVC,UAAU,EAAE,yCAAyC;EACrDC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,SAAS;EAChBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,EAAE;EACZC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,IAAI;EAChBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE;CACV,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,GAAG;EACVC,UAAU,EAAE,wCAAwC;EACpDC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,UAAU;EACjBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,iCAAiC;IACvCC,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE,iBAAiB;IACxBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE,oBAAoB;IAC3BC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,kBAAkB;EACzBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,eAAe;EACrBC,KAAK,EAAE,aAAa;EACpBC,UAAU,EAAE,KAAK;EACjBC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,CAAC,KAAK,CAAC;EACbC,OAAO,EAAE,CACP;IACET,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV,EACD;IACET,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE,CACP;MACET,IAAI,EAAE,iCAAiC;MACvCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,OAAO,EAAE;KACV,EACD;MACET,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,cAAc;MACrBC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,OAAO,EAAE,CACP;QACET,IAAI,EAAE,+BAA+B;QACrCC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,UAAU;QACjBC,UAAU,EAAE,KAAK;QACjBC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,EAAE;QACdC,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,OAAO,EAAE;OACV;KAEJ;GAEJ,EACD;IACET,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,CAAC,EAAE,CAAC;IACVC,OAAO,EAAE;GACV;CAEJ,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}