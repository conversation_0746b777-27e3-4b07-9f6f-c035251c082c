{"ast": null, "code": "import { ProduitComponent } from \"./produit/produit.component\";\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\nimport { FormProduitComponent } from \"./form-produit/form-produit.component\";\nimport { ProduitDeleteComponent } from \"./produit-delete/produit-delete.component\";\nexport const PRODUIT_ROUTE = [{\n  path: \"allProduits\",\n  component: ProduitComponent\n}, {\n  path: \"FormProduit\",\n  component: FormProduitComponent\n}, {\n  path: \"deleteProduit\",\n  component: ProduitDeleteComponent\n}, {\n  path: '**',\n  component: Page404Component\n}];", "map": {"version": 3, "names": ["ProduitComponent", "Page404Component", "FormProduitComponent", "ProduitDeleteComponent", "PRODUIT_ROUTE", "path", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit.routes.ts"], "sourcesContent": ["import { Route } from \"@angular/router\";\r\nimport { ProduitComponent } from \"./produit/produit.component\";\r\n\r\nimport { Page404Component } from \"app/authentication/page404/page404.component\";\r\nimport { FormProduitComponent } from \"./form-produit/form-produit.component\";\r\nimport { ProduitDeleteComponent } from \"./produit-delete/produit-delete.component\";\r\n\r\nexport const PRODUIT_ROUTE: Route[] = [\r\n  {\r\n    path: \"allProduits\",\r\n    component: ProduitComponent,\r\n  },\r\n  {\r\n    path: \"FormProduit\",\r\n    component: FormProduitComponent,\r\n  },\r\n  {\r\n    path: \"deleteProduit\",\r\n    component: ProduitDeleteComponent,\r\n  },\r\n  { path: '**', component: Page404Component },\r\n];\r\n\r\n"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,6BAA6B;AAE9D,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAElF,OAAO,MAAMC,aAAa,GAAY,CACpC;EACEC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEH;CACZ,EACD;EAAEE,IAAI,EAAE,IAAI;EAAEC,SAAS,EAAEL;AAAgB,CAAE,CAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}