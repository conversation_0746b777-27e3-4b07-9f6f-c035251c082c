{"ast": null, "code": "import * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, InjectionToken, inject, Injectable, SkipSelf, Directive, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { MatCommonModule } from '@angular/material/core';\nimport { trigger, state, style, transition, group, animate, query, animateChild } from '@angular/animations';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nfunction MatDialogContainer_ng_template_2_Template(rf, ctx) {}\nclass MatDialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Custom class for the overlay pane. */\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    this.hasBackdrop = true;\n    /** Custom class for the backdrop. */\n    this.backdropClass = '';\n    /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n    this.disableClose = false;\n    /** Width of the dialog. */\n    this.width = '';\n    /** Height of the dialog. */\n    this.height = '';\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    this.ariaLabelledBy = null;\n    /** Aria label to assign to the dialog element. */\n    this.ariaLabel = null;\n    /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n    this.ariaModal = true;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the\n     * previously-focused element, after it's closed.\n     */\n    this.restoreFocus = true;\n    /** Whether to wait for the opening animation to finish before trapping focus. */\n    this.delayFocusTrap = true;\n    /**\n     * Whether the dialog should close when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    this.closeOnNavigation = true;\n    // TODO(jelbourn): add configuration for lifecycle hooks, ARIA labelling.\n  }\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nclass MatDialogContainer extends CdkDialogContainer {\n  constructor(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, _animationMode, focusMonitor) {\n    super(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, focusMonitor);\n    this._animationMode = _animationMode;\n    /** Emits when an animation state changes. */\n    this._animationStateChanged = new EventEmitter();\n    /** Whether animations are enabled. */\n    this._animationsEnabled = this._animationMode !== 'NoopAnimations';\n    /** Number of actions projected in the dialog. */\n    this._actionSectionCount = 0;\n    /** Host element of the dialog container component. */\n    this._hostElement = this._elementRef.nativeElement;\n    /** Duration of the dialog open animation. */\n    this._enterAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION : 0;\n    /** Duration of the dialog close animation. */\n    this._exitAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION : 0;\n    /** Current timer for dialog animations. */\n    this._animationTimer = null;\n    /**\n     * Completes the dialog open by clearing potential animation classes, trapping\n     * focus and emitting an opened event.\n     */\n    this._finishDialogOpen = () => {\n      this._clearAnimationClasses();\n      this._openAnimationDone(this._enterAnimationDuration);\n    };\n    /**\n     * Completes the dialog close by clearing potential animation classes, restoring\n     * focus and emitting a closed event.\n     */\n    this._finishDialogClose = () => {\n      this._clearAnimationClasses();\n      this._animationStateChanged.emit({\n        state: 'closed',\n        totalTime: this._exitAnimationDuration\n      });\n    };\n  }\n  _contentAttached() {\n    // Delegate to the original dialog-container initialization (i.e. saving the\n    // previous element, setting up the focus trap and moving focus to the container).\n    super._contentAttached();\n    // Note: Usually we would be able to use the MDC dialog foundation here to handle\n    // the dialog animation for us, but there are a few reasons why we just leverage\n    // their styles and not use the runtime foundation code:\n    //   1. Foundation does not allow us to disable animations.\n    //   2. Foundation contains unnecessary features we don't need and aren't\n    //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n    //   3. Foundation uses unnecessary timers for animations to work around limitations\n    //      in React's `setState` mechanism.\n    //      https://github.com/material-components/material-components-web/pull/3682.\n    this._startOpenAnimation();\n  }\n  /** Starts the dialog open animation if enabled. */\n  _startOpenAnimation() {\n    this._animationStateChanged.emit({\n      state: 'opening',\n      totalTime: this._enterAnimationDuration\n    });\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      // One would expect that the open class is added once the animation finished, but MDC\n      // uses the open class in combination with the opening class to start the animation.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n      this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n    } else {\n      this._hostElement.classList.add(OPEN_CLASS);\n      // Note: We could immediately finish the dialog opening here with noop animations,\n      // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n      // Executing this immediately would mean that `afterOpened` emits synchronously\n      // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n      Promise.resolve().then(() => this._finishDialogOpen());\n    }\n  }\n  /**\n   * Starts the exit animation of the dialog if enabled. This method is\n   * called by the dialog ref.\n   */\n  _startExitAnimation() {\n    this._animationStateChanged.emit({\n      state: 'closing',\n      totalTime: this._exitAnimationDuration\n    });\n    this._hostElement.classList.remove(OPEN_CLASS);\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n      // We need to give the `setProperty` call from above some time to be applied.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n      this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n    } else {\n      // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n      // set up before any user can subscribe to the backdrop click. The subscription triggers\n      // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n      // animation state event if animations are disabled, the overlay would be disposed\n      // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n      // skipped. We work around this by waiting with the dialog close until the next tick when\n      // all subscriptions have been fired as expected. This is not an ideal solution, but\n      // there doesn't seem to be any other good way. Alternatives that have been considered:\n      //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n      //      Also this issue is specific to the MDC implementation where the dialog could\n      //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n      //      and closing always takes at least a tick.\n      //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n      //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n      //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n      // Based on the fact that this is specific to the MDC-based implementation of the dialog\n      // animations, the defer is applied here.\n      Promise.resolve().then(() => this._finishDialogClose());\n    }\n  }\n  /**\n   * Updates the number action sections.\n   * @param delta Increase/decrease in the number of sections.\n   */\n  _updateActionSectionCount(delta) {\n    this._actionSectionCount += delta;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Clears all dialog animation classes. */\n  _clearAnimationClasses() {\n    this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n  }\n  _waitForAnimationToComplete(duration, callback) {\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n    // Note that we want this timer to run inside the NgZone, because we want\n    // the related events like `afterClosed` to be inside the zone as well.\n    this._animationTimer = setTimeout(callback, duration);\n  }\n  /** Runs a callback in `requestAnimationFrame`, if available. */\n  _requestAnimationFrame(callback) {\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame === 'function') {\n        requestAnimationFrame(callback);\n      } else {\n        callback();\n      }\n    });\n  }\n  _captureInitialFocus() {\n    if (!this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Callback for when the open dialog animation has finished. Intended to\n   * be called by sub-classes that use different animation implementations.\n   */\n  _openAnimationDone(totalTime) {\n    if (this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n    this._animationStateChanged.next({\n      state: 'opened',\n      totalTime\n    });\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n  }\n  attachComponentPortal(portal) {\n    // When a component is passed into the dialog, the host element interrupts\n    // the `display:flex` from affecting the dialog title, content, and\n    // actions. To fix this, we make the component host `display: contents` by\n    // marking its host with the `mat-mdc-dialog-component-host` class.\n    //\n    // Note that this problem does not exist when a template ref is used since\n    // the title, contents, and actions are then nested directly under the\n    // dialog surface.\n    const ref = super.attachComponentPortal(portal);\n    ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n    return ref;\n  }\n  static #_ = this.ɵfac = function MatDialogContainer_Factory(t) {\n    return new (t || MatDialogContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(MatDialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n  };\n  static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDialogContainer,\n    selectors: [[\"mat-dialog-container\"]],\n    hostAttrs: [\"tabindex\", \"-1\", 1, \"mat-mdc-dialog-container\", \"mdc-dialog\"],\n    hostVars: 10,\n    hostBindings: function MatDialogContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx._config.id);\n        i0.ɵɵattribute(\"aria-modal\", ctx._config.ariaModal)(\"role\", ctx._config.role)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", !ctx._animationsEnabled)(\"mat-mdc-dialog-container-with-actions\", ctx._actionSectionCount > 0);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    decls: 3,\n    vars: 0,\n    consts: [[1, \"mdc-dialog__container\"], [1, \"mat-mdc-dialog-surface\", \"mdc-dialog__surface\"], [\"cdkPortalOutlet\", \"\"]],\n    template: function MatDialogContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, MatDialogContainer_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵelementEnd()();\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0;transform:scale(0.8)}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--closing .mdc-dialog__surface{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{opacity:1}.mdc-dialog--open .mdc-dialog__surface{transform:none}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{width:100%;height:100%}.mat-mdc-dialog-component-host{display:contents}.mat-mdc-dialog-container{--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition:opacity linear var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container .mdc-dialog__surface{transition:transform var(--mat-dialog-transition-duration, 0ms) 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container,.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__surface{transition:none}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 80vw);min-width:var(--mat-dialog-container-min-width, 0)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, 80vw)}}.mat-mdc-dialog-title{padding:var(--mat-dialog-headline-padding, 0 24px 9px)}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{padding:var(--mat-dialog-actions-padding, 8px);justify-content:var(--mat-dialog-actions-alignment, start)}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      standalone: true,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mat-mdc-dialog-container mdc-dialog',\n        'tabindex': '-1',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[id]': '_config.id',\n        '[attr.role]': '_config.role',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n        '[class._mat-animation-noopable]': '!_animationsEnabled',\n        '[class.mat-mdc-dialog-container-with-actions]': '_actionSectionCount > 0'\n      },\n      template: \"<div class=\\\"mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\",\n      styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0;transform:scale(0.8)}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--closing .mdc-dialog__surface{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{opacity:1}.mdc-dialog--open .mdc-dialog__surface{transform:none}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{width:100%;height:100%}.mat-mdc-dialog-component-host{display:contents}.mat-mdc-dialog-container{--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition:opacity linear var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container .mdc-dialog__surface{transition:transform var(--mat-dialog-transition-duration, 0ms) 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container,.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__surface{transition:none}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 80vw);min-width:var(--mat-dialog-container-min-width, 0)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, 80vw)}}.mat-mdc-dialog-title{padding:var(--mat-dialog-headline-padding, 0 24px 9px)}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{padding:var(--mat-dialog-actions-padding, 8px);justify-content:var(--mat-dialog-actions-alignment, start)}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.FocusTrapFactory\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: MatDialogConfig\n  }, {\n    type: i1.InteractivityChecker\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.OverlayRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: i1.FocusMonitor\n  }], null);\n})();\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n  if (time == null) {\n    return null;\n  }\n  if (typeof time === 'number') {\n    return time;\n  }\n  if (time.endsWith('ms')) {\n    return coerceNumberProperty(time.substring(0, time.length - 2));\n  }\n  if (time.endsWith('s')) {\n    return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n  }\n  if (time === '0') {\n    return 0;\n  }\n  return null; // anything else is invalid.\n}\nvar MatDialogState;\n(function (MatDialogState) {\n  MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n  MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n  MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n})(MatDialogState || (MatDialogState = {}));\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n  constructor(_ref, config, _containerInstance) {\n    this._ref = _ref;\n    this._containerInstance = _containerInstance;\n    /** Subject for notifying the user that the dialog has finished opening. */\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the dialog has started closing. */\n    this._beforeClosed = new Subject();\n    /** Current state of the dialog. */\n    this._state = MatDialogState.OPEN;\n    this.disableClose = config.disableClose;\n    this.id = _ref.id;\n    // Used to target panels specifically tied to dialogs.\n    _ref.addPanelClass('mat-mdc-dialog-panel');\n    // Emit when opening animation completes\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'opened'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    });\n    // Dispose overlay when closing animation is complete\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closed'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n      this._finishDialogClose();\n    });\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n      this._beforeClosed.complete();\n      this._finishDialogClose();\n    });\n    merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n      if (!this.disableClose) {\n        event.preventDefault();\n        _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n  close(dialogResult) {\n    this._result = dialogResult;\n    // Transition the backdrop in parallel to the dialog.\n    this._containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closing'), take(1)).subscribe(event => {\n      this._beforeClosed.next(dialogResult);\n      this._beforeClosed.complete();\n      this._ref.overlayRef.detachBackdrop();\n      // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n      this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n    });\n    this._state = MatDialogState.CLOSING;\n    this._containerInstance._startExitAnimation();\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n  afterClosed() {\n    return this._ref.closed;\n  }\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n  beforeClosed() {\n    return this._beforeClosed;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick() {\n    return this._ref.backdropClick;\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents() {\n    return this._ref.keydownEvents;\n  }\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n  updatePosition(position) {\n    let strategy = this._ref.config.positionStrategy;\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n    this._ref.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this._ref.updateSize(width, height);\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this._ref.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this._ref.removePanelClass(classes);\n    return this;\n  }\n  /** Gets the current state of the dialog's lifecycle. */\n  getState() {\n    return this._state;\n  }\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n  _finishDialogClose() {\n    this._state = MatDialogState.CLOSED;\n    this._ref.close(this._result, {\n      focusOrigin: this._closeInteractionType\n    });\n    this.componentInstance = null;\n  }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n  ref._closeInteractionType = interactionType;\n  return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n// Counter for unique dialog ids.\nlet uniqueId = 0;\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog {\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  constructor(_overlay, injector,\n  /**\n   * @deprecated `_location` parameter to be removed.\n   * @breaking-change 10.0.0\n   */\n  location, _defaultOptions, _scrollStrategy, _parentDialog,\n  /**\n   * @deprecated No longer used. To be removed.\n   * @breaking-change 15.0.0\n   */\n  _overlayContainer,\n  /**\n   * @deprecated No longer used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  _animationMode) {\n    this._overlay = _overlay;\n    this._defaultOptions = _defaultOptions;\n    this._scrollStrategy = _scrollStrategy;\n    this._parentDialog = _parentDialog;\n    this._openDialogsAtThisLevel = [];\n    this._afterAllClosedAtThisLevel = new Subject();\n    this._afterOpenedAtThisLevel = new Subject();\n    this.dialogConfigClass = MatDialogConfig;\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    this._dialog = injector.get(Dialog);\n    this._dialogRefConstructor = MatDialogRef;\n    this._dialogContainerType = MatDialogContainer;\n    this._dialogDataToken = MAT_DIALOG_DATA;\n  }\n  open(componentOrTemplateRef, config) {\n    let dialogRef;\n    config = {\n      ...(this._defaultOptions || new MatDialogConfig()),\n      ...config\n    };\n    config.id = config.id || `mat-mdc-dialog-${uniqueId++}`;\n    config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n    const cdkRef = this._dialog.open(componentOrTemplateRef, {\n      ...config,\n      positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n      // Disable closing since we need to sync it up to the animation ourselves.\n      disableClose: true,\n      // Disable closing on destroy, because this service cleans up its open dialogs as well.\n      // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n      // the dialogs immediately whereas we want it to wait for the animations to finish.\n      closeOnDestroy: false,\n      // Disable closing on detachments so that we can sync up the animation.\n      // The Material dialog ref handles this manually.\n      closeOnOverlayDetachments: false,\n      container: {\n        type: this._dialogContainerType,\n        providers: () => [\n        // Provide our config as the CDK config as well since it has the same interface as the\n        // CDK one, but it contains the actual values passed in by the user for things like\n        // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n        {\n          provide: this.dialogConfigClass,\n          useValue: config\n        }, {\n          provide: DialogConfig,\n          useValue: config\n        }]\n      },\n      templateContext: () => ({\n        dialogRef\n      }),\n      providers: (ref, cdkConfig, dialogContainer) => {\n        dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n        dialogRef.updatePosition(config?.position);\n        return [{\n          provide: this._dialogContainerType,\n          useValue: dialogContainer\n        }, {\n          provide: this._dialogDataToken,\n          useValue: cdkConfig.data\n        }, {\n          provide: this._dialogRefConstructor,\n          useValue: dialogRef\n        }];\n      }\n    });\n    // This can't be assigned in the `providers` callback, because\n    // the instance hasn't been assigned to the CDK ref yet.\n    dialogRef.componentRef = cdkRef.componentRef;\n    dialogRef.componentInstance = cdkRef.componentInstance;\n    this.openDialogs.push(dialogRef);\n    this.afterOpened.next(dialogRef);\n    dialogRef.afterClosed().subscribe(() => {\n      const index = this.openDialogs.indexOf(dialogRef);\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n        if (!this.openDialogs.length) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    });\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    this._closeDialogs(this.openDialogs);\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Only close the dialogs at this level on destroy\n    // since the parent service may still be active.\n    this._closeDialogs(this._openDialogsAtThisLevel);\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n  }\n  _closeDialogs(dialogs) {\n    let i = dialogs.length;\n    while (i--) {\n      dialogs[i].close();\n    }\n  }\n  static #_ = this.ɵfac = function MatDialog_Factory(t) {\n    return new (t || MatDialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.Location, 8), i0.ɵɵinject(MAT_DIALOG_DEFAULT_OPTIONS, 8), i0.ɵɵinject(MAT_DIALOG_SCROLL_STRATEGY), i0.ɵɵinject(MatDialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(ANIMATION_MODULE_TYPE, 8));\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatDialog,\n    factory: MatDialog.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$1.Overlay\n  }, {\n    type: i0.Injector\n  }, {\n    type: i2.Location,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: MatDialogConfig,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_DIALOG_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_DIALOG_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: MatDialog,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: i1$1.OverlayContainer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], null);\n})();\n\n/** Counter used to generate unique IDs for dialog elements. */\nlet dialogElementUid = 0;\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n  constructor(\n  // The dialog title directive is always used in combination with a `MatDialogRef`.\n  // tslint:disable-next-line: lightweight-tokens\n  dialogRef, _elementRef, _dialog) {\n    this.dialogRef = dialogRef;\n    this._elementRef = _elementRef;\n    this._dialog = _dialog;\n    /** Default to \"button\" to prevents accidental form submits. */\n    this.type = 'button';\n  }\n  ngOnInit() {\n    if (!this.dialogRef) {\n      // When this directive is included in a dialog via TemplateRef (rather than being\n      // in a Component), the DialogRef isn't available via injection because embedded\n      // views cannot be given a custom injector. Instead, we look up the DialogRef by\n      // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n      // be resolved at constructor time.\n      this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n  }\n  ngOnChanges(changes) {\n    const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n    if (proxiedChange) {\n      this.dialogResult = proxiedChange.currentValue;\n    }\n  }\n  _onButtonClick(event) {\n    // Determinate the focus origin using the click event, because using the FocusMonitor will\n    // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n    // dialog, and therefore clicking the button won't result in a focus change. This means that\n    // the FocusMonitor won't detect any origin change, and will always output `program`.\n    _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n  }\n  static #_ = this.ɵfac = function MatDialogClose_Factory(t) {\n    return new (t || MatDialogClose)(i0.ɵɵdirectiveInject(MatDialogRef, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatDialog));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogClose,\n    selectors: [[\"\", \"mat-dialog-close\", \"\"], [\"\", \"matDialogClose\", \"\"]],\n    hostVars: 2,\n    hostBindings: function MatDialogClose_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatDialogClose_click_HostBindingHandler($event) {\n          return ctx._onButtonClick($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"type\", ctx.type);\n      }\n    },\n    inputs: {\n      ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n      type: \"type\",\n      dialogResult: [i0.ɵɵInputFlags.None, \"mat-dialog-close\", \"dialogResult\"],\n      _matDialogClose: [i0.ɵɵInputFlags.None, \"matDialogClose\", \"_matDialogClose\"]\n    },\n    exportAs: [\"matDialogClose\"],\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogClose, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-close], [matDialogClose]',\n      exportAs: 'matDialogClose',\n      standalone: true,\n      host: {\n        '(click)': '_onButtonClick($event)',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.type]': 'type'\n      }\n    }]\n  }], () => [{\n    type: MatDialogRef,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: MatDialog\n  }], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    type: [{\n      type: Input\n    }],\n    dialogResult: [{\n      type: Input,\n      args: ['mat-dialog-close']\n    }],\n    _matDialogClose: [{\n      type: Input,\n      args: ['matDialogClose']\n    }]\n  });\n})();\nclass MatDialogLayoutSection {\n  constructor(\n  // The dialog title directive is always used in combination with a `MatDialogRef`.\n  // tslint:disable-next-line: lightweight-tokens\n  _dialogRef, _elementRef, _dialog) {\n    this._dialogRef = _dialogRef;\n    this._elementRef = _elementRef;\n    this._dialog = _dialog;\n  }\n  ngOnInit() {\n    if (!this._dialogRef) {\n      this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n    }\n    if (this._dialogRef) {\n      Promise.resolve().then(() => {\n        this._onAdd();\n      });\n    }\n  }\n  ngOnDestroy() {\n    // Note: we null check because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    const instance = this._dialogRef?._containerInstance;\n    if (instance) {\n      Promise.resolve().then(() => {\n        this._onRemove();\n      });\n    }\n  }\n  static #_ = this.ɵfac = function MatDialogLayoutSection_Factory(t) {\n    return new (t || MatDialogLayoutSection)(i0.ɵɵdirectiveInject(MatDialogRef, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatDialog));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogLayoutSection,\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogLayoutSection, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], () => [{\n    type: MatDialogRef,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: MatDialog\n  }], null);\n})();\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle extends MatDialogLayoutSection {\n  constructor() {\n    super(...arguments);\n    this.id = `mat-mdc-dialog-title-${dialogElementUid++}`;\n  }\n  _onAdd() {\n    // Note: we null check the queue, because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n  }\n  _onRemove() {\n    this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n  }\n  static #_ = this.ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDialogTitle_BaseFactory;\n    return function MatDialogTitle_Factory(t) {\n      return (ɵMatDialogTitle_BaseFactory || (ɵMatDialogTitle_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogTitle)))(t || MatDialogTitle);\n    };\n  })();\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogTitle,\n    selectors: [[\"\", \"mat-dialog-title\", \"\"], [\"\", \"matDialogTitle\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-title\", \"mdc-dialog__title\"],\n    hostVars: 1,\n    hostBindings: function MatDialogTitle_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\"\n    },\n    exportAs: [\"matDialogTitle\"],\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogTitle, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-dialog-title], [matDialogTitle]',\n      exportAs: 'matDialogTitle',\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n        '[id]': 'id'\n      }\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {\n  static #_ = this.ɵfac = function MatDialogContent_Factory(t) {\n    return new (t || MatDialogContent)();\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogContent,\n    selectors: [[\"\", \"mat-dialog-content\", \"\"], [\"mat-dialog-content\"], [\"\", \"matDialogContent\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-content\", \"mdc-dialog__content\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogContent, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n      host: {\n        'class': 'mat-mdc-dialog-content mdc-dialog__content'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions extends MatDialogLayoutSection {\n  _onAdd() {\n    this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n  }\n  _onRemove() {\n    this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n  }\n  static #_ = this.ɵfac = /* @__PURE__ */(() => {\n    let ɵMatDialogActions_BaseFactory;\n    return function MatDialogActions_Factory(t) {\n      return (ɵMatDialogActions_BaseFactory || (ɵMatDialogActions_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogActions)))(t || MatDialogActions);\n    };\n  })();\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatDialogActions,\n    selectors: [[\"\", \"mat-dialog-actions\", \"\"], [\"mat-dialog-actions\"], [\"\", \"matDialogActions\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-dialog-actions\", \"mdc-dialog__actions\"],\n    hostVars: 6,\n    hostBindings: function MatDialogActions_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-dialog-actions-align-start\", ctx.align === \"start\")(\"mat-mdc-dialog-actions-align-center\", ctx.align === \"center\")(\"mat-mdc-dialog-actions-align-end\", ctx.align === \"end\");\n      }\n    },\n    inputs: {\n      align: \"align\"\n    },\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogActions, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n        '[class.mat-mdc-dialog-actions-align-start]': 'align === \"start\"',\n        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n  let parent = element.nativeElement.parentElement;\n  while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n    parent = parent.parentElement;\n  }\n  return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\nconst DIRECTIVES = [MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent];\nclass MatDialogModule {\n  static #_ = this.ɵfac = function MatDialogModule_Factory(t) {\n    return new (t || MatDialogModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDialogModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatDialog],\n    imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatDialog]\n    }]\n  }], null, null);\n})();\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n */\nconst _defaultParams = {\n  params: {\n    enterAnimationDuration: '150ms',\n    exitAnimationDuration: '75ms'\n  }\n};\n/**\n * Animations used by MatDialog.\n * @docs-private\n */\nconst matDialogAnimations = {\n  /** Animation that is applied on the dialog container by default. */\n  dialogContainer: trigger('dialogContainer', [\n  // Note: The `enter` animation transitions to `transform: none`, because for some reason\n  // specifying the transform explicitly, causes IE both to blur the dialog content and\n  // decimate the animation performance. Leaving it as `none` solves both issues.\n  state('void, exit', style({\n    opacity: 0,\n    transform: 'scale(0.7)'\n  })), state('enter', style({\n    transform: 'none'\n  })), transition('* => enter', group([animate('{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)', style({\n    transform: 'none',\n    opacity: 1\n  })), query('@*', animateChild(), {\n    optional: true\n  })]), _defaultParams), transition('* => void, * => exit', group([animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', style({\n    opacity: 0\n  })), query('@*', animateChild(), {\n    optional: true\n  })]), _defaultParams)])\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DIALOG_DATA, MAT_DIALOG_DEFAULT_OPTIONS, MAT_DIALOG_SCROLL_STRATEGY, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, MatDialog, MatDialogActions, MatDialogClose, MatDialogConfig, MatDialogContainer, MatDialogContent, MatDialogModule, MatDialogRef, MatDialogState, MatDialogTitle, _closeDialogVia, _defaultParams, matDialogAnimations };", "map": {"version": 3, "names": ["i1$1", "Overlay", "OverlayModule", "i2", "DOCUMENT", "i0", "EventEmitter", "ANIMATION_MODULE_TYPE", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "InjectionToken", "inject", "Injectable", "SkipSelf", "Directive", "Input", "NgModule", "i1", "CdkDialogContainer", "Dialog", "DialogConfig", "DialogModule", "coerceNumberProperty", "CdkPortalOutlet", "PortalModule", "Subject", "merge", "defer", "filter", "take", "startWith", "ESCAPE", "hasModifierKey", "MatCommonModule", "trigger", "state", "style", "transition", "group", "animate", "query", "animate<PERSON><PERSON><PERSON>", "MatDialogContainer_ng_template_2_Template", "rf", "ctx", "MatDialogConfig", "constructor", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "width", "height", "data", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "delayFocusTrap", "closeOnNavigation", "OPEN_CLASS", "OPENING_CLASS", "CLOSING_CLASS", "OPEN_ANIMATION_DURATION", "CLOSE_ANIMATION_DURATION", "MatDialogContainer", "elementRef", "focusTrapFactory", "_document", "dialogConfig", "interactivityChecker", "ngZone", "overlayRef", "_animationMode", "focusMonitor", "_animationStateChanged", "_animationsEnabled", "_actionSectionCount", "_hostElement", "_elementRef", "nativeElement", "_enterAnimationDuration", "parseCssTime", "_config", "enterAnimationDuration", "_exitAnimationDuration", "exitAnimationDuration", "_animationTimer", "_finishDialogOpen", "_clearAnimationClasses", "_openAnimationDone", "_finishDialogClose", "emit", "totalTime", "_contentAttached", "_startOpenAnimation", "setProperty", "TRANSITION_DURATION_PROPERTY", "_requestAnimationFrame", "classList", "add", "_waitForAnimationToComplete", "Promise", "resolve", "then", "_startExitAnimation", "remove", "_updateActionSectionCount", "delta", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration", "callback", "clearTimeout", "setTimeout", "_ngZone", "runOutsideAngular", "requestAnimationFrame", "_captureInitialFocus", "_trapFocus", "next", "ngOnDestroy", "attachComponentPortal", "portal", "ref", "location", "_", "ɵfac", "MatDialogContainer_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "FocusTrapFactory", "InteractivityChecker", "NgZone", "OverlayRef", "FocusMonitor", "_2", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatDialogContainer_HostBindings", "ɵɵhostProperty", "id", "ɵɵattribute", "_ariaLabelledByQueue", "ɵɵclassProp", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "MatDialogContainer_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "dependencies", "styles", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "host", "undefined", "decorators", "time", "endsWith", "substring", "length", "MatDialogState", "MatDialogRef", "_ref", "config", "_containerInstance", "_afterOpened", "_beforeClosed", "_state", "OPEN", "addPanelClass", "pipe", "event", "subscribe", "complete", "_closeFallbackTimeout", "detachments", "_result", "backdropClick", "keydownEvents", "keyCode", "preventDefault", "_closeDialogVia", "close", "dialogResult", "detachBackdrop", "CLOSING", "afterOpened", "afterClosed", "closed", "beforeClosed", "updatePosition", "position", "strategy", "positionStrategy", "left", "right", "centerHorizontally", "top", "bottom", "centerVertically", "updateSize", "classes", "removePanelClass", "getState", "CLOSED", "<PERSON><PERSON><PERSON><PERSON>", "_closeInteractionType", "componentInstance", "interactionType", "result", "MAT_DIALOG_DATA", "MAT_DIALOG_DEFAULT_OPTIONS", "MAT_DIALOG_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "block", "MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "MAT_DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "uniqueId", "MatDialog", "openDialogs", "_parentDialog", "_openDialogsAtThisLevel", "_afterOpenedAtThisLevel", "_getAfterAllClosed", "parent", "_afterAllClosedAtThisLevel", "_overlay", "injector", "_defaultOptions", "_scrollStrategy", "_overlayContainer", "dialogConfigClass", "afterAllClosed", "_dialog", "get", "_dialogRefConstructor", "_dialogContainerType", "_dialogDataToken", "open", "componentOrTemplateRef", "dialogRef", "scrollStrategy", "cdkRef", "global", "closeOnDestroy", "closeOnOverlayDetachments", "container", "providers", "useValue", "templateContext", "cdkConfig", "dialogContainer", "componentRef", "push", "index", "indexOf", "splice", "closeAll", "_closeDialogs", "getDialogById", "find", "dialog", "dialogs", "i", "MatDialog_Factory", "ɵɵinject", "Injector", "Location", "OverlayContainer", "ɵprov", "ɵɵdefineInjectable", "token", "dialogElementUid", "MatDialogClose", "ngOnInit", "getClosestDialog", "ngOnChanges", "changes", "proxied<PERSON><PERSON>e", "currentValue", "_onButtonClick", "screenX", "screenY", "MatDialogClose_Factory", "ɵdir", "ɵɵdefineDirective", "MatDialogClose_HostBindings", "ɵɵlistener", "MatDialogClose_click_HostBindingHandler", "$event", "inputs", "ɵɵInputFlags", "_matDialogClose", "exportAs", "ɵɵNgOnChangesFeature", "MatDialogLayoutSection", "_dialogRef", "_onAdd", "instance", "_onRemove", "MatDialogLayoutSection_Factory", "MatDialogTitle", "arguments", "_addAriaLabelledBy", "_removeAriaLabelledBy", "ɵMatDialogTitle_BaseFactory", "MatDialogTitle_Factory", "ɵɵgetInheritedFactory", "MatDialogTitle_HostBindings", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogContent_Factory", "MatDialogActions", "ɵMatDialogActions_BaseFactory", "MatDialogActions_Factory", "MatDialogActions_HostBindings", "align", "element", "parentElement", "contains", "DIRECTIVES", "MatDialogModule", "MatDialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "exports", "_defaultParams", "params", "matDialogAnimations", "opacity", "transform", "optional"], "sources": ["C:/Users/<USER>/Desktop/Printexa_Frontend/node_modules/@angular/material/fesm2022/dialog.mjs"], "sourcesContent": ["import * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, InjectionToken, inject, Injectable, SkipSelf, Directive, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { MatCommonModule } from '@angular/material/core';\nimport { trigger, state, style, transition, group, animate, query, animateChild } from '@angular/animations';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nclass MatDialogConfig {\n    constructor() {\n        /** The ARIA role of the dialog element. */\n        this.role = 'dialog';\n        /** Custom class for the overlay pane. */\n        this.panelClass = '';\n        /** Whether the dialog has a backdrop. */\n        this.hasBackdrop = true;\n        /** Custom class for the backdrop. */\n        this.backdropClass = '';\n        /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n        this.disableClose = false;\n        /** Width of the dialog. */\n        this.width = '';\n        /** Height of the dialog. */\n        this.height = '';\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** ID of the element that describes the dialog. */\n        this.ariaDescribedBy = null;\n        /** ID of the element that labels the dialog. */\n        this.ariaLabelledBy = null;\n        /** Aria label to assign to the dialog element. */\n        this.ariaLabel = null;\n        /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n        this.ariaModal = true;\n        /**\n         * Where the dialog should focus on open.\n         * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n         * AutoFocusTarget instead.\n         */\n        this.autoFocus = 'first-tabbable';\n        /**\n         * Whether the dialog should restore focus to the\n         * previously-focused element, after it's closed.\n         */\n        this.restoreFocus = true;\n        /** Whether to wait for the opening animation to finish before trapping focus. */\n        this.delayFocusTrap = true;\n        /**\n         * Whether the dialog should close when the user goes backwards/forwards in history.\n         * Note that this usually doesn't include clicking on links (unless the user is using\n         * the `HashLocationStrategy`).\n         */\n        this.closeOnNavigation = true;\n        // TODO(jelbourn): add configuration for lifecycle hooks, ARIA labelling.\n    }\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nclass MatDialogContainer extends CdkDialogContainer {\n    constructor(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, _animationMode, focusMonitor) {\n        super(elementRef, focusTrapFactory, _document, dialogConfig, interactivityChecker, ngZone, overlayRef, focusMonitor);\n        this._animationMode = _animationMode;\n        /** Emits when an animation state changes. */\n        this._animationStateChanged = new EventEmitter();\n        /** Whether animations are enabled. */\n        this._animationsEnabled = this._animationMode !== 'NoopAnimations';\n        /** Number of actions projected in the dialog. */\n        this._actionSectionCount = 0;\n        /** Host element of the dialog container component. */\n        this._hostElement = this._elementRef.nativeElement;\n        /** Duration of the dialog open animation. */\n        this._enterAnimationDuration = this._animationsEnabled\n            ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION\n            : 0;\n        /** Duration of the dialog close animation. */\n        this._exitAnimationDuration = this._animationsEnabled\n            ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION\n            : 0;\n        /** Current timer for dialog animations. */\n        this._animationTimer = null;\n        /**\n         * Completes the dialog open by clearing potential animation classes, trapping\n         * focus and emitting an opened event.\n         */\n        this._finishDialogOpen = () => {\n            this._clearAnimationClasses();\n            this._openAnimationDone(this._enterAnimationDuration);\n        };\n        /**\n         * Completes the dialog close by clearing potential animation classes, restoring\n         * focus and emitting a closed event.\n         */\n        this._finishDialogClose = () => {\n            this._clearAnimationClasses();\n            this._animationStateChanged.emit({ state: 'closed', totalTime: this._exitAnimationDuration });\n        };\n    }\n    _contentAttached() {\n        // Delegate to the original dialog-container initialization (i.e. saving the\n        // previous element, setting up the focus trap and moving focus to the container).\n        super._contentAttached();\n        // Note: Usually we would be able to use the MDC dialog foundation here to handle\n        // the dialog animation for us, but there are a few reasons why we just leverage\n        // their styles and not use the runtime foundation code:\n        //   1. Foundation does not allow us to disable animations.\n        //   2. Foundation contains unnecessary features we don't need and aren't\n        //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n        //   3. Foundation uses unnecessary timers for animations to work around limitations\n        //      in React's `setState` mechanism.\n        //      https://github.com/material-components/material-components-web/pull/3682.\n        this._startOpenAnimation();\n    }\n    /** Starts the dialog open animation if enabled. */\n    _startOpenAnimation() {\n        this._animationStateChanged.emit({ state: 'opening', totalTime: this._enterAnimationDuration });\n        if (this._animationsEnabled) {\n            this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n            // We need to give the `setProperty` call from above some time to be applied.\n            // One would expect that the open class is added once the animation finished, but MDC\n            // uses the open class in combination with the opening class to start the animation.\n            this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n            this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n        }\n        else {\n            this._hostElement.classList.add(OPEN_CLASS);\n            // Note: We could immediately finish the dialog opening here with noop animations,\n            // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n            // Executing this immediately would mean that `afterOpened` emits synchronously\n            // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n            Promise.resolve().then(() => this._finishDialogOpen());\n        }\n    }\n    /**\n     * Starts the exit animation of the dialog if enabled. This method is\n     * called by the dialog ref.\n     */\n    _startExitAnimation() {\n        this._animationStateChanged.emit({ state: 'closing', totalTime: this._exitAnimationDuration });\n        this._hostElement.classList.remove(OPEN_CLASS);\n        if (this._animationsEnabled) {\n            this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n            // We need to give the `setProperty` call from above some time to be applied.\n            this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n            this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n        }\n        else {\n            // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n            // set up before any user can subscribe to the backdrop click. The subscription triggers\n            // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n            // animation state event if animations are disabled, the overlay would be disposed\n            // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n            // skipped. We work around this by waiting with the dialog close until the next tick when\n            // all subscriptions have been fired as expected. This is not an ideal solution, but\n            // there doesn't seem to be any other good way. Alternatives that have been considered:\n            //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n            //      Also this issue is specific to the MDC implementation where the dialog could\n            //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n            //      and closing always takes at least a tick.\n            //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n            //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n            //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n            // Based on the fact that this is specific to the MDC-based implementation of the dialog\n            // animations, the defer is applied here.\n            Promise.resolve().then(() => this._finishDialogClose());\n        }\n    }\n    /**\n     * Updates the number action sections.\n     * @param delta Increase/decrease in the number of sections.\n     */\n    _updateActionSectionCount(delta) {\n        this._actionSectionCount += delta;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Clears all dialog animation classes. */\n    _clearAnimationClasses() {\n        this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n    }\n    _waitForAnimationToComplete(duration, callback) {\n        if (this._animationTimer !== null) {\n            clearTimeout(this._animationTimer);\n        }\n        // Note that we want this timer to run inside the NgZone, because we want\n        // the related events like `afterClosed` to be inside the zone as well.\n        this._animationTimer = setTimeout(callback, duration);\n    }\n    /** Runs a callback in `requestAnimationFrame`, if available. */\n    _requestAnimationFrame(callback) {\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame === 'function') {\n                requestAnimationFrame(callback);\n            }\n            else {\n                callback();\n            }\n        });\n    }\n    _captureInitialFocus() {\n        if (!this._config.delayFocusTrap) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Callback for when the open dialog animation has finished. Intended to\n     * be called by sub-classes that use different animation implementations.\n     */\n    _openAnimationDone(totalTime) {\n        if (this._config.delayFocusTrap) {\n            this._trapFocus();\n        }\n        this._animationStateChanged.next({ state: 'opened', totalTime });\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._animationTimer !== null) {\n            clearTimeout(this._animationTimer);\n        }\n    }\n    attachComponentPortal(portal) {\n        // When a component is passed into the dialog, the host element interrupts\n        // the `display:flex` from affecting the dialog title, content, and\n        // actions. To fix this, we make the component host `display: contents` by\n        // marking its host with the `mat-mdc-dialog-component-host` class.\n        //\n        // Note that this problem does not exist when a template ref is used since\n        // the title, contents, and actions are then nested directly under the\n        // dialog surface.\n        const ref = super.attachComponentPortal(portal);\n        ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n        return ref;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogContainer, deps: [{ token: i0.ElementRef }, { token: i1.FocusTrapFactory }, { token: DOCUMENT, optional: true }, { token: MatDialogConfig }, { token: i1.InteractivityChecker }, { token: i0.NgZone }, { token: i1$1.OverlayRef }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: i1.FocusMonitor }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatDialogContainer, isStandalone: true, selector: \"mat-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.aria-modal\": \"_config.ariaModal\", \"id\": \"_config.id\", \"attr.role\": \"_config.role\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledByQueue[0]\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\", \"class._mat-animation-noopable\": \"!_animationsEnabled\", \"class.mat-mdc-dialog-container-with-actions\": \"_actionSectionCount > 0\" }, classAttribute: \"mat-mdc-dialog-container mdc-dialog\" }, usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\", styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0;transform:scale(0.8)}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--closing .mdc-dialog__surface{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{opacity:1}.mdc-dialog--open .mdc-dialog__surface{transform:none}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{width:100%;height:100%}.mat-mdc-dialog-component-host{display:contents}.mat-mdc-dialog-container{--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition:opacity linear var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container .mdc-dialog__surface{transition:transform var(--mat-dialog-transition-duration, 0ms) 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container,.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__surface{transition:none}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 80vw);min-width:var(--mat-dialog-container-min-width, 0)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, 80vw)}}.mat-mdc-dialog-title{padding:var(--mat-dialog-headline-padding, 0 24px 9px)}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{padding:var(--mat-dialog-actions-padding, 8px);justify-content:var(--mat-dialog-actions-alignment, start)}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, standalone: true, imports: [CdkPortalOutlet], host: {\n                        'class': 'mat-mdc-dialog-container mdc-dialog',\n                        'tabindex': '-1',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[id]': '_config.id',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                        '[class._mat-animation-noopable]': '!_animationsEnabled',\n                        '[class.mat-mdc-dialog-container-with-actions]': '_actionSectionCount > 0',\n                    }, template: \"<div class=\\\"mdc-dialog__container\\\">\\n  <div class=\\\"mat-mdc-dialog-surface mdc-dialog__surface\\\">\\n    <ng-template cdkPortalOutlet />\\n  </div>\\n</div>\\n\", styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0;transform:scale(0.8)}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--closing .mdc-dialog__surface{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{opacity:1}.mdc-dialog--open .mdc-dialog__surface{transform:none}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{width:100%;height:100%}.mat-mdc-dialog-component-host{display:contents}.mat-mdc-dialog-container{--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition:opacity linear var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container .mdc-dialog__surface{transition:transform var(--mat-dialog-transition-duration, 0ms) 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container,.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__surface{transition:none}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 80vw);min-width:var(--mat-dialog-container-min-width, 0)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, 80vw)}}.mat-mdc-dialog-title{padding:var(--mat-dialog-headline-padding, 0 24px 9px)}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{padding:var(--mat-dialog-actions-padding, 8px);justify-content:var(--mat-dialog-actions-alignment, start)}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: MatDialogConfig }, { type: i1.InteractivityChecker }, { type: i0.NgZone }, { type: i1$1.OverlayRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: i1.FocusMonitor }] });\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n    if (time == null) {\n        return null;\n    }\n    if (typeof time === 'number') {\n        return time;\n    }\n    if (time.endsWith('ms')) {\n        return coerceNumberProperty(time.substring(0, time.length - 2));\n    }\n    if (time.endsWith('s')) {\n        return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n    }\n    if (time === '0') {\n        return 0;\n    }\n    return null; // anything else is invalid.\n}\n\nvar MatDialogState;\n(function (MatDialogState) {\n    MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n    MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n    MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n})(MatDialogState || (MatDialogState = {}));\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n    constructor(_ref, config, _containerInstance) {\n        this._ref = _ref;\n        this._containerInstance = _containerInstance;\n        /** Subject for notifying the user that the dialog has finished opening. */\n        this._afterOpened = new Subject();\n        /** Subject for notifying the user that the dialog has started closing. */\n        this._beforeClosed = new Subject();\n        /** Current state of the dialog. */\n        this._state = MatDialogState.OPEN;\n        this.disableClose = config.disableClose;\n        this.id = _ref.id;\n        // Used to target panels specifically tied to dialogs.\n        _ref.addPanelClass('mat-mdc-dialog-panel');\n        // Emit when opening animation completes\n        _containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'opened'), take(1))\n            .subscribe(() => {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        });\n        // Dispose overlay when closing animation is complete\n        _containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'closed'), take(1))\n            .subscribe(() => {\n            clearTimeout(this._closeFallbackTimeout);\n            this._finishDialogClose();\n        });\n        _ref.overlayRef.detachments().subscribe(() => {\n            this._beforeClosed.next(this._result);\n            this._beforeClosed.complete();\n            this._finishDialogClose();\n        });\n        merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n            if (!this.disableClose) {\n                event.preventDefault();\n                _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param dialogResult Optional result to return to the dialog opener.\n     */\n    close(dialogResult) {\n        this._result = dialogResult;\n        // Transition the backdrop in parallel to the dialog.\n        this._containerInstance._animationStateChanged\n            .pipe(filter(event => event.state === 'closing'), take(1))\n            .subscribe(event => {\n            this._beforeClosed.next(dialogResult);\n            this._beforeClosed.complete();\n            this._ref.overlayRef.detachBackdrop();\n            // The logic that disposes of the overlay depends on the exit animation completing, however\n            // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n            // timeout which will clean everything up if the animation hasn't fired within the specified\n            // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n            // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n            this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n        });\n        this._state = MatDialogState.CLOSING;\n        this._containerInstance._startExitAnimation();\n    }\n    /**\n     * Gets an observable that is notified when the dialog is finished opening.\n     */\n    afterOpened() {\n        return this._afterOpened;\n    }\n    /**\n     * Gets an observable that is notified when the dialog is finished closing.\n     */\n    afterClosed() {\n        return this._ref.closed;\n    }\n    /**\n     * Gets an observable that is notified when the dialog has started closing.\n     */\n    beforeClosed() {\n        return this._beforeClosed;\n    }\n    /**\n     * Gets an observable that emits when the overlay's backdrop has been clicked.\n     */\n    backdropClick() {\n        return this._ref.backdropClick;\n    }\n    /**\n     * Gets an observable that emits when keydown events are targeted on the overlay.\n     */\n    keydownEvents() {\n        return this._ref.keydownEvents;\n    }\n    /**\n     * Updates the dialog's position.\n     * @param position New dialog position.\n     */\n    updatePosition(position) {\n        let strategy = this._ref.config.positionStrategy;\n        if (position && (position.left || position.right)) {\n            position.left ? strategy.left(position.left) : strategy.right(position.right);\n        }\n        else {\n            strategy.centerHorizontally();\n        }\n        if (position && (position.top || position.bottom)) {\n            position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n        }\n        else {\n            strategy.centerVertically();\n        }\n        this._ref.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this._ref.updateSize(width, height);\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this._ref.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this._ref.removePanelClass(classes);\n        return this;\n    }\n    /** Gets the current state of the dialog's lifecycle. */\n    getState() {\n        return this._state;\n    }\n    /**\n     * Finishes the dialog close by updating the state of the dialog\n     * and disposing the overlay.\n     */\n    _finishDialogClose() {\n        this._state = MatDialogState.CLOSED;\n        this._ref.close(this._result, { focusOrigin: this._closeInteractionType });\n        this.componentInstance = null;\n    }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n    ref._closeInteractionType = interactionType;\n    return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.block();\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n// Counter for unique dialog ids.\nlet uniqueId = 0;\n/**\n * Service to open Material Design modal dialogs.\n */\nclass MatDialog {\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    constructor(_overlay, injector, \n    /**\n     * @deprecated `_location` parameter to be removed.\n     * @breaking-change 10.0.0\n     */\n    location, _defaultOptions, _scrollStrategy, _parentDialog, \n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 15.0.0\n     */\n    _overlayContainer, \n    /**\n     * @deprecated No longer used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    _animationMode) {\n        this._overlay = _overlay;\n        this._defaultOptions = _defaultOptions;\n        this._scrollStrategy = _scrollStrategy;\n        this._parentDialog = _parentDialog;\n        this._openDialogsAtThisLevel = [];\n        this._afterAllClosedAtThisLevel = new Subject();\n        this._afterOpenedAtThisLevel = new Subject();\n        this.dialogConfigClass = MatDialogConfig;\n        /**\n         * Stream that emits when all open dialog have finished closing.\n         * Will emit on subscribe if there are no open dialogs to begin with.\n         */\n        this.afterAllClosed = defer(() => this.openDialogs.length\n            ? this._getAfterAllClosed()\n            : this._getAfterAllClosed().pipe(startWith(undefined)));\n        this._dialog = injector.get(Dialog);\n        this._dialogRefConstructor = MatDialogRef;\n        this._dialogContainerType = MatDialogContainer;\n        this._dialogDataToken = MAT_DIALOG_DATA;\n    }\n    open(componentOrTemplateRef, config) {\n        let dialogRef;\n        config = { ...(this._defaultOptions || new MatDialogConfig()), ...config };\n        config.id = config.id || `mat-mdc-dialog-${uniqueId++}`;\n        config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n        const cdkRef = this._dialog.open(componentOrTemplateRef, {\n            ...config,\n            positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n            // Disable closing since we need to sync it up to the animation ourselves.\n            disableClose: true,\n            // Disable closing on destroy, because this service cleans up its open dialogs as well.\n            // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n            // the dialogs immediately whereas we want it to wait for the animations to finish.\n            closeOnDestroy: false,\n            // Disable closing on detachments so that we can sync up the animation.\n            // The Material dialog ref handles this manually.\n            closeOnOverlayDetachments: false,\n            container: {\n                type: this._dialogContainerType,\n                providers: () => [\n                    // Provide our config as the CDK config as well since it has the same interface as the\n                    // CDK one, but it contains the actual values passed in by the user for things like\n                    // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n                    { provide: this.dialogConfigClass, useValue: config },\n                    { provide: DialogConfig, useValue: config },\n                ],\n            },\n            templateContext: () => ({ dialogRef }),\n            providers: (ref, cdkConfig, dialogContainer) => {\n                dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n                dialogRef.updatePosition(config?.position);\n                return [\n                    { provide: this._dialogContainerType, useValue: dialogContainer },\n                    { provide: this._dialogDataToken, useValue: cdkConfig.data },\n                    { provide: this._dialogRefConstructor, useValue: dialogRef },\n                ];\n            },\n        });\n        // This can't be assigned in the `providers` callback, because\n        // the instance hasn't been assigned to the CDK ref yet.\n        dialogRef.componentRef = cdkRef.componentRef;\n        dialogRef.componentInstance = cdkRef.componentInstance;\n        this.openDialogs.push(dialogRef);\n        this.afterOpened.next(dialogRef);\n        dialogRef.afterClosed().subscribe(() => {\n            const index = this.openDialogs.indexOf(dialogRef);\n            if (index > -1) {\n                this.openDialogs.splice(index, 1);\n                if (!this.openDialogs.length) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        });\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        this._closeDialogs(this.openDialogs);\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Only close the dialogs at this level on destroy\n        // since the parent service may still be active.\n        this._closeDialogs(this._openDialogsAtThisLevel);\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n    }\n    _closeDialogs(dialogs) {\n        let i = dialogs.length;\n        while (i--) {\n            dialogs[i].close();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialog, deps: [{ token: i1$1.Overlay }, { token: i0.Injector }, { token: i2.Location, optional: true }, { token: MAT_DIALOG_DEFAULT_OPTIONS, optional: true }, { token: MAT_DIALOG_SCROLL_STRATEGY }, { token: MatDialog, optional: true, skipSelf: true }, { token: i1$1.OverlayContainer }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialog, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialog, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1$1.Overlay }, { type: i0.Injector }, { type: i2.Location, decorators: [{\n                    type: Optional\n                }] }, { type: MatDialogConfig, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DIALOG_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_DIALOG_SCROLL_STRATEGY]\n                }] }, { type: MatDialog, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: i1$1.OverlayContainer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }] });\n\n/** Counter used to generate unique IDs for dialog elements. */\nlet dialogElementUid = 0;\n/**\n * Button that will close the current dialog.\n */\nclass MatDialogClose {\n    constructor(\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    dialogRef, _elementRef, _dialog) {\n        this.dialogRef = dialogRef;\n        this._elementRef = _elementRef;\n        this._dialog = _dialog;\n        /** Default to \"button\" to prevents accidental form submits. */\n        this.type = 'button';\n    }\n    ngOnInit() {\n        if (!this.dialogRef) {\n            // When this directive is included in a dialog via TemplateRef (rather than being\n            // in a Component), the DialogRef isn't available via injection because embedded\n            // views cannot be given a custom injector. Instead, we look up the DialogRef by\n            // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n            // be resolved at constructor time.\n            this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n        }\n    }\n    ngOnChanges(changes) {\n        const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n        if (proxiedChange) {\n            this.dialogResult = proxiedChange.currentValue;\n        }\n    }\n    _onButtonClick(event) {\n        // Determinate the focus origin using the click event, because using the FocusMonitor will\n        // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n        // dialog, and therefore clicking the button won't result in a focus change. This means that\n        // the FocusMonitor won't detect any origin change, and will always output `program`.\n        _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogClose, deps: [{ token: MatDialogRef, optional: true }, { token: i0.ElementRef }, { token: MatDialog }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatDialogClose, isStandalone: true, selector: \"[mat-dialog-close], [matDialogClose]\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], type: \"type\", dialogResult: [\"mat-dialog-close\", \"dialogResult\"], _matDialogClose: [\"matDialogClose\", \"_matDialogClose\"] }, host: { listeners: { \"click\": \"_onButtonClick($event)\" }, properties: { \"attr.aria-label\": \"ariaLabel || null\", \"attr.type\": \"type\" } }, exportAs: [\"matDialogClose\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogClose, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-dialog-close], [matDialogClose]',\n                    exportAs: 'matDialogClose',\n                    standalone: true,\n                    host: {\n                        '(click)': '_onButtonClick($event)',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.type]': 'type',\n                    },\n                }]\n        }], ctorParameters: () => [{ type: MatDialogRef, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ElementRef }, { type: MatDialog }], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], type: [{\n                type: Input\n            }], dialogResult: [{\n                type: Input,\n                args: ['mat-dialog-close']\n            }], _matDialogClose: [{\n                type: Input,\n                args: ['matDialogClose']\n            }] } });\nclass MatDialogLayoutSection {\n    constructor(\n    // The dialog title directive is always used in combination with a `MatDialogRef`.\n    // tslint:disable-next-line: lightweight-tokens\n    _dialogRef, _elementRef, _dialog) {\n        this._dialogRef = _dialogRef;\n        this._elementRef = _elementRef;\n        this._dialog = _dialog;\n    }\n    ngOnInit() {\n        if (!this._dialogRef) {\n            this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n        }\n        if (this._dialogRef) {\n            Promise.resolve().then(() => {\n                this._onAdd();\n            });\n        }\n    }\n    ngOnDestroy() {\n        // Note: we null check because there are some internal\n        // tests that are mocking out `MatDialogRef` incorrectly.\n        const instance = this._dialogRef?._containerInstance;\n        if (instance) {\n            Promise.resolve().then(() => {\n                this._onRemove();\n            });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogLayoutSection, deps: [{ token: MatDialogRef, optional: true }, { token: i0.ElementRef }, { token: MatDialog }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatDialogLayoutSection, isStandalone: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogLayoutSection, decorators: [{\n            type: Directive,\n            args: [{ standalone: true }]\n        }], ctorParameters: () => [{ type: MatDialogRef, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ElementRef }, { type: MatDialog }] });\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nclass MatDialogTitle extends MatDialogLayoutSection {\n    constructor() {\n        super(...arguments);\n        this.id = `mat-mdc-dialog-title-${dialogElementUid++}`;\n    }\n    _onAdd() {\n        // Note: we null check the queue, because there are some internal\n        // tests that are mocking out `MatDialogRef` incorrectly.\n        this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n    }\n    _onRemove() {\n        this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogTitle, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatDialogTitle, isStandalone: true, selector: \"[mat-dialog-title], [matDialogTitle]\", inputs: { id: \"id\" }, host: { properties: { \"id\": \"id\" }, classAttribute: \"mat-mdc-dialog-title mdc-dialog__title\" }, exportAs: [\"matDialogTitle\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-dialog-title], [matDialogTitle]',\n                    exportAs: 'matDialogTitle',\n                    standalone: true,\n                    host: {\n                        'class': 'mat-mdc-dialog-title mdc-dialog__title',\n                        '[id]': 'id',\n                    },\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n/**\n * Scrollable content container of a dialog.\n */\nclass MatDialogContent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogContent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatDialogContent, isStandalone: true, selector: \"[mat-dialog-content], mat-dialog-content, [matDialogContent]\", host: { classAttribute: \"mat-mdc-dialog-content mdc-dialog__content\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n                    host: { 'class': 'mat-mdc-dialog-content mdc-dialog__content' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nclass MatDialogActions extends MatDialogLayoutSection {\n    _onAdd() {\n        this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n    }\n    _onRemove() {\n        this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogActions, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatDialogActions, isStandalone: true, selector: \"[mat-dialog-actions], mat-dialog-actions, [matDialogActions]\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-dialog-actions-align-start\": \"align === \\\"start\\\"\", \"class.mat-mdc-dialog-actions-align-center\": \"align === \\\"center\\\"\", \"class.mat-mdc-dialog-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-dialog-actions mdc-dialog__actions\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n                    standalone: true,\n                    host: {\n                        'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n                        '[class.mat-mdc-dialog-actions-align-start]': 'align === \"start\"',\n                        '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n                        '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n    let parent = element.nativeElement.parentElement;\n    while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n        parent = parent.parentElement;\n    }\n    return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\n\nconst DIRECTIVES = [\n    MatDialogContainer,\n    MatDialogClose,\n    MatDialogTitle,\n    MatDialogActions,\n    MatDialogContent,\n];\nclass MatDialogModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogModule, imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatDialogContainer,\n            MatDialogClose,\n            MatDialogTitle,\n            MatDialogActions,\n            MatDialogContent], exports: [MatCommonModule, MatDialogContainer,\n            MatDialogClose,\n            MatDialogTitle,\n            MatDialogActions,\n            MatDialogContent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogModule, providers: [MatDialog], imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatDialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, ...DIRECTIVES],\n                    exports: [MatCommonModule, ...DIRECTIVES],\n                    providers: [MatDialog],\n                }]\n        }] });\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n */\nconst _defaultParams = {\n    params: { enterAnimationDuration: '150ms', exitAnimationDuration: '75ms' },\n};\n/**\n * Animations used by MatDialog.\n * @docs-private\n */\nconst matDialogAnimations = {\n    /** Animation that is applied on the dialog container by default. */\n    dialogContainer: trigger('dialogContainer', [\n        // Note: The `enter` animation transitions to `transform: none`, because for some reason\n        // specifying the transform explicitly, causes IE both to blur the dialog content and\n        // decimate the animation performance. Leaving it as `none` solves both issues.\n        state('void, exit', style({ opacity: 0, transform: 'scale(0.7)' })),\n        state('enter', style({ transform: 'none' })),\n        transition('* => enter', group([\n            animate('{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)', style({ transform: 'none', opacity: 1 })),\n            query('@*', animateChild(), { optional: true }),\n        ]), _defaultParams),\n        transition('* => void, * => exit', group([\n            animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', style({ opacity: 0 })),\n            query('@*', animateChild(), { optional: true }),\n        ]), _defaultParams),\n    ]),\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_DIALOG_DATA, MAT_DIALOG_DEFAULT_OPTIONS, MAT_DIALOG_SCROLL_STRATEGY, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, MatDialog, MatDialogActions, MatDialogClose, MatDialogConfig, MatDialogContainer, MatDialogContent, MatDialogModule, MatDialogRef, MatDialogState, MatDialogTitle, _closeDialogVia, _defaultParams, matDialogAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,QAAQ,sBAAsB;AAC7D,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACtN,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,kBAAkB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,YAAY,QAAQ,qBAAqB;AAC5F,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,eAAe,EAAEC,YAAY,QAAQ,qBAAqB;AACnE,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AACxD,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,YAAY,QAAQ,qBAAqB;;AAE5G;AACA;AACA;AAFA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;AAGA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;IACA,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;IACA,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,gBAAgB;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;EACJ;AACJ;;AAEA;AACA,MAAMC,UAAU,GAAG,kBAAkB;AACrC;AACA,MAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA,MAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA,MAAMC,uBAAuB,GAAG,GAAG;AACnC;AACA,MAAMC,wBAAwB,GAAG,EAAE;AACnC,MAAMC,kBAAkB,SAASlD,kBAAkB,CAAC;EAChD4B,WAAWA,CAACuB,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,YAAY,EAAE;IACvI,KAAK,CAACR,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,UAAU,EAAEE,YAAY,CAAC;IACpH,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACE,sBAAsB,GAAG,IAAI3E,YAAY,CAAC,CAAC;IAChD;IACA,IAAI,CAAC4E,kBAAkB,GAAG,IAAI,CAACH,cAAc,KAAK,gBAAgB;IAClE;IACA,IAAI,CAACI,mBAAmB,GAAG,CAAC;IAC5B;IACA,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa;IAClD;IACA,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACL,kBAAkB,GAChDM,YAAY,CAAC,IAAI,CAACC,OAAO,CAACC,sBAAsB,CAAC,IAAIrB,uBAAuB,GAC5E,CAAC;IACP;IACA,IAAI,CAACsB,sBAAsB,GAAG,IAAI,CAACT,kBAAkB,GAC/CM,YAAY,CAAC,IAAI,CAACC,OAAO,CAACG,qBAAqB,CAAC,IAAItB,wBAAwB,GAC5E,CAAC;IACP;IACA,IAAI,CAACuB,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,MAAM;MAC3B,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACT,uBAAuB,CAAC;IACzD,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACU,kBAAkB,GAAG,MAAM;MAC5B,IAAI,CAACF,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACd,sBAAsB,CAACiB,IAAI,CAAC;QAAE5D,KAAK,EAAE,QAAQ;QAAE6D,SAAS,EAAE,IAAI,CAACR;MAAuB,CAAC,CAAC;IACjG,CAAC;EACL;EACAS,gBAAgBA,CAAA,EAAG;IACf;IACA;IACA,KAAK,CAACA,gBAAgB,CAAC,CAAC;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAA,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACpB,sBAAsB,CAACiB,IAAI,CAAC;MAAE5D,KAAK,EAAE,SAAS;MAAE6D,SAAS,EAAE,IAAI,CAACZ;IAAwB,CAAC,CAAC;IAC/F,IAAI,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAACE,YAAY,CAAC7C,KAAK,CAAC+D,WAAW,CAACC,4BAA4B,EAAG,GAAE,IAAI,CAAChB,uBAAwB,IAAG,CAAC;MACtG;MACA;MACA;MACA,IAAI,CAACiB,sBAAsB,CAAC,MAAM,IAAI,CAACpB,YAAY,CAACqB,SAAS,CAACC,GAAG,CAACvC,aAAa,EAAED,UAAU,CAAC,CAAC;MAC7F,IAAI,CAACyC,2BAA2B,CAAC,IAAI,CAACpB,uBAAuB,EAAE,IAAI,CAACO,iBAAiB,CAAC;IAC1F,CAAC,MACI;MACD,IAAI,CAACV,YAAY,CAACqB,SAAS,CAACC,GAAG,CAACxC,UAAU,CAAC;MAC3C;MACA;MACA;MACA;MACA0C,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAAChB,iBAAiB,CAAC,CAAC,CAAC;IAC1D;EACJ;EACA;AACJ;AACA;AACA;EACIiB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC9B,sBAAsB,CAACiB,IAAI,CAAC;MAAE5D,KAAK,EAAE,SAAS;MAAE6D,SAAS,EAAE,IAAI,CAACR;IAAuB,CAAC,CAAC;IAC9F,IAAI,CAACP,YAAY,CAACqB,SAAS,CAACO,MAAM,CAAC9C,UAAU,CAAC;IAC9C,IAAI,IAAI,CAACgB,kBAAkB,EAAE;MACzB,IAAI,CAACE,YAAY,CAAC7C,KAAK,CAAC+D,WAAW,CAACC,4BAA4B,EAAG,GAAE,IAAI,CAACZ,sBAAuB,IAAG,CAAC;MACrG;MACA,IAAI,CAACa,sBAAsB,CAAC,MAAM,IAAI,CAACpB,YAAY,CAACqB,SAAS,CAACC,GAAG,CAACtC,aAAa,CAAC,CAAC;MACjF,IAAI,CAACuC,2BAA2B,CAAC,IAAI,CAAChB,sBAAsB,EAAE,IAAI,CAACM,kBAAkB,CAAC;IAC1F,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAW,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACb,kBAAkB,CAAC,CAAC,CAAC;IAC3D;EACJ;EACA;AACJ;AACA;AACA;EACIgB,yBAAyBA,CAACC,KAAK,EAAE;IAC7B,IAAI,CAAC/B,mBAAmB,IAAI+B,KAAK;IACjC,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACArB,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACX,YAAY,CAACqB,SAAS,CAACO,MAAM,CAAC7C,aAAa,EAAEC,aAAa,CAAC;EACpE;EACAuC,2BAA2BA,CAACU,QAAQ,EAAEC,QAAQ,EAAE;IAC5C,IAAI,IAAI,CAACzB,eAAe,KAAK,IAAI,EAAE;MAC/B0B,YAAY,CAAC,IAAI,CAAC1B,eAAe,CAAC;IACtC;IACA;IACA;IACA,IAAI,CAACA,eAAe,GAAG2B,UAAU,CAACF,QAAQ,EAAED,QAAQ,CAAC;EACzD;EACA;EACAb,sBAAsBA,CAACc,QAAQ,EAAE;IAC7B,IAAI,CAACG,OAAO,CAACC,iBAAiB,CAAC,MAAM;MACjC,IAAI,OAAOC,qBAAqB,KAAK,UAAU,EAAE;QAC7CA,qBAAqB,CAACL,QAAQ,CAAC;MACnC,CAAC,MACI;QACDA,QAAQ,CAAC,CAAC;MACd;IACJ,CAAC,CAAC;EACN;EACAM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACnC,OAAO,CAACzB,cAAc,EAAE;MAC9B,IAAI,CAAC6D,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;EACI7B,kBAAkBA,CAACG,SAAS,EAAE;IAC1B,IAAI,IAAI,CAACV,OAAO,CAACzB,cAAc,EAAE;MAC7B,IAAI,CAAC6D,UAAU,CAAC,CAAC;IACrB;IACA,IAAI,CAAC5C,sBAAsB,CAAC6C,IAAI,CAAC;MAAExF,KAAK,EAAE,QAAQ;MAAE6D;IAAU,CAAC,CAAC;EACpE;EACA4B,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,IAAI,CAAClC,eAAe,KAAK,IAAI,EAAE;MAC/B0B,YAAY,CAAC,IAAI,CAAC1B,eAAe,CAAC;IACtC;EACJ;EACAmC,qBAAqBA,CAACC,MAAM,EAAE;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,GAAG,GAAG,KAAK,CAACF,qBAAqB,CAACC,MAAM,CAAC;IAC/CC,GAAG,CAACC,QAAQ,CAAC7C,aAAa,CAACmB,SAAS,CAACC,GAAG,CAAC,+BAA+B,CAAC;IACzE,OAAOwB,GAAG;EACd;EAAC,QAAAE,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,2BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFhE,kBAAkB,EAA5BlE,EAAE,CAAAmI,iBAAA,CAA4CnI,EAAE,CAACoI,UAAU,GAA3DpI,EAAE,CAAAmI,iBAAA,CAAsEpH,EAAE,CAACsH,gBAAgB,GAA3FrI,EAAE,CAAAmI,iBAAA,CAAsGpI,QAAQ,MAAhHC,EAAE,CAAAmI,iBAAA,CAA2IxF,eAAe,GAA5J3C,EAAE,CAAAmI,iBAAA,CAAuKpH,EAAE,CAACuH,oBAAoB,GAAhMtI,EAAE,CAAAmI,iBAAA,CAA2MnI,EAAE,CAACuI,MAAM,GAAtNvI,EAAE,CAAAmI,iBAAA,CAAiOxI,IAAI,CAAC6I,UAAU,GAAlPxI,EAAE,CAAAmI,iBAAA,CAA6PjI,qBAAqB,MAApRF,EAAE,CAAAmI,iBAAA,CAA+SpH,EAAE,CAAC0H,YAAY;EAAA,CAA4C;EAAA,QAAAC,EAAA,GACnc,IAAI,CAACC,IAAI,kBAD8E3I,EAAE,CAAA4I,iBAAA;IAAAC,IAAA,EACJ3E,kBAAkB;IAAA4E,SAAA;IAAAC,SAAA,eAA0F,IAAI;IAAAC,QAAA;IAAAC,YAAA,WAAAC,gCAAAzG,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD9GzC,EAAE,CAAAmJ,cAAA,OAAAzG,GAAA,CAAA0C,OAAA,CAAAgE,EAAA;QAAFpJ,EAAE,CAAAqJ,WAAA,eAAA3G,GAAA,CAAA0C,OAAA,CAAA5B,SAAA,UAAAd,GAAA,CAAA0C,OAAA,CAAAvC,IAAA,qBAAAH,GAAA,CAAA0C,OAAA,CAAA7B,SAAA,UAAAb,GAAA,CAAA4G,oBAAA,mBAAA5G,GAAA,CAAA0C,OAAA,CAAA7B,SAAA,sBAAAb,GAAA,CAAA0C,OAAA,CAAA/B,eAAA;QAAFrD,EAAE,CAAAuJ,WAAA,6BAAA7G,GAAA,CAAAmC,kBAAA,2CAAAnC,GAAA,CAAAoC,mBAAA;MAAA;IAAA;IAAA0E,UAAA;IAAAC,QAAA,GAAFzJ,EAAE,CAAA0J,0BAAA,EAAF1J,EAAE,CAAA2J,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAvH,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFzC,EAAE,CAAAiK,cAAA,YAC0pB,CAAC,YAAD,CAAC;QAD7pBjK,EAAE,CAAAkK,UAAA,IAAA1H,yCAAA,wBAC6vB,CAAC;QADhwBxC,EAAE,CAAAmK,YAAA,CACuwB,CAAC,CAAD,CAAC;MAAA;IAAA;IAAAC,YAAA,GAAimX/I,eAAe;IAAAgJ,MAAA;IAAAC,aAAA;EAAA,EAAuN;AACrrZ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGvK,EAAE,CAAAwK,iBAAA,CAGXtG,kBAAkB,EAAc,CAAC;IAChH2E,IAAI,EAAE1I,SAAS;IACfsK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEJ,aAAa,EAAElK,iBAAiB,CAACuK,IAAI;MAAEC,eAAe,EAAEvK,uBAAuB,CAACwK,OAAO;MAAErB,UAAU,EAAE,IAAI;MAAEsB,OAAO,EAAE,CAACzJ,eAAe,CAAC;MAAE0J,IAAI,EAAE;QAC5K,OAAO,EAAE,qCAAqC;QAC9C,UAAU,EAAE,IAAI;QAChB,mBAAmB,EAAE,mBAAmB;QACxC,MAAM,EAAE,YAAY;QACpB,aAAa,EAAE,cAAc;QAC7B,wBAAwB,EAAE,oDAAoD;QAC9E,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE,iCAAiC;QAC5D,iCAAiC,EAAE,qBAAqB;QACxD,+CAA+C,EAAE;MACrD,CAAC;MAAEhB,QAAQ,EAAE,8JAA8J;MAAEM,MAAM,EAAE,CAAC,8hXAA8hX;IAAE,CAAC;EACnuX,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAExB,IAAI,EAAE7I,EAAE,CAACoI;EAAW,CAAC,EAAE;IAAES,IAAI,EAAE9H,EAAE,CAACsH;EAAiB,CAAC,EAAE;IAAEQ,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvGpC,IAAI,EAAEvI;IACV,CAAC,EAAE;MACCuI,IAAI,EAAEtI,MAAM;MACZkK,IAAI,EAAE,CAAC1K,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8I,IAAI,EAAElG;EAAgB,CAAC,EAAE;IAAEkG,IAAI,EAAE9H,EAAE,CAACuH;EAAqB,CAAC,EAAE;IAAEO,IAAI,EAAE7I,EAAE,CAACuI;EAAO,CAAC,EAAE;IAAEM,IAAI,EAAElJ,IAAI,CAAC6I;EAAW,CAAC,EAAE;IAAEK,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAChJpC,IAAI,EAAEvI;IACV,CAAC,EAAE;MACCuI,IAAI,EAAEtI,MAAM;MACZkK,IAAI,EAAE,CAACvK,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAE2I,IAAI,EAAE9H,EAAE,CAAC0H;EAAa,CAAC,CAAC;AAAA;AAChD,MAAMvC,4BAA4B,GAAG,kCAAkC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASf,YAAYA,CAAC+F,IAAI,EAAE;EACxB,IAAIA,IAAI,IAAI,IAAI,EAAE;IACd,OAAO,IAAI;EACf;EACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf;EACA,IAAIA,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrB,OAAO/J,oBAAoB,CAAC8J,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC;EACnE;EACA,IAAIH,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACpB,OAAO/J,oBAAoB,CAAC8J,IAAI,CAACE,SAAS,CAAC,CAAC,EAAEF,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;EAC1E;EACA,IAAIH,IAAI,KAAK,GAAG,EAAE;IACd,OAAO,CAAC;EACZ;EACA,OAAO,IAAI,CAAC,CAAC;AACjB;AAEA,IAAII,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACnDA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACzDA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC3D,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACf3I,WAAWA,CAAC4I,IAAI,EAAEC,MAAM,EAAEC,kBAAkB,EAAE;IAC1C,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACC,YAAY,GAAG,IAAIpK,OAAO,CAAC,CAAC;IACjC;IACA,IAAI,CAACqK,aAAa,GAAG,IAAIrK,OAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAACsK,MAAM,GAAGP,cAAc,CAACQ,IAAI;IACjC,IAAI,CAAC7I,YAAY,GAAGwI,MAAM,CAACxI,YAAY;IACvC,IAAI,CAACmG,EAAE,GAAGoC,IAAI,CAACpC,EAAE;IACjB;IACAoC,IAAI,CAACO,aAAa,CAAC,sBAAsB,CAAC;IAC1C;IACAL,kBAAkB,CAAC9G,sBAAsB,CACpCoH,IAAI,CAACtK,MAAM,CAACuK,KAAK,IAAIA,KAAK,CAAChK,KAAK,KAAK,QAAQ,CAAC,EAAEN,IAAI,CAAC,CAAC,CAAC,CAAC,CACxDuK,SAAS,CAAC,MAAM;MACjB,IAAI,CAACP,YAAY,CAAClE,IAAI,CAAC,CAAC;MACxB,IAAI,CAACkE,YAAY,CAACQ,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC;IACF;IACAT,kBAAkB,CAAC9G,sBAAsB,CACpCoH,IAAI,CAACtK,MAAM,CAACuK,KAAK,IAAIA,KAAK,CAAChK,KAAK,KAAK,QAAQ,CAAC,EAAEN,IAAI,CAAC,CAAC,CAAC,CAAC,CACxDuK,SAAS,CAAC,MAAM;MACjBhF,YAAY,CAAC,IAAI,CAACkF,qBAAqB,CAAC;MACxC,IAAI,CAACxG,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF4F,IAAI,CAAC/G,UAAU,CAAC4H,WAAW,CAAC,CAAC,CAACH,SAAS,CAAC,MAAM;MAC1C,IAAI,CAACN,aAAa,CAACnE,IAAI,CAAC,IAAI,CAAC6E,OAAO,CAAC;MACrC,IAAI,CAACV,aAAa,CAACO,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACvG,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;IACFpE,KAAK,CAAC,IAAI,CAAC+K,aAAa,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAACR,IAAI,CAACtK,MAAM,CAACuK,KAAK,IAAIA,KAAK,CAACQ,OAAO,KAAK5K,MAAM,IAAI,CAAC,IAAI,CAACoB,YAAY,IAAI,CAACnB,cAAc,CAACmK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAACD,KAAK,IAAI;MACjK,IAAI,CAAC,IAAI,CAAChJ,YAAY,EAAE;QACpBgJ,KAAK,CAACS,cAAc,CAAC,CAAC;QACtBC,eAAe,CAAC,IAAI,EAAEV,KAAK,CAACpD,IAAI,KAAK,SAAS,GAAG,UAAU,GAAG,OAAO,CAAC;MAC1E;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI+D,KAAKA,CAACC,YAAY,EAAE;IAChB,IAAI,CAACP,OAAO,GAAGO,YAAY;IAC3B;IACA,IAAI,CAACnB,kBAAkB,CAAC9G,sBAAsB,CACzCoH,IAAI,CAACtK,MAAM,CAACuK,KAAK,IAAIA,KAAK,CAAChK,KAAK,KAAK,SAAS,CAAC,EAAEN,IAAI,CAAC,CAAC,CAAC,CAAC,CACzDuK,SAAS,CAACD,KAAK,IAAI;MACpB,IAAI,CAACL,aAAa,CAACnE,IAAI,CAACoF,YAAY,CAAC;MACrC,IAAI,CAACjB,aAAa,CAACO,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACX,IAAI,CAAC/G,UAAU,CAACqI,cAAc,CAAC,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACV,qBAAqB,GAAGjF,UAAU,CAAC,MAAM,IAAI,CAACvB,kBAAkB,CAAC,CAAC,EAAEqG,KAAK,CAACnG,SAAS,GAAG,GAAG,CAAC;IACnG,CAAC,CAAC;IACF,IAAI,CAAC+F,MAAM,GAAGP,cAAc,CAACyB,OAAO;IACpC,IAAI,CAACrB,kBAAkB,CAAChF,mBAAmB,CAAC,CAAC;EACjD;EACA;AACJ;AACA;EACIsG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACrB,YAAY;EAC5B;EACA;AACJ;AACA;EACIsB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACzB,IAAI,CAAC0B,MAAM;EAC3B;EACA;AACJ;AACA;EACIC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACvB,aAAa;EAC7B;EACA;AACJ;AACA;EACIW,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACf,IAAI,CAACe,aAAa;EAClC;EACA;AACJ;AACA;EACIC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChB,IAAI,CAACgB,aAAa;EAClC;EACA;AACJ;AACA;AACA;EACIY,cAAcA,CAACC,QAAQ,EAAE;IACrB,IAAIC,QAAQ,GAAG,IAAI,CAAC9B,IAAI,CAACC,MAAM,CAAC8B,gBAAgB;IAChD,IAAIF,QAAQ,KAAKA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACI,KAAK,CAAC,EAAE;MAC/CJ,QAAQ,CAACG,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACH,QAAQ,CAACG,IAAI,CAAC,GAAGF,QAAQ,CAACG,KAAK,CAACJ,QAAQ,CAACI,KAAK,CAAC;IACjF,CAAC,MACI;MACDH,QAAQ,CAACI,kBAAkB,CAAC,CAAC;IACjC;IACA,IAAIL,QAAQ,KAAKA,QAAQ,CAACM,GAAG,IAAIN,QAAQ,CAACO,MAAM,CAAC,EAAE;MAC/CP,QAAQ,CAACM,GAAG,GAAGL,QAAQ,CAACK,GAAG,CAACN,QAAQ,CAACM,GAAG,CAAC,GAAGL,QAAQ,CAACM,MAAM,CAACP,QAAQ,CAACO,MAAM,CAAC;IAChF,CAAC,MACI;MACDN,QAAQ,CAACO,gBAAgB,CAAC,CAAC;IAC/B;IACA,IAAI,CAACrC,IAAI,CAAC4B,cAAc,CAAC,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIU,UAAUA,CAAC5K,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAACqI,IAAI,CAACsC,UAAU,CAAC5K,KAAK,EAAEC,MAAM,CAAC;IACnC,OAAO,IAAI;EACf;EACA;EACA4I,aAAaA,CAACgC,OAAO,EAAE;IACnB,IAAI,CAACvC,IAAI,CAACO,aAAa,CAACgC,OAAO,CAAC;IAChC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAACvC,IAAI,CAACwC,gBAAgB,CAACD,OAAO,CAAC;IACnC,OAAO,IAAI;EACf;EACA;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpC,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIjG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACiG,MAAM,GAAGP,cAAc,CAAC4C,MAAM;IACnC,IAAI,CAAC1C,IAAI,CAACoB,KAAK,CAAC,IAAI,CAACN,OAAO,EAAE;MAAE6B,WAAW,EAAE,IAAI,CAACC;IAAsB,CAAC,CAAC;IAC1E,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1B,eAAeA,CAAC9E,GAAG,EAAEyG,eAAe,EAAEC,MAAM,EAAE;EACnD1G,GAAG,CAACuG,qBAAqB,GAAGE,eAAe;EAC3C,OAAOzG,GAAG,CAAC+E,KAAK,CAAC2B,MAAM,CAAC;AAC5B;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIhO,cAAc,CAAC,kBAAkB,CAAC;AAC9D;AACA,MAAMiO,0BAA0B,GAAG,IAAIjO,cAAc,CAAC,gCAAgC,CAAC;AACvF;AACA,MAAMkO,0BAA0B,GAAG,IAAIlO,cAAc,CAAC,gCAAgC,EAAE;EACpFmO,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAGpO,MAAM,CAACb,OAAO,CAAC;IAC/B,OAAO,MAAMiP,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;EACjD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASC,2CAA2CA,CAACH,OAAO,EAAE;EAC1D,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,mCAAmC,GAAG;EACxCC,OAAO,EAAER,0BAA0B;EACnCS,IAAI,EAAE,CAACvP,OAAO,CAAC;EACfwP,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA,IAAIK,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,WAAW,GAAG,IAAI,CAACE,uBAAuB;EAC7F;EACA;EACA,IAAIzC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACwC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACxC,WAAW,GAAG,IAAI,CAAC0C,uBAAuB;EAC7F;EACAC,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,MAAM,GAAG,IAAI,CAACJ,aAAa;IACjC,OAAOI,MAAM,GAAGA,MAAM,CAACD,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACE,0BAA0B;EACjF;EACAjN,WAAWA,CAACkN,QAAQ,EAAEC,QAAQ;EAC9B;AACJ;AACA;AACA;EACIjI,QAAQ,EAAEkI,eAAe,EAAEC,eAAe,EAAET,aAAa;EACzD;AACJ;AACA;AACA;EACIU,iBAAiB;EACjB;AACJ;AACA;AACA;EACIxL,cAAc,EAAE;IACZ,IAAI,CAACoL,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACT,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACI,0BAA0B,GAAG,IAAItO,OAAO,CAAC,CAAC;IAC/C,IAAI,CAACmO,uBAAuB,GAAG,IAAInO,OAAO,CAAC,CAAC;IAC5C,IAAI,CAAC4O,iBAAiB,GAAGxN,eAAe;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAACyN,cAAc,GAAG3O,KAAK,CAAC,MAAM,IAAI,CAAC8N,WAAW,CAAClE,MAAM,GACnD,IAAI,CAACsE,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC3D,IAAI,CAACpK,SAAS,CAACoJ,SAAS,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACqF,OAAO,GAAGN,QAAQ,CAACO,GAAG,CAACrP,MAAM,CAAC;IACnC,IAAI,CAACsP,qBAAqB,GAAGhF,YAAY;IACzC,IAAI,CAACiF,oBAAoB,GAAGtM,kBAAkB;IAC9C,IAAI,CAACuM,gBAAgB,GAAGjC,eAAe;EAC3C;EACAkC,IAAIA,CAACC,sBAAsB,EAAElF,MAAM,EAAE;IACjC,IAAImF,SAAS;IACbnF,MAAM,GAAG;MAAE,IAAI,IAAI,CAACuE,eAAe,IAAI,IAAIrN,eAAe,CAAC,CAAC,CAAC;MAAE,GAAG8I;IAAO,CAAC;IAC1EA,MAAM,CAACrC,EAAE,GAAGqC,MAAM,CAACrC,EAAE,IAAK,kBAAiBiG,QAAQ,EAAG,EAAC;IACvD5D,MAAM,CAACoF,cAAc,GAAGpF,MAAM,CAACoF,cAAc,IAAI,IAAI,CAACZ,eAAe,CAAC,CAAC;IACvE,MAAMa,MAAM,GAAG,IAAI,CAACT,OAAO,CAACK,IAAI,CAACC,sBAAsB,EAAE;MACrD,GAAGlF,MAAM;MACT8B,gBAAgB,EAAE,IAAI,CAACuC,QAAQ,CAACzC,QAAQ,CAAC,CAAC,CAAC0D,MAAM,CAAC,CAAC,CAACrD,kBAAkB,CAAC,CAAC,CAACG,gBAAgB,CAAC,CAAC;MAC3F;MACA5K,YAAY,EAAE,IAAI;MAClB;MACA;MACA;MACA+N,cAAc,EAAE,KAAK;MACrB;MACA;MACAC,yBAAyB,EAAE,KAAK;MAChCC,SAAS,EAAE;QACPrI,IAAI,EAAE,IAAI,CAAC2H,oBAAoB;QAC/BW,SAAS,EAAEA,CAAA,KAAM;QACb;QACA;QACA;QACA;UAAEjC,OAAO,EAAE,IAAI,CAACiB,iBAAiB;UAAEiB,QAAQ,EAAE3F;QAAO,CAAC,EACrD;UAAEyD,OAAO,EAAEhO,YAAY;UAAEkQ,QAAQ,EAAE3F;QAAO,CAAC;MAEnD,CAAC;MACD4F,eAAe,EAAEA,CAAA,MAAO;QAAET;MAAU,CAAC,CAAC;MACtCO,SAAS,EAAEA,CAACtJ,GAAG,EAAEyJ,SAAS,EAAEC,eAAe,KAAK;QAC5CX,SAAS,GAAG,IAAI,IAAI,CAACL,qBAAqB,CAAC1I,GAAG,EAAE4D,MAAM,EAAE8F,eAAe,CAAC;QACxEX,SAAS,CAACxD,cAAc,CAAC3B,MAAM,EAAE4B,QAAQ,CAAC;QAC1C,OAAO,CACH;UAAE6B,OAAO,EAAE,IAAI,CAACsB,oBAAoB;UAAEY,QAAQ,EAAEG;QAAgB,CAAC,EACjE;UAAErC,OAAO,EAAE,IAAI,CAACuB,gBAAgB;UAAEW,QAAQ,EAAEE,SAAS,CAAClO;QAAK,CAAC,EAC5D;UAAE8L,OAAO,EAAE,IAAI,CAACqB,qBAAqB;UAAEa,QAAQ,EAAER;QAAU,CAAC,CAC/D;MACL;IACJ,CAAC,CAAC;IACF;IACA;IACAA,SAAS,CAACY,YAAY,GAAGV,MAAM,CAACU,YAAY;IAC5CZ,SAAS,CAACvC,iBAAiB,GAAGyC,MAAM,CAACzC,iBAAiB;IACtD,IAAI,CAACkB,WAAW,CAACkC,IAAI,CAACb,SAAS,CAAC;IAChC,IAAI,CAAC5D,WAAW,CAACvF,IAAI,CAACmJ,SAAS,CAAC;IAChCA,SAAS,CAAC3D,WAAW,CAAC,CAAC,CAACf,SAAS,CAAC,MAAM;MACpC,MAAMwF,KAAK,GAAG,IAAI,CAACnC,WAAW,CAACoC,OAAO,CAACf,SAAS,CAAC;MACjD,IAAIc,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ,IAAI,CAACnC,WAAW,CAACqC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,CAACnC,WAAW,CAAClE,MAAM,EAAE;UAC1B,IAAI,CAACsE,kBAAkB,CAAC,CAAC,CAAClI,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC;IACF,OAAOmJ,SAAS;EACpB;EACA;AACJ;AACA;EACIiB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,aAAa,CAAC,IAAI,CAACvC,WAAW,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIwC,aAAaA,CAAC3I,EAAE,EAAE;IACd,OAAO,IAAI,CAACmG,WAAW,CAACyC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC7I,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACA1B,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACoK,aAAa,CAAC,IAAI,CAACrC,uBAAuB,CAAC;IAChD,IAAI,CAACI,0BAA0B,CAAC1D,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACuD,uBAAuB,CAACvD,QAAQ,CAAC,CAAC;EAC3C;EACA2F,aAAaA,CAACI,OAAO,EAAE;IACnB,IAAIC,CAAC,GAAGD,OAAO,CAAC7G,MAAM;IACtB,OAAO8G,CAAC,EAAE,EAAE;MACRD,OAAO,CAACC,CAAC,CAAC,CAACvF,KAAK,CAAC,CAAC;IACtB;EACJ;EAAC,QAAA7E,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAoK,kBAAAlK,CAAA;IAAA,YAAAA,CAAA,IAAwFoH,SAAS,EAlYnBtP,EAAE,CAAAqS,QAAA,CAkYmC1S,IAAI,CAACC,OAAO,GAlYjDI,EAAE,CAAAqS,QAAA,CAkY4DrS,EAAE,CAACsS,QAAQ,GAlYzEtS,EAAE,CAAAqS,QAAA,CAkYoFvS,EAAE,CAACyS,QAAQ,MAlYjGvS,EAAE,CAAAqS,QAAA,CAkY4H5D,0BAA0B,MAlYxJzO,EAAE,CAAAqS,QAAA,CAkYmL3D,0BAA0B,GAlY/M1O,EAAE,CAAAqS,QAAA,CAkY0N/C,SAAS,OAlYrOtP,EAAE,CAAAqS,QAAA,CAkYgR1S,IAAI,CAAC6S,gBAAgB,GAlYvSxS,EAAE,CAAAqS,QAAA,CAkYkTnS,qBAAqB;EAAA,CAA6D;EAAA,QAAAwI,EAAA,GAC7d,IAAI,CAAC+J,KAAK,kBAnY6EzS,EAAE,CAAA0S,kBAAA;IAAAC,KAAA,EAmYYrD,SAAS;IAAAV,OAAA,EAATU,SAAS,CAAAtH,IAAA;IAAA2G,UAAA,EAAc;EAAM,EAAG;AAClJ;AACA;EAAA,QAAApE,SAAA,oBAAAA,SAAA,KArYoGvK,EAAE,CAAAwK,iBAAA,CAqYX8E,SAAS,EAAc,CAAC;IACvGzG,IAAI,EAAEnI,UAAU;IAChB+J,IAAI,EAAE,CAAC;MAAEkE,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9F,IAAI,EAAElJ,IAAI,CAACC;EAAQ,CAAC,EAAE;IAAEiJ,IAAI,EAAE7I,EAAE,CAACsS;EAAS,CAAC,EAAE;IAAEzJ,IAAI,EAAE/I,EAAE,CAACyS,QAAQ;IAAEtH,UAAU,EAAE,CAAC;MAChGpC,IAAI,EAAEvI;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuI,IAAI,EAAElG,eAAe;IAAEsI,UAAU,EAAE,CAAC;MACxCpC,IAAI,EAAEvI;IACV,CAAC,EAAE;MACCuI,IAAI,EAAEtI,MAAM;MACZkK,IAAI,EAAE,CAACgE,0BAA0B;IACrC,CAAC;EAAE,CAAC,EAAE;IAAE5F,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCpC,IAAI,EAAEtI,MAAM;MACZkK,IAAI,EAAE,CAACiE,0BAA0B;IACrC,CAAC;EAAE,CAAC,EAAE;IAAE7F,IAAI,EAAEyG,SAAS;IAAErE,UAAU,EAAE,CAAC;MAClCpC,IAAI,EAAEvI;IACV,CAAC,EAAE;MACCuI,IAAI,EAAElI;IACV,CAAC;EAAE,CAAC,EAAE;IAAEkI,IAAI,EAAElJ,IAAI,CAAC6S;EAAiB,CAAC,EAAE;IAAE3J,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnEpC,IAAI,EAAEvI;IACV,CAAC,EAAE;MACCuI,IAAI,EAAEtI,MAAM;MACZkK,IAAI,EAAE,CAACvK,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA,IAAI0S,gBAAgB,GAAG,CAAC;AACxB;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBjQ,WAAWA;EACX;EACA;EACAgO,SAAS,EAAE5L,WAAW,EAAEqL,OAAO,EAAE;IAC7B,IAAI,CAACO,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC5L,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACqL,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACxH,IAAI,GAAG,QAAQ;EACxB;EACAiK,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAClC,SAAS,EAAE;MACjB;MACA;MACA;MACA;MACA;MACA,IAAI,CAACA,SAAS,GAAGmC,gBAAgB,CAAC,IAAI,CAAC/N,WAAW,EAAE,IAAI,CAACqL,OAAO,CAACd,WAAW,CAAC;IACjF;EACJ;EACAyD,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,aAAa,GAAGD,OAAO,CAAC,iBAAiB,CAAC,IAAIA,OAAO,CAAC,uBAAuB,CAAC;IACpF,IAAIC,aAAa,EAAE;MACf,IAAI,CAACrG,YAAY,GAAGqG,aAAa,CAACC,YAAY;IAClD;EACJ;EACAC,cAAcA,CAACnH,KAAK,EAAE;IAClB;IACA;IACA;IACA;IACAU,eAAe,CAAC,IAAI,CAACiE,SAAS,EAAE3E,KAAK,CAACoH,OAAO,KAAK,CAAC,IAAIpH,KAAK,CAACqH,OAAO,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,IAAI,CAACzG,YAAY,CAAC;EACzH;EAAC,QAAA9E,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAuL,uBAAArL,CAAA;IAAA,YAAAA,CAAA,IAAwF2K,cAAc,EApcxB7S,EAAE,CAAAmI,iBAAA,CAocwCoD,YAAY,MApctDvL,EAAE,CAAAmI,iBAAA,CAociFnI,EAAE,CAACoI,UAAU,GApchGpI,EAAE,CAAAmI,iBAAA,CAoc2GmH,SAAS;EAAA,CAA4C;EAAA,QAAA5G,EAAA,GACzP,IAAI,CAAC8K,IAAI,kBArc8ExT,EAAE,CAAAyT,iBAAA;IAAA5K,IAAA,EAqcJgK,cAAc;IAAA/J,SAAA;IAAAE,QAAA;IAAAC,YAAA,WAAAyK,4BAAAjR,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QArcZzC,EAAE,CAAA2T,UAAA,mBAAAC,wCAAAC,MAAA;UAAA,OAqcJnR,GAAA,CAAA0Q,cAAA,CAAAS,MAAqB,CAAC;QAAA;MAAA;MAAA,IAAApR,EAAA;QArcpBzC,EAAE,CAAAqJ,WAAA,eAAA3G,GAAA,CAAAa,SAAA,kBAAAb,GAAA,CAAAmG,IAAA;MAAA;IAAA;IAAAiL,MAAA;MAAAvQ,SAAA,GAAFvD,EAAE,CAAA+T,YAAA,CAAApJ,IAAA;MAAA9B,IAAA;MAAAgE,YAAA,GAAF7M,EAAE,CAAA+T,YAAA,CAAApJ,IAAA;MAAAqJ,eAAA,GAAFhU,EAAE,CAAA+T,YAAA,CAAApJ,IAAA;IAAA;IAAAsJ,QAAA;IAAAzK,UAAA;IAAAC,QAAA,GAAFzJ,EAAE,CAAAkU,oBAAA;EAAA,EAqc2c;AACjjB;AACA;EAAA,QAAA3J,SAAA,oBAAAA,SAAA,KAvcoGvK,EAAE,CAAAwK,iBAAA,CAucXqI,cAAc,EAAc,CAAC;IAC5GhK,IAAI,EAAEjI,SAAS;IACf6J,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDuJ,QAAQ,EAAE,gBAAgB;MAC1BzK,UAAU,EAAE,IAAI;MAChBuB,IAAI,EAAE;QACF,SAAS,EAAE,wBAAwB;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,aAAa,EAAE;MACnB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAElC,IAAI,EAAE0C,YAAY;IAAEN,UAAU,EAAE,CAAC;MAClDpC,IAAI,EAAEvI;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuI,IAAI,EAAE7I,EAAE,CAACoI;EAAW,CAAC,EAAE;IAAES,IAAI,EAAEyG;EAAU,CAAC,CAAC,EAAkB;IAAE/L,SAAS,EAAE,CAAC;MACnFsF,IAAI,EAAEhI,KAAK;MACX4J,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE5B,IAAI,EAAE,CAAC;MACPA,IAAI,EAAEhI;IACV,CAAC,CAAC;IAAEgM,YAAY,EAAE,CAAC;MACfhE,IAAI,EAAEhI,KAAK;MACX4J,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEuJ,eAAe,EAAE,CAAC;MAClBnL,IAAI,EAAEhI,KAAK;MACX4J,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM0J,sBAAsB,CAAC;EACzBvR,WAAWA;EACX;EACA;EACAwR,UAAU,EAAEpP,WAAW,EAAEqL,OAAO,EAAE;IAC9B,IAAI,CAAC+D,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACpP,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACqL,OAAO,GAAGA,OAAO;EAC1B;EACAyC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACsB,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAGrB,gBAAgB,CAAC,IAAI,CAAC/N,WAAW,EAAE,IAAI,CAACqL,OAAO,CAACd,WAAW,CAAC;IAClF;IACA,IAAI,IAAI,CAAC6E,UAAU,EAAE;MACjB7N,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAC4N,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC;IACN;EACJ;EACA3M,WAAWA,CAAA,EAAG;IACV;IACA;IACA,MAAM4M,QAAQ,GAAG,IAAI,CAACF,UAAU,EAAE1I,kBAAkB;IACpD,IAAI4I,QAAQ,EAAE;MACV/N,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAC8N,SAAS,CAAC,CAAC;MACpB,CAAC,CAAC;IACN;EACJ;EAAC,QAAAxM,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAwM,+BAAAtM,CAAA;IAAA,YAAAA,CAAA,IAAwFiM,sBAAsB,EA9fhCnU,EAAE,CAAAmI,iBAAA,CA8fgDoD,YAAY,MA9f9DvL,EAAE,CAAAmI,iBAAA,CA8fyFnI,EAAE,CAACoI,UAAU,GA9fxGpI,EAAE,CAAAmI,iBAAA,CA8fmHmH,SAAS;EAAA,CAA4C;EAAA,QAAA5G,EAAA,GACjQ,IAAI,CAAC8K,IAAI,kBA/f8ExT,EAAE,CAAAyT,iBAAA;IAAA5K,IAAA,EA+fJsL,sBAAsB;IAAA3K,UAAA;EAAA,EAAqC;AAC7J;AACA;EAAA,QAAAe,SAAA,oBAAAA,SAAA,KAjgBoGvK,EAAE,CAAAwK,iBAAA,CAigBX2J,sBAAsB,EAAc,CAAC;IACpHtL,IAAI,EAAEjI,SAAS;IACf6J,IAAI,EAAE,CAAC;MAAEjB,UAAU,EAAE;IAAK,CAAC;EAC/B,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAE0C,YAAY;IAAEN,UAAU,EAAE,CAAC;MAClDpC,IAAI,EAAEvI;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuI,IAAI,EAAE7I,EAAE,CAACoI;EAAW,CAAC,EAAE;IAAES,IAAI,EAAEyG;EAAU,CAAC,CAAC;AAAA;AACnE;AACA;AACA;AACA,MAAMmF,cAAc,SAASN,sBAAsB,CAAC;EAChDvR,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG8R,SAAS,CAAC;IACnB,IAAI,CAACtL,EAAE,GAAI,wBAAuBwJ,gBAAgB,EAAG,EAAC;EAC1D;EACAyB,MAAMA,CAAA,EAAG;IACL;IACA;IACA,IAAI,CAACD,UAAU,CAAC1I,kBAAkB,EAAEiJ,kBAAkB,GAAG,IAAI,CAACvL,EAAE,CAAC;EACrE;EACAmL,SAASA,CAAA,EAAG;IACR,IAAI,CAACH,UAAU,EAAE1I,kBAAkB,EAAEkJ,qBAAqB,GAAG,IAAI,CAACxL,EAAE,CAAC;EACzE;EAAC,QAAArB,CAAA,GACQ,IAAI,CAACC,IAAI;IAAA,IAAA6M,2BAAA;IAAA,gBAAAC,uBAAA5M,CAAA;MAAA,QAAA2M,2BAAA,KAAAA,2BAAA,GAvhB8E7U,EAAE,CAAA+U,qBAAA,CAuhBQN,cAAc,IAAAvM,CAAA,IAAduM,cAAc;IAAA;EAAA,IAAqD;EAAA,QAAA/L,EAAA,GACpK,IAAI,CAAC8K,IAAI,kBAxhB8ExT,EAAE,CAAAyT,iBAAA;IAAA5K,IAAA,EAwhBJ4L,cAAc;IAAA3L,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAA+L,4BAAAvS,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAxhBZzC,EAAE,CAAAmJ,cAAA,OAAAzG,GAAA,CAAA0G,EAAA;MAAA;IAAA;IAAA0K,MAAA;MAAA1K,EAAA;IAAA;IAAA6K,QAAA;IAAAzK,UAAA;IAAAC,QAAA,GAAFzJ,EAAE,CAAA0J,0BAAA;EAAA,EAwhB4Q;AAClX;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA1hBoGvK,EAAE,CAAAwK,iBAAA,CA0hBXiK,cAAc,EAAc,CAAC;IAC5G5L,IAAI,EAAEjI,SAAS;IACf6J,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDuJ,QAAQ,EAAE,gBAAgB;MAC1BzK,UAAU,EAAE,IAAI;MAChBuB,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE3B,EAAE,EAAE,CAAC;MACnBP,IAAI,EAAEhI;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMoU,gBAAgB,CAAC;EAAA,QAAAlN,CAAA,GACV,IAAI,CAACC,IAAI,YAAAkN,yBAAAhN,CAAA;IAAA,YAAAA,CAAA,IAAwF+M,gBAAgB;EAAA,CAAmD;EAAA,QAAAvM,EAAA,GACpK,IAAI,CAAC8K,IAAI,kBA7iB8ExT,EAAE,CAAAyT,iBAAA;IAAA5K,IAAA,EA6iBJoM,gBAAgB;IAAAnM,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA,EAAuL;AACzS;AACA;EAAA,QAAAe,SAAA,oBAAAA,SAAA,KA/iBoGvK,EAAE,CAAAwK,iBAAA,CA+iBXyK,gBAAgB,EAAc,CAAC;IAC9GpM,IAAI,EAAEjI,SAAS;IACf6J,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,8DAA6D;MACxEK,IAAI,EAAE;QAAE,OAAO,EAAE;MAA6C,CAAC;MAC/DvB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAM2L,gBAAgB,SAAShB,sBAAsB,CAAC;EAClDE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACD,UAAU,CAAC1I,kBAAkB,EAAE9E,yBAAyB,GAAG,CAAC,CAAC;EACtE;EACA2N,SAASA,CAAA,EAAG;IACR,IAAI,CAACH,UAAU,CAAC1I,kBAAkB,EAAE9E,yBAAyB,GAAG,CAAC,CAAC,CAAC;EACvE;EAAC,QAAAmB,CAAA,GACQ,IAAI,CAACC,IAAI;IAAA,IAAAoN,6BAAA;IAAA,gBAAAC,yBAAAnN,CAAA;MAAA,QAAAkN,6BAAA,KAAAA,6BAAA,GAlkB8EpV,EAAE,CAAA+U,qBAAA,CAkkBQI,gBAAgB,IAAAjN,CAAA,IAAhBiN,gBAAgB;IAAA;EAAA,IAAqD;EAAA,QAAAzM,EAAA,GACtK,IAAI,CAAC8K,IAAI,kBAnkB8ExT,EAAE,CAAAyT,iBAAA;IAAA5K,IAAA,EAmkBJsM,gBAAgB;IAAArM,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAqM,8BAAA7S,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAnkBdzC,EAAE,CAAAuJ,WAAA,uCAAA7G,GAAA,CAAA6S,KAAA,qDAAA7S,GAAA,CAAA6S,KAAA,mDAAA7S,GAAA,CAAA6S,KAAA;MAAA;IAAA;IAAAzB,MAAA;MAAAyB,KAAA;IAAA;IAAA/L,UAAA;IAAAC,QAAA,GAAFzJ,EAAE,CAAA0J,0BAAA;EAAA,EAmkB6c;AACnjB;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KArkBoGvK,EAAE,CAAAwK,iBAAA,CAqkBX2K,gBAAgB,EAAc,CAAC;IAC9GtM,IAAI,EAAEjI,SAAS;IACf6J,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,8DAA6D;MACxElB,UAAU,EAAE,IAAI;MAChBuB,IAAI,EAAE;QACF,OAAO,EAAE,4CAA4C;QACrD,4CAA4C,EAAE,mBAAmB;QACjE,6CAA6C,EAAE,oBAAoB;QACnE,0CAA0C,EAAE;MAChD;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEwK,KAAK,EAAE,CAAC;MACtB1M,IAAI,EAAEhI;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,SAASkS,gBAAgBA,CAACyC,OAAO,EAAEjG,WAAW,EAAE;EAC5C,IAAIK,MAAM,GAAG4F,OAAO,CAACvQ,aAAa,CAACwQ,aAAa;EAChD,OAAO7F,MAAM,IAAI,CAACA,MAAM,CAACxJ,SAAS,CAACsP,QAAQ,CAAC,0BAA0B,CAAC,EAAE;IACrE9F,MAAM,GAAGA,MAAM,CAAC6F,aAAa;EACjC;EACA,OAAO7F,MAAM,GAAGL,WAAW,CAACyC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC7I,EAAE,KAAKwG,MAAM,CAACxG,EAAE,CAAC,GAAG,IAAI;AAC9E;AAEA,MAAMuM,UAAU,GAAG,CACfzR,kBAAkB,EAClB2O,cAAc,EACd4B,cAAc,EACdU,gBAAgB,EAChBF,gBAAgB,CACnB;AACD,MAAMW,eAAe,CAAC;EAAA,QAAA7N,CAAA,GACT,IAAI,CAACC,IAAI,YAAA6N,wBAAA3N,CAAA;IAAA,YAAAA,CAAA,IAAwF0N,eAAe;EAAA,CAAkD;EAAA,QAAAlN,EAAA,GAClK,IAAI,CAACoN,IAAI,kBA1mB8E9V,EAAE,CAAA+V,gBAAA;IAAAlN,IAAA,EA0mBS+M;EAAe,EAQ9F;EAAA,QAAAI,EAAA,GACnB,IAAI,CAACC,IAAI,kBAnnB8EjW,EAAE,CAAAkW,gBAAA;IAAA/E,SAAA,EAmnBqC,CAAC7B,SAAS,CAAC;IAAAxE,OAAA,GAAY3J,YAAY,EAAEtB,aAAa,EAAEyB,YAAY,EAAES,eAAe,EAAEA,eAAe;EAAA,EAAI;AACjP;AACA;EAAA,QAAAwI,SAAA,oBAAAA,SAAA,KArnBoGvK,EAAE,CAAAwK,iBAAA,CAqnBXoL,eAAe,EAAc,CAAC;IAC7G/M,IAAI,EAAE/H,QAAQ;IACd2J,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAAC3J,YAAY,EAAEtB,aAAa,EAAEyB,YAAY,EAAES,eAAe,EAAE,GAAG4T,UAAU,CAAC;MACpFQ,OAAO,EAAE,CAACpU,eAAe,EAAE,GAAG4T,UAAU,CAAC;MACzCxE,SAAS,EAAE,CAAC7B,SAAS;IACzB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM8G,cAAc,GAAG;EACnBC,MAAM,EAAE;IAAEhR,sBAAsB,EAAE,OAAO;IAAEE,qBAAqB,EAAE;EAAO;AAC7E,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM+Q,mBAAmB,GAAG;EACxB;EACA/E,eAAe,EAAEvP,OAAO,CAAC,iBAAiB,EAAE;EACxC;EACA;EACA;EACAC,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC;IAAEqU,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CAAC,CAAC,EACnEvU,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;IAAEsU,SAAS,EAAE;EAAO,CAAC,CAAC,CAAC,EAC5CrU,UAAU,CAAC,YAAY,EAAEC,KAAK,CAAC,CAC3BC,OAAO,CAAC,uDAAuD,EAAEH,KAAK,CAAC;IAAEsU,SAAS,EAAE,MAAM;IAAED,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EAC1GjU,KAAK,CAAC,IAAI,EAAEC,YAAY,CAAC,CAAC,EAAE;IAAEkU,QAAQ,EAAE;EAAK,CAAC,CAAC,CAClD,CAAC,EAAEL,cAAc,CAAC,EACnBjU,UAAU,CAAC,sBAAsB,EAAEC,KAAK,CAAC,CACrCC,OAAO,CAAC,0DAA0D,EAAEH,KAAK,CAAC;IAAEqU,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,EAC1FjU,KAAK,CAAC,IAAI,EAAEC,YAAY,CAAC,CAAC,EAAE;IAAEkU,QAAQ,EAAE;EAAK,CAAC,CAAC,CAClD,CAAC,EAAEL,cAAc,CAAC,CACtB;AACL,CAAC;;AAED;AACA;AACA;;AAEA,SAAS5H,eAAe,EAAEC,0BAA0B,EAAEC,0BAA0B,EAAEO,mCAAmC,EAAED,2CAA2C,EAAEM,SAAS,EAAE6F,gBAAgB,EAAEtC,cAAc,EAAElQ,eAAe,EAAEuB,kBAAkB,EAAE+Q,gBAAgB,EAAEW,eAAe,EAAErK,YAAY,EAAED,cAAc,EAAEmJ,cAAc,EAAE9H,eAAe,EAAEyJ,cAAc,EAAEE,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}