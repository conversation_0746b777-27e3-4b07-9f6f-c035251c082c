{"ast": null, "code": "import { move<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CdkDropList, CdkDrag, CdkDragHandle, CdkDragPlaceholder } from '@angular/cdk/drag-drop';\nimport { NgClass } from '@angular/common';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatButtonModule } from '@angular/material/button';\nimport { NgScrollbar } from 'ngx-scrollbar';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/progress-bar\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/tabs\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/input\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/checkbox\";\nimport * as i8 from \"@angular/material/tooltip\";\nfunction DataWidgetComponent_ng_template_754_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 236);\n    i0.ɵɵtext(1, \"Sarah Smith \");\n  }\n}\nfunction DataWidgetComponent_ng_template_835_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 237);\n    i0.ɵɵtext(1, \"Jalpa Joshi \");\n  }\n}\nfunction DataWidgetComponent_ng_template_916_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 238);\n    i0.ɵɵtext(1, \"Mark Peter \");\n  }\n}\nfunction DataWidgetComponent_For_1282_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 249);\n  }\n}\nfunction DataWidgetComponent_For_1282_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 250);\n    i0.ɵɵtext(1, \" arrow_downward \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DataWidgetComponent_For_1282_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 251);\n    i0.ɵɵtext(1, \" arrow_upward \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DataWidgetComponent_For_1282_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 252);\n    i0.ɵɵtext(1, \" remove\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = a0 => ({\n  done: a0\n});\nconst _c1 = (a0, a1, a2) => ({\n  \"task-low\": a0,\n  \"task-high\": a1,\n  \"task-normal\": a2\n});\nfunction DataWidgetComponent_For_1282_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 239)(1, \"div\")(2, \"div\", 240)(3, \"mat-icon\", 241);\n    i0.ɵɵtext(4, \"drag_indicator\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"mat-checkbox\", 242);\n    i0.ɵɵlistener(\"change\", function DataWidgetComponent_For_1282_Template_mat_checkbox_change_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const task_r4 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.toggle(task_r4));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, DataWidgetComponent_For_1282_div_6_Template, 1, 0, \"div\", 243);\n    i0.ɵɵelementStart(7, \"div\", 244);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 245);\n    i0.ɵɵtemplate(10, DataWidgetComponent_For_1282_Conditional_10_Template, 2, 0, \"mat-icon\", 246)(11, DataWidgetComponent_For_1282_Conditional_11_Template, 2, 0, \"mat-icon\", 247)(12, DataWidgetComponent_For_1282_Conditional_12_Template, 2, 0, \"mat-icon\", 248);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r4 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"checked\", !!task_r4.done);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, task_r4.done));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", task_r4.title, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(10, _c1, task_r4.priority === \"Low\", task_r4.priority === \"High\", task_r4.priority === \"Normal\"));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(10, (task_r4 == null ? null : task_r4.priority) === \"Low\" ? 10 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(11, (task_r4 == null ? null : task_r4.priority) === \"High\" ? 11 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(12, (task_r4 == null ? null : task_r4.priority) === \"Normal\" ? 12 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", task_r4.priority, \" \");\n  }\n}\nconst _c2 = () => [\"Home\"];\nexport class DataWidgetComponent {\n  constructor() {\n    // TODO start\n    this.tasks = [{\n      id: '1',\n      title: 'Submit Science Homework',\n      done: true,\n      priority: 'High'\n    }, {\n      id: '2',\n      title: 'Request for festivle holiday',\n      done: false,\n      priority: 'High'\n    }, {\n      id: '3',\n      title: 'Order new java book',\n      done: false,\n      priority: 'Low'\n    }, {\n      id: '4',\n      title: 'Remind for lunch in hotel',\n      done: true,\n      priority: 'Normal'\n    }, {\n      id: '5',\n      title: 'Pay Hostel Fees',\n      done: false,\n      priority: 'High'\n    }, {\n      id: '6',\n      title: 'Attend Seminar On Sunday',\n      done: false,\n      priority: 'Normal'\n    }, {\n      id: '7',\n      title: 'Renew bus pass',\n      done: true,\n      priority: 'High'\n    }, {\n      id: '8',\n      title: 'Issue book in library',\n      done: false,\n      priority: 'High'\n    }, {\n      id: '9',\n      title: 'Project report submit',\n      done: false,\n      priority: 'Low'\n    }];\n    // /constructor\n  }\n  drop(event) {\n    moveItemInArray(this.tasks, event.previousIndex, event.currentIndex);\n  }\n  toggle(task) {\n    task.done = !task.done;\n  }\n  static #_ = this.ɵfac = function DataWidgetComponent_Factory(t) {\n    return new (t || DataWidgetComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DataWidgetComponent,\n    selectors: [[\"app-data-widget\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 1402,\n    vars: 4,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-12\", \"col-sm-12\"], [1, \"card\"], [1, \"header\"], [1, \"tableBody\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [\"mode\", \"determinate\", \"value\", \"30\", \"color\", \"warn\"], [\"mode\", \"determinate\", \"value\", \"55\"], [\"mode\", \"determinate\", \"value\", \"67\"], [\"mode\", \"determinate\", \"value\", \"70\"], [\"mode\", \"determinate\", \"value\", \"24\", \"color\", \"warn\"], [\"mode\", \"determinate\", \"value\", \"77\"], [\"mode\", \"determinate\", \"value\", \"41\", \"color\", \"warn\"], [1, \"col-xs-12\", \"col-sm-12\", \"col-md-4\", \"col-lg-4\"], [1, \"body\"], [1, \"doc-file-type\"], [\"visibility\", \"hover\", 2, \"height\", \"285px\"], [1, \"list-unstyled\"], [1, \"d-flex\", \"mb-3\"], [1, \"msr-3\", \"align-self-center\", \"img-icon\", \"primary-rgba\", \"text-primary\"], [1, \"far\", \"fa-file-word\"], [1, \"set-flex\"], [1, \"font-14\", \"mb-1\"], [1, \"ms-auto\"], [1, \"far\", \"fa-trash-alt\", \"psr-3\"], [1, \"far\", \"fa-arrow-alt-circle-down\"], [1, \"msr-3\", \"align-self-center\", \"img-icon\", \"success-rgba\", \"text-success\"], [1, \"far\", \"fa-file-excel\"], [1, \"msr-3\", \"align-self-center\", \"img-icon\", \"danger-rgba\", \"text-danger\"], [1, \"far\", \"fa-file-pdf\"], [1, \"msr-3\", \"align-self-center\", \"img-icon\", \"info-rgba\", \"text-info\"], [1, \"far\", \"fa-file-archive\"], [1, \"text-center\", \"p-t-20\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\"], [1, \"card-body\"], [\"visibility\", \"hover\", 2, \"height\", \"335px\"], [1, \"list-unstyled\", \"user-progress\", \"list-unstyled-border\", \"list-unstyled-noborder\"], [1, \"lecture-list\"], [\"alt\", \"image\", \"width\", \"40\", \"src\", \"assets/images/user/usrbig1.jpg\", 1, \"msr-3\", \"rounded-circle\"], [1, \"media-title\", \"font-14\", \"font-b-500\"], [1, \"text-job\", \"text-muted\", \"mb-0\", \"font-12\"], [\"alt\", \"image\", \"width\", \"40\", \"src\", \"assets/images/user/usrbig2.jpg\", 1, \"msr-3\", \"rounded-circle\"], [\"alt\", \"image\", \"width\", \"40\", \"src\", \"assets/images/user/usrbig3.jpg\", 1, \"msr-3\", \"rounded-circle\"], [\"alt\", \"image\", \"width\", \"40\", \"src\", \"assets/images/user/usrbig4.jpg\", 1, \"msr-3\", \"rounded-circle\"], [\"alt\", \"image\", \"width\", \"40\", \"src\", \"assets/images/user/usrbig5.jpg\", 1, \"msr-3\", \"rounded-circle\"], [\"alt\", \"image\", \"width\", \"40\", \"src\", \"assets/images/user/usrbig6.jpg\", 1, \"msr-3\", \"rounded-circle\"], [1, \"row\", \"clearfix\"], [1, \"col-xs-12\", \"col-sm-12\", \"col-md-8\", \"col-lg-8\"], [1, \"review-block\"], [1, \"review-img\"], [\"src\", \"assets/images/user/user1.jpg\", \"alt\", \"\"], [1, \"col\"], [1, \"m-b-0\", \"m-t-5\"], [1, \"float-end\", \"m-r-10\", \"text-muted\", \"font-12\"], [1, \"material-icons\"], [1, \"m-t-10\", \"m-b-15\", \"text-muted\"], [\"href\", \"#!\"], [1, \"material-icons\", \"m-r-10\"], [1, \"material-icons\", \"m-r-10\", \"col-red\"], [\"src\", \"assets/images/user/user2.jpg\", \"alt\", \"\"], [1, \"text-center\", \"m-b-5\"], [\"href\", \"#!\", 1, \"b-b-primary\", \"text-primary\"], [1, \"col-md-4\", \"col-sm-12\", \"col-12\"], [1, \"totalEarning\"], [\"id\", \"skills\", 1, \"tab-pane\", \"body\"], [1, \"mb-2\"], [1, \"progress-label\"], [1, \"float-end\", \"progress-percent\", \"label\", \"label-info\", \"m-b-5\"], [1, \"progress\", \"skill-progress\", \"m-b-20\", \"w-100\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"45\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-green\", \"width-per-45\"], [1, \"float-start\", \"progress-label\"], [1, \"float-end\", \"progress-percent\", \"label\", \"label-danger\", \"m-b-5\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"27\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-purple\", \"width-per-27\"], [1, \"float-end\", \"progress-percent\", \"label\", \"label-primary\", \"m-b-5\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"25\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-orange\", \"width-per-25\"], [1, \"float-end\", \"progress-percent\", \"label\", \"label-success\", \"m-b-5\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"18\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-cyan\", \"width-per-18\"], [1, \"float-end\", \"progress-percent\", \"label\", \"label-warning\", \"m-b-5\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"13\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-red\", \"width-per-13\"], [1, \"col-lg-4\", \"col-md-4\", \"col-sm-4\", \"col-xs-12\"], [1, \"box-part\", \"text-center\"], [1, \"fab\", \"fa-twitter\", \"fa-3x\", \"col-blue\"], [1, \"title\", \"p-t-15\"], [1, \"text\", \"p-b-10\"], [\"href\", \"#\"], [1, \"fab\", \"fa-instagram\", \"fa-3x\", \"col-red\"], [1, \"fab\", \"fa-facebook-f\", \"fa-3x\", \"col-blue\"], [1, \"col-xs-12\", \"col-sm-12\", \"col-md-6\", \"col-lg-6\"], [\"visibility\", \"hover\", 2, \"height\", \"400px\"], [1, \"assign-style\"], [1, \"feedBody\"], [1, \"active-feed\"], [1, \"feed-user-img\"], [\"src\", \"assets/images/user/user1.jpg\", \"alt\", \"User-Profile-Image\", 1, \"img-radius\"], [1, \"feedLblStyle\", \"lblFileStyle\"], [1, \"text-muted\", \"float-end\"], [1, \"m-b-15\", \"m-t-15\"], [1, \"diactive-feed\"], [\"src\", \"assets/images/user/user2.jpg\", \"alt\", \"User-Profile-Image\", 1, \"img-radius\"], [1, \"feedLblStyle\", \"lblTaskStyle\"], [\"src\", \"assets/images/user/user3.jpg\", \"alt\", \"User-Profile-Image\", 1, \"img-radius\"], [1, \"feedLblStyle\", \"lblCommentStyle\"], [\"src\", \"assets/images/user/user4.jpg\", \"alt\", \"User-Profile-Image\", 1, \"img-radius\"], [1, \"feedLblStyle\", \"lblReplyStyle\"], [\"src\", \"assets/images/user/user5.jpg\", \"alt\", \"User-Profile-Image\", 1, \"img-radius\"], [\"src\", \"assets/images/user/user6.jpg\", \"alt\", \"User-Profile-Image\", 1, \"img-radius\"], [1, \"table\", \"table-hover\", \"dashboard-task-infos\"], [1, \"table-img\"], [1, \"label\", \"l-bg-green\", \"shadow-style\"], [1, \"progress\", \"shadow-style\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"62\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-green\", \"width-per-62\"], [1, \"label\", \"l-bg-purple\", \"shadow-style\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"40\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-purple\", \"width-per-40\"], [\"src\", \"assets/images/user/user3.jpg\", \"alt\", \"\"], [1, \"label\", \"l-bg-orange\", \"shadow-style\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"72\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-orange\", \"width-per-72\"], [\"src\", \"assets/images/user/user4.jpg\", \"alt\", \"\"], [1, \"label\", \"l-bg-cyan\", \"shadow-style\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"95\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-cyan\", \"width-per-95\"], [\"src\", \"assets/images/user/user5.jpg\", \"alt\", \"\"], [1, \"label\", \"bg-green\", \"shadow-style\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"87\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-green\", \"width-per-87\"], [\"visibility\", \"hover\", 2, \"height\", \"390px\"], [\"id\", \"new-orders\", 1, \"media-list\", \"position-relative\"], [\"id\", \"new-orders-table\", 1, \"table\", \"table-hover\", \"table-xl\", \"mb-0\"], [1, \"border-top-0\"], [1, \"text-truncate\"], [1, \"list-unstyled\", \"order-list\"], [1, \"avatar\", \"avatar-sm\"], [\"src\", \"assets/images/user/user1.jpg\", \"alt\", \"user\", 1, \"rounded-circle\"], [\"src\", \"assets/images/user/user2.jpg\", \"alt\", \"user\", 1, \"rounded-circle\"], [\"src\", \"assets/images/user/user3.jpg\", \"alt\", \"user\", 1, \"rounded-circle\"], [1, \"badge\"], [\"mat-tab-label\", \"\"], [1, \"table-responsive\", \"mt-3\"], [1, \"badge\", \"col-green\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"72\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-green\", \"width-per-72\"], [1, \"badge\", \"col-red\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"62\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-red\", \"width-per-62\"], [1, \"badge\", \"col-purple\"], [1, \"col-lg-6\", \"col-md-6\", \"col-sm-6\", \"col-xs-12\"], [1, \"card\", \"profile-header\"], [1, \"col-lg-4\", \"col-md-4\", \"col-12\"], [1, \"profile-image\", \"float-md-right\"], [\"src\", \"assets/images/user/usrbig6.jpg\", \"alt\", \"\"], [1, \"col-lg-8\", \"col-md-8\", \"col-12\"], [1, \"m-t-0\", \"m-b-0\"], [1, \"job_post\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"btn-border-radius\", \"msr-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-border-radius\"], [\"src\", \"assets/images/user/usrbig1.jpg\", \"alt\", \"\"], [1, \"sl-item\", \"sl-primary\"], [1, \"sl-content\"], [1, \"text-muted\"], [1, \"fa\", \"fa-user\", \"position-left\"], [1, \"sl-item\", \"sl-danger\"], [1, \"sl-item\", \"sl-success\"], [1, \"sl-item\", \"sl-warning\"], [1, \"chat\"], [1, \"chat-header\", \"clearfix\"], [\"src\", \"assets/images/user/user2.jpg\", \"alt\", \"avatar\"], [1, \"chat-about\"], [1, \"chat-with\"], [1, \"chat-num-messages\"], [\"visibility\", \"hover\", 2, \"height\", \"270px\"], [\"id\", \"chat-conversation\", 1, \"chat-history\"], [1, \"clearfix\"], [1, \"message-data\", \"text-end\"], [1, \"message-data-time\"], [1, \"message-data-name\"], [1, \"zmdi\", \"zmdi-circle\", \"me\"], [1, \"message\", \"other-message\", \"float-end\"], [1, \"message-data\"], [1, \"zmdi\", \"zmdi-circle\", \"online\"], [1, \"message\", \"my-message\"], [1, \"chat-message\", \"clearfix\"], [1, \"form-group\"], [1, \"form-line\"], [1, \"example-full-width\"], [\"matInput\", \"\", \"placeholder\", \"Enter text here..\"], [1, \"card-block\"], [1, \"row\", \"m-b-20\"], [1, \"col-auto\", \"p-r-0\"], [\"src\", \"assets/images/posts/post1.jpg\", \"alt\", \"user image\", 1, \"latest-posts-img\"], [1, \"text-muted\", \"m-b-5\", \"font-12\"], [\"src\", \"assets/images/posts/post2.jpg\", \"alt\", \"user image\", 1, \"latest-posts-img\"], [\"src\", \"assets/images/posts/post3.jpg\", \"alt\", \"user image\", 1, \"latest-posts-img\"], [1, \"text-center\"], [1, \"recent-comment\"], [1, \"notice-board\"], [\"src\", \"assets/images/user/user6.jpg\", \"alt\", \"...\", 1, \"notice-object\"], [1, \"notice-body\"], [1, \"notice-heading\", \"col-green\"], [\"src\", \"assets/images/user/user4.jpg\", \"alt\", \"...\", 1, \"notice-object\"], [1, \"notice-heading\", \"color-primary\", \"col-indigo\"], [1, \"comment-date\"], [\"src\", \"assets/images/user/user3.jpg\", \"alt\", \"...\", 1, \"notice-object\"], [1, \"notice-heading\", \"color-danger\", \"col-cyan\"], [1, \"notice-board\", \"no-border\"], [\"src\", \"assets/images/user/user7.jpg\", \"alt\", \"...\", 1, \"notice-object\"], [1, \"notice-heading\", \"color-info\", \"col-orange\"], [1, \"timeline\"], [1, \"timeline-badge\", \"primary\"], [\"src\", \"assets/images/user/user1.jpg\", \"alt\", \"...\", 1, \"notice-object\"], [1, \"timeline-panel\"], [1, \"timeline-heading\"], [1, \"timeline-title\"], [1, \"timeline-body\"], [\"src\", \"assets/images/user/user2.jpg\", \"alt\", \"...\", 1, \"notice-object\"], [\"src\", \"assets/images/user/user8.jpg\", \"alt\", \"...\", 1, \"notice-object\"], [\"visibility\", \"hover\", 2, \"height\", \"370px\"], [\"cdkDropList\", \"\", 1, \"task-list\", 3, \"cdkDropListDropped\"], [1, \"table\", \"table-borderless\", \"medicine-list\"], [1, \"fas\", \"fa-tablets\", \"pill-style\"], [1, \"text-end\", \"w-25\"], [1, \"badge-outline\"], [1, \"fas\", \"fa-capsules\", \"pill-style\"], [1, \"fas\", \"fa-syringe\", \"pill-style\"], [1, \"fas\", \"fa-pills\", \"pill-style\"], [1, \"plain-card\"], [1, \"card-inner\"], [1, \"tx-primary\", \"m-b-15\"], [1, \"row\", \"mb-2\"], [1, \"col-4\"], [1, \"font-16\", \"font-b-500\"], [1, \"material-icons\", \"col-green\", \"font-20\"], [1, \"material-icons\", \"col-orange\", \"font-20\"], [1, \"progress\", \"shadow-style\", \"mb-2\", \"mt-3\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"70\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-green\", \"width-per-70\"], [1, \"plain-card\", \"mt-4\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"50\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"l-bg-red\", \"width-per-50\"], [\"src\", \"assets/images/user/user1.jpg\", \"alt\", \"user\"], [\"src\", \"assets/images/user/user2.jpg\", \"alt\", \"user\"], [\"src\", \"assets/images/user/user3.jpg\", \"alt\", \"user\"], [\"cdkDrag\", \"\", 1, \"task-box\"], [\"cdkDragHandle\", \"\", 1, \"task-handle\", \"m-r-20\"], [\"aria-hidden\", \"false\"], [\"color\", \"primary\", 1, \"m-r-15\", 3, \"checked\", \"change\"], [\"class\", \"task-custom-placeholder\", 4, \"cdkDragPlaceholder\"], [\"matTooltip\", \"Title\", 3, \"ngClass\"], [3, \"ngClass\"], [\"matTooltip\", \"Low\", \"aria-hidden\", \"false\", \"class\", \"lbl-low\"], [\"matTooltip\", \"High\", \"aria-hidden\", \"false\", \"class\", \"lbl-high\"], [\"matTooltip\", \"Normal\", \"aria-hidden\", \"false\", \"class\", \"lbl-normal\"], [1, \"task-custom-placeholder\"], [\"matTooltip\", \"Low\", \"aria-hidden\", \"false\", 1, \"lbl-low\"], [\"matTooltip\", \"High\", \"aria-hidden\", \"false\", 1, \"lbl-high\"], [\"matTooltip\", \"Normal\", \"aria-hidden\", \"false\", 1, \"lbl-normal\"], [\"class\", \"task-box\", \"cdkDrag\", \"\"]],\n    template: function DataWidgetComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\");\n        i0.ɵɵtext(9, \"Your Progress\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"table\", 10)(13, \"thead\")(14, \"tr\")(15, \"th\");\n        i0.ɵɵtext(16, \"Subject\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"th\");\n        i0.ɵɵtext(18, \"Progress\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"th\");\n        i0.ɵɵtext(20, \"Duration\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(21, \"tbody\")(22, \"tr\")(23, \"td\");\n        i0.ɵɵtext(24, \"Chemistry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"td\");\n        i0.ɵɵtext(26, \"30%\");\n        i0.ɵɵelement(27, \"mat-progress-bar\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"td\");\n        i0.ɵɵtext(29, \"2 Months\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"tr\")(31, \"td\");\n        i0.ɵɵtext(32, \"Mathematics\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"td\");\n        i0.ɵɵtext(34, \"55%\");\n        i0.ɵɵelement(35, \"mat-progress-bar\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"td\");\n        i0.ɵɵtext(37, \"3 Months\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"tr\")(39, \"td\");\n        i0.ɵɵtext(40, \"Painting\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"td\");\n        i0.ɵɵtext(42, \"67%\");\n        i0.ɵɵelement(43, \"mat-progress-bar\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"td\");\n        i0.ɵɵtext(45, \"1 Months\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(46, \"tr\")(47, \"td\");\n        i0.ɵɵtext(48, \"Business studies\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"td\");\n        i0.ɵɵtext(50, \"70%\");\n        i0.ɵɵelement(51, \"mat-progress-bar\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"td\");\n        i0.ɵɵtext(53, \"2 Months\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"tr\")(55, \"td\");\n        i0.ɵɵtext(56, \"Biology\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"td\");\n        i0.ɵɵtext(58, \"24%\");\n        i0.ɵɵelement(59, \"mat-progress-bar\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"td\");\n        i0.ɵɵtext(61, \"3 Months\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(62, \"tr\")(63, \"td\");\n        i0.ɵɵtext(64, \"Computer studies\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(65, \"td\");\n        i0.ɵɵtext(66, \"77%\");\n        i0.ɵɵelement(67, \"mat-progress-bar\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"td\");\n        i0.ɵɵtext(69, \"4 Months\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(70, \"tr\")(71, \"td\");\n        i0.ɵɵtext(72, \"Geography\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(73, \"td\");\n        i0.ɵɵtext(74, \"41%\");\n        i0.ɵɵelement(75, \"mat-progress-bar\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(76, \"td\");\n        i0.ɵɵtext(77, \"2 Months\");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(78, \"div\", 18)(79, \"div\", 6)(80, \"div\", 7)(81, \"h2\");\n        i0.ɵɵtext(82, \"Assignments\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(83, \"div\", 19)(84, \"div\", 20)(85, \"ng-scrollbar\", 21)(86, \"ul\", 22)(87, \"li\", 23)(88, \"span\", 24);\n        i0.ɵɵelement(89, \"i\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(90, \"div\", 26)(91, \"h5\", 27);\n        i0.ɵɵtext(92, \"Java Programming\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(93, \"p\");\n        i0.ɵɵtext(94, \".doc, 4.3 MB\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(95, \"div\", 28)(96, \"td\");\n        i0.ɵɵelement(97, \"i\", 29)(98, \"i\", 30);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(99, \"li\", 23)(100, \"span\", 31);\n        i0.ɵɵelement(101, \"i\", 32);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(102, \"div\", 26)(103, \"h5\", 27);\n        i0.ɵɵtext(104, \"Angular Theory\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(105, \"p\");\n        i0.ɵɵtext(106, \".xls, 2.5 MB\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(107, \"div\", 28)(108, \"td\");\n        i0.ɵɵelement(109, \"i\", 29)(110, \"i\", 30);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(111, \"li\", 23)(112, \"span\", 33);\n        i0.ɵɵelement(113, \"i\", 34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"div\", 26)(115, \"h5\", 27);\n        i0.ɵɵtext(116, \"Maths Sums Solution\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(117, \"p\");\n        i0.ɵɵtext(118, \".pdf, 10.5 MB\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(119, \"div\", 28)(120, \"td\");\n        i0.ɵɵelement(121, \"i\", 29)(122, \"i\", 30);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(123, \"li\", 23)(124, \"span\", 35);\n        i0.ɵɵelement(125, \"i\", 36);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(126, \"div\", 26)(127, \"h5\", 27);\n        i0.ɵɵtext(128, \"Submit Science Journal\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(129, \"p\");\n        i0.ɵɵtext(130, \".zip, 53.2 MB\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(131, \"div\", 28)(132, \"td\");\n        i0.ɵɵelement(133, \"i\", 29)(134, \"i\", 30);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(135, \"li\", 23)(136, \"span\", 24);\n        i0.ɵɵelement(137, \"i\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(138, \"div\", 26)(139, \"h5\", 27);\n        i0.ɵɵtext(140, \"Marketing Instructions\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(141, \"p\");\n        i0.ɵɵtext(142, \".doc, 5.3 MB\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(143, \"div\", 28)(144, \"td\");\n        i0.ɵɵelement(145, \"i\", 29)(146, \"i\", 30);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(147, \"div\", 37)(148, \"button\", 38);\n        i0.ɵɵtext(149, \"More Records\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(150, \"div\", 5)(151, \"div\", 6)(152, \"div\", 7)(153, \"h2\");\n        i0.ɵɵtext(154, \" Upcomming Appointments \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(155, \"div\", 39)(156, \"ng-scrollbar\", 40)(157, \"ul\", 41)(158, \"li\", 42);\n        i0.ɵɵelement(159, \"img\", 43);\n        i0.ɵɵelementStart(160, \"div\", 26)(161, \"div\", 44);\n        i0.ɵɵtext(162, \"Cara Stevens\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(163, \"div\", 45);\n        i0.ɵɵtext(164, \"M.B.B.S.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(165, \"div\", 28)(166, \"div\", 44);\n        i0.ɵɵtext(167, \"12 June '20\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(168, \"div\", 45);\n        i0.ɵɵtext(169, \"09:00-10:00\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(170, \"li\", 42);\n        i0.ɵɵelement(171, \"img\", 46);\n        i0.ɵɵelementStart(172, \"div\", 26)(173, \"div\", 44);\n        i0.ɵɵtext(174, \"Airi Satou\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(175, \"div\", 45);\n        i0.ɵɵtext(176, \"M.S.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(177, \"div\", 28)(178, \"div\", 44);\n        i0.ɵɵtext(179, \"13 June '20\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(180, \"div\", 45);\n        i0.ɵɵtext(181, \"11:00-12:00\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(182, \"li\", 42);\n        i0.ɵɵelement(183, \"img\", 47);\n        i0.ɵɵelementStart(184, \"div\", 26)(185, \"div\", 44);\n        i0.ɵɵtext(186, \"Jens Brincker\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(187, \"div\", 45);\n        i0.ɵɵtext(188, \"Geography\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(189, \"div\", 28)(190, \"div\", 44);\n        i0.ɵɵtext(191, \"15 June '20\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(192, \"div\", 45);\n        i0.ɵɵtext(193, \"09:30-10:30\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(194, \"li\", 42);\n        i0.ɵɵelement(195, \"img\", 48);\n        i0.ɵɵelementStart(196, \"div\", 26)(197, \"div\", 44);\n        i0.ɵɵtext(198, \"Angelica Ramos\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(199, \"div\", 45);\n        i0.ɵɵtext(200, \"B.H.M.S.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(201, \"div\", 28)(202, \"div\", 44);\n        i0.ɵɵtext(203, \"16 June '20\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(204, \"div\", 45);\n        i0.ɵɵtext(205, \"14:00-15:00\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(206, \"li\", 42);\n        i0.ɵɵelement(207, \"img\", 49);\n        i0.ɵɵelementStart(208, \"div\", 26)(209, \"div\", 44);\n        i0.ɵɵtext(210, \"Cara Stevens\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(211, \"div\", 45);\n        i0.ɵɵtext(212, \"M.B.B.S.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(213, \"div\", 28)(214, \"div\", 44);\n        i0.ɵɵtext(215, \"18 June '20\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(216, \"div\", 45);\n        i0.ɵɵtext(217, \"11:00-12:30\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(218, \"li\", 42);\n        i0.ɵɵelement(219, \"img\", 50);\n        i0.ɵɵelementStart(220, \"div\", 26)(221, \"div\", 44);\n        i0.ɵɵtext(222, \"Jacob Ryan\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(223, \"div\", 45);\n        i0.ɵɵtext(224, \"M.D.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(225, \"div\", 28)(226, \"div\", 44);\n        i0.ɵɵtext(227, \"22 June '20\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(228, \"div\", 45);\n        i0.ɵɵtext(229, \"13:00-14:15\");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(230, \"div\", 51)(231, \"div\", 52)(232, \"div\", 6)(233, \"div\", 7)(234, \"h2\");\n        i0.ɵɵtext(235, \"Customer Review\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(236, \"div\", 19)(237, \"div\", 53)(238, \"div\", 4)(239, \"div\", 54);\n        i0.ɵɵelement(240, \"img\", 55);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(241, \"div\", 56)(242, \"h6\", 57);\n        i0.ɵɵtext(243, \"Alis Smith \");\n        i0.ɵɵelementStart(244, \"span\", 58);\n        i0.ɵɵtext(245, \" a week ago\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(246, \"i\", 59);\n        i0.ɵɵtext(247, \"star\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(248, \"i\", 59);\n        i0.ɵɵtext(249, \"star\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(250, \"i\", 59);\n        i0.ɵɵtext(251, \"star\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(252, \"i\", 59);\n        i0.ɵɵtext(253, \"star_half\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(254, \"i\", 59);\n        i0.ɵɵtext(255, \"star_border\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(256, \"p\", 60);\n        i0.ɵɵtext(257, \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vel rutrum ex, at ornare mi. In quis scelerisque dui, eget rhoncus orci. Fusce et sodales ipsum. Nam id nunc euismod, aliquet arcu quis, mattis nisi.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(258, \"a\", 61)(259, \"i\", 62);\n        i0.ɵɵtext(260, \"thumb_up\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(261, \"a\", 61)(262, \"i\", 63);\n        i0.ɵɵtext(263, \"thumb_down\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(264, \"div\", 4)(265, \"div\", 54);\n        i0.ɵɵelement(266, \"img\", 64);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(267, \"div\", 56)(268, \"h6\", 57);\n        i0.ɵɵtext(269, \"John Dio \");\n        i0.ɵɵelementStart(270, \"span\", 58);\n        i0.ɵɵtext(271, \" a week ago\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(272, \"i\", 59);\n        i0.ɵɵtext(273, \"star\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(274, \"i\", 59);\n        i0.ɵɵtext(275, \"star_half\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(276, \"i\", 59);\n        i0.ɵɵtext(277, \"star_border\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(278, \"i\", 59);\n        i0.ɵɵtext(279, \"star_border\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(280, \"i\", 59);\n        i0.ɵɵtext(281, \"star_border\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(282, \"p\", 60);\n        i0.ɵɵtext(283, \"Nam quis ligula est. Nunc sed risus non turpis tristique tempor. Ut sollicitudin faucibus magna nec gravida. Suspendisse ullamcorper justo vel porta imperdiet. Nunc nec ipsum vel augue placerat faucibus. \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(284, \"a\", 61)(285, \"i\", 62);\n        i0.ɵɵtext(286, \"thumb_up\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(287, \"a\", 61)(288, \"i\", 63);\n        i0.ɵɵtext(289, \"thumb_down\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(290, \"div\", 65)(291, \"a\", 66);\n        i0.ɵɵtext(292, \"View all Customer Reviews\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(293, \"div\", 67)(294, \"div\", 6)(295, \"div\", 7)(296, \"h2\");\n        i0.ɵɵtext(297, \"Earning Source\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(298, \"div\", 19)(299, \"div\", 68)(300, \"h4\");\n        i0.ɵɵtext(301, \"$90,808\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(302, \"div\", 69)(303, \"ul\", 22)(304, \"li\")(305, \"div\", 70)(306, \"span\", 71);\n        i0.ɵɵtext(307, \"envato.com\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(308, \"span\", 72);\n        i0.ɵɵtext(309, \"17%\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(310, \"div\", 73);\n        i0.ɵɵelement(311, \"div\", 74);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(312, \"li\")(313, \"div\", 70)(314, \"span\", 75);\n        i0.ɵɵtext(315, \"google.com\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(316, \"span\", 76);\n        i0.ɵɵtext(317, \"27%\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(318, \"div\", 73);\n        i0.ɵɵelement(319, \"div\", 77);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(320, \"li\")(321, \"div\", 70)(322, \"span\", 75);\n        i0.ɵɵtext(323, \"yahoo.com\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(324, \"span\", 78);\n        i0.ɵɵtext(325, \"25%\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(326, \"div\", 73);\n        i0.ɵɵelement(327, \"div\", 79);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(328, \"li\")(329, \"div\", 70)(330, \"span\", 75);\n        i0.ɵɵtext(331, \"store\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(332, \"span\", 80);\n        i0.ɵɵtext(333, \"18%\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(334, \"div\", 73);\n        i0.ɵɵelement(335, \"div\", 81);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(336, \"li\")(337, \"div\", 70)(338, \"span\", 75);\n        i0.ɵɵtext(339, \"Others\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(340, \"span\", 82);\n        i0.ɵɵtext(341, \"13%\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(342, \"div\", 73);\n        i0.ɵɵelement(343, \"div\", 83);\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(344, \"div\", 51)(345, \"div\", 84)(346, \"div\", 85);\n        i0.ɵɵelement(347, \"i\", 86);\n        i0.ɵɵelementStart(348, \"div\", 87)(349, \"h4\");\n        i0.ɵɵtext(350, \"Twitter\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(351, \"div\", 88)(352, \"span\");\n        i0.ɵɵtext(353, \"Lorem ipsum dolor sit amet, id quo eruditi eloquentiam. Assum decore te sed. Elitr scripta ocurreret qui ad.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(354, \"a\", 89);\n        i0.ɵɵtext(355, \"Learn More\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(356, \"div\", 84)(357, \"div\", 85);\n        i0.ɵɵelement(358, \"i\", 90);\n        i0.ɵɵelementStart(359, \"div\", 87)(360, \"h4\");\n        i0.ɵɵtext(361, \"Instagram\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(362, \"div\", 88)(363, \"span\");\n        i0.ɵɵtext(364, \"Lorem ipsum dolor sit amet, id quo eruditi eloquentiam. Assum decore te sed. Elitr scripta ocurreret qui ad.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(365, \"a\", 89);\n        i0.ɵɵtext(366, \"Learn More\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(367, \"div\", 84)(368, \"div\", 85);\n        i0.ɵɵelement(369, \"i\", 91);\n        i0.ɵɵelementStart(370, \"div\", 87)(371, \"h4\");\n        i0.ɵɵtext(372, \"Facebook\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(373, \"div\", 88)(374, \"span\");\n        i0.ɵɵtext(375, \"Lorem ipsum dolor sit amet, id quo eruditi eloquentiam. Assum decore te sed. Elitr scripta ocurreret qui ad.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(376, \"a\", 89);\n        i0.ɵɵtext(377, \"Learn More\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(378, \"div\", 51)(379, \"div\", 92)(380, \"div\", 6)(381, \"div\", 7)(382, \"h2\");\n        i0.ɵɵtext(383, \"Activity\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(384, \"div\", 19)(385, \"ng-scrollbar\", 93)(386, \"div\", 94)(387, \"ul\", 95)(388, \"li\", 96)(389, \"div\", 97);\n        i0.ɵɵelement(390, \"img\", 98);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(391, \"h6\")(392, \"span\", 99);\n        i0.ɵɵtext(393, \"File\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(394, \" Sarah Smith \");\n        i0.ɵɵelementStart(395, \"small\", 100);\n        i0.ɵɵtext(396, \"6 hours ago\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(397, \"p\", 101);\n        i0.ɵɵtext(398, \" hii John, I have upload doc related to task. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(399, \"li\", 102)(400, \"div\", 97);\n        i0.ɵɵelement(401, \"img\", 103);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(402, \"h6\")(403, \"span\", 104);\n        i0.ɵɵtext(404, \"Task \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(405, \" Jalpa Joshi \");\n        i0.ɵɵelementStart(406, \"small\", 100);\n        i0.ɵɵtext(407, \"5 hours ago \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(408, \"p\", 101);\n        i0.ɵɵtext(409, \" Please do as specify. Let me know if you have any query. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(410, \"li\", 102)(411, \"div\", 97);\n        i0.ɵɵelement(412, \"img\", 105);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(413, \"h6\")(414, \"span\", 106);\n        i0.ɵɵtext(415, \"comment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(416, \" Lina Smith \");\n        i0.ɵɵelementStart(417, \"small\", 100);\n        i0.ɵɵtext(418, \"6 hours ago\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(419, \"p\", 101);\n        i0.ɵɵtext(420, \" Hey, How are you?? \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(421, \"li\", 96)(422, \"div\", 97);\n        i0.ɵɵelement(423, \"img\", 107);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(424, \"h6\")(425, \"span\", 108);\n        i0.ɵɵtext(426, \"Reply\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(427, \" Jacob Ryan \");\n        i0.ɵɵelementStart(428, \"small\", 100);\n        i0.ɵɵtext(429, \"7 hours ago\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(430, \"p\", 101);\n        i0.ɵɵtext(431, \" I am fine. You?? \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(432, \"li\", 96)(433, \"div\", 97);\n        i0.ɵɵelement(434, \"img\", 109);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(435, \"h6\")(436, \"span\", 99);\n        i0.ɵɵtext(437, \"File\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(438, \" Sarah Smith \");\n        i0.ɵɵelementStart(439, \"small\", 100);\n        i0.ɵɵtext(440, \"6 hours ago\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(441, \"p\", 101);\n        i0.ɵɵtext(442, \" hii John, I have upload doc related to task. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(443, \"li\", 102)(444, \"div\", 97);\n        i0.ɵɵelement(445, \"img\", 110);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(446, \"h6\")(447, \"span\", 104);\n        i0.ɵɵtext(448, \"Task \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(449, \" Jalpa Joshi \");\n        i0.ɵɵelementStart(450, \"small\", 100);\n        i0.ɵɵtext(451, \"5 hours ago \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(452, \"p\", 101);\n        i0.ɵɵtext(453, \" Please do as specify. Let me know if you have any query. \");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(454, \"div\", 92)(455, \"div\", 6)(456, \"div\", 7)(457, \"h2\");\n        i0.ɵɵtext(458, \"Assign Task\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(459, \"div\", 8)(460, \"div\", 9)(461, \"table\", 111)(462, \"thead\")(463, \"tr\")(464, \"th\");\n        i0.ɵɵtext(465, \"User\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(466, \"th\");\n        i0.ɵɵtext(467, \"Task\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(468, \"th\");\n        i0.ɵɵtext(469, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(470, \"th\");\n        i0.ɵɵtext(471, \"Manager\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(472, \"th\");\n        i0.ɵɵtext(473, \"Progress\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(474, \"tbody\")(475, \"tr\")(476, \"td\", 112);\n        i0.ɵɵelement(477, \"img\", 55);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(478, \"td\");\n        i0.ɵɵtext(479, \"Task A\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(480, \"td\")(481, \"span\", 113);\n        i0.ɵɵtext(482, \"Doing\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(483, \"td\");\n        i0.ɵɵtext(484, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(485, \"td\")(486, \"div\", 114);\n        i0.ɵɵelement(487, \"div\", 115);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(488, \"tr\")(489, \"td\", 112);\n        i0.ɵɵelement(490, \"img\", 64);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(491, \"td\");\n        i0.ɵɵtext(492, \"Task B\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(493, \"td\")(494, \"span\", 116);\n        i0.ɵɵtext(495, \"To Do\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(496, \"td\");\n        i0.ɵɵtext(497, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(498, \"td\")(499, \"div\", 114);\n        i0.ɵɵelement(500, \"div\", 117);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(501, \"tr\")(502, \"td\", 112);\n        i0.ɵɵelement(503, \"img\", 118);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(504, \"td\");\n        i0.ɵɵtext(505, \"Task C\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(506, \"td\")(507, \"span\", 119);\n        i0.ɵɵtext(508, \"On Hold\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(509, \"td\");\n        i0.ɵɵtext(510, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(511, \"td\")(512, \"div\", 114);\n        i0.ɵɵelement(513, \"div\", 120);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(514, \"tr\")(515, \"td\", 112);\n        i0.ɵɵelement(516, \"img\", 121);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(517, \"td\");\n        i0.ɵɵtext(518, \"Task D\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(519, \"td\")(520, \"span\", 122);\n        i0.ɵɵtext(521, \"Wait Approvel\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(522, \"td\");\n        i0.ɵɵtext(523, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(524, \"td\")(525, \"div\", 114);\n        i0.ɵɵelement(526, \"div\", 123);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(527, \"tr\")(528, \"td\", 112);\n        i0.ɵɵelement(529, \"img\", 124);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(530, \"td\");\n        i0.ɵɵtext(531, \"Task E\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(532, \"td\")(533, \"span\", 125);\n        i0.ɵɵtext(534, \"Suspended\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(535, \"td\");\n        i0.ɵɵtext(536, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(537, \"td\")(538, \"div\", 114);\n        i0.ɵɵelement(539, \"div\", 126);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(540, \"tr\")(541, \"td\", 112);\n        i0.ɵɵelement(542, \"img\", 55);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(543, \"td\");\n        i0.ɵɵtext(544, \"Task A\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(545, \"td\")(546, \"span\", 125);\n        i0.ɵɵtext(547, \"Doing\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(548, \"td\");\n        i0.ɵɵtext(549, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(550, \"td\")(551, \"div\", 114);\n        i0.ɵɵelement(552, \"div\", 115);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(553, \"tr\")(554, \"td\", 112);\n        i0.ɵɵelement(555, \"img\", 64);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(556, \"td\");\n        i0.ɵɵtext(557, \"Task B\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(558, \"td\")(559, \"span\", 116);\n        i0.ɵɵtext(560, \"To Do\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(561, \"td\");\n        i0.ɵɵtext(562, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(563, \"td\")(564, \"div\", 114);\n        i0.ɵɵelement(565, \"div\", 117);\n        i0.ɵɵelementEnd()()()()()()()()()();\n        i0.ɵɵelementStart(566, \"div\", 4)(567, \"div\", 92)(568, \"div\", 6)(569, \"div\", 7)(570, \"h2\");\n        i0.ɵɵtext(571, \"New Orders\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(572, \"div\", 19)(573, \"ng-scrollbar\", 127)(574, \"div\", 128)(575, \"div\", 9)(576, \"table\", 129)(577, \"thead\")(578, \"tr\")(579, \"th\", 130);\n        i0.ɵɵtext(580, \"Product\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(581, \"th\", 130);\n        i0.ɵɵtext(582, \"Customers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(583, \"th\", 130);\n        i0.ɵɵtext(584, \"Total\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(585, \"tbody\")(586, \"tr\")(587, \"td\", 131);\n        i0.ɵɵtext(588, \"iPhone X\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(589, \"td\", 131)(590, \"ul\", 132)(591, \"li\", 133);\n        i0.ɵɵelement(592, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(593, \"li\", 133);\n        i0.ɵɵelement(594, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(595, \"li\", 133);\n        i0.ɵɵelement(596, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(597, \"li\", 133)(598, \"span\", 137);\n        i0.ɵɵtext(599, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(600, \"td\", 131);\n        i0.ɵɵtext(601, \"$8999\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(602, \"tr\")(603, \"td\", 131);\n        i0.ɵɵtext(604, \"Pixel 2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(605, \"td\", 131)(606, \"ul\", 132)(607, \"li\", 133);\n        i0.ɵɵelement(608, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(609, \"li\", 133);\n        i0.ɵɵelement(610, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(611, \"li\", 133);\n        i0.ɵɵelement(612, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(613, \"li\", 133)(614, \"span\", 137);\n        i0.ɵɵtext(615, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(616, \"td\", 131);\n        i0.ɵɵtext(617, \"$5550\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(618, \"tr\")(619, \"td\", 131);\n        i0.ɵɵtext(620, \"OnePlus\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(621, \"td\", 131)(622, \"ul\", 132)(623, \"li\", 133);\n        i0.ɵɵelement(624, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(625, \"li\", 133);\n        i0.ɵɵelement(626, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(627, \"li\", 133);\n        i0.ɵɵelement(628, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(629, \"li\", 133)(630, \"span\", 137);\n        i0.ɵɵtext(631, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(632, \"td\", 131);\n        i0.ɵɵtext(633, \"$9000\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(634, \"tr\")(635, \"td\", 131);\n        i0.ɵɵtext(636, \"Galaxy\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(637, \"td\", 131)(638, \"ul\", 132)(639, \"li\", 133);\n        i0.ɵɵelement(640, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(641, \"li\", 133);\n        i0.ɵɵelement(642, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(643, \"li\", 133);\n        i0.ɵɵelement(644, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(645, \"li\", 133)(646, \"span\", 137);\n        i0.ɵɵtext(647, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(648, \"td\", 131);\n        i0.ɵɵtext(649, \"$7500\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(650, \"tr\")(651, \"td\", 131);\n        i0.ɵɵtext(652, \"Moto Z2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(653, \"td\", 131)(654, \"ul\", 132)(655, \"li\", 133);\n        i0.ɵɵelement(656, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(657, \"li\", 133);\n        i0.ɵɵelement(658, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(659, \"li\", 133);\n        i0.ɵɵelement(660, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(661, \"li\", 133)(662, \"span\", 137);\n        i0.ɵɵtext(663, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(664, \"td\", 131);\n        i0.ɵɵtext(665, \"$8500\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(666, \"tr\")(667, \"td\", 131);\n        i0.ɵɵtext(668, \"iPhone X\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(669, \"td\", 131)(670, \"ul\", 132)(671, \"li\", 133);\n        i0.ɵɵelement(672, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(673, \"li\", 133);\n        i0.ɵɵelement(674, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(675, \"li\", 133);\n        i0.ɵɵelement(676, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(677, \"li\", 133)(678, \"span\", 137);\n        i0.ɵɵtext(679, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(680, \"td\", 131);\n        i0.ɵɵtext(681, \"$8999\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(682, \"tr\")(683, \"td\", 131);\n        i0.ɵɵtext(684, \"iPhone X\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(685, \"td\", 131)(686, \"ul\", 132)(687, \"li\", 133);\n        i0.ɵɵelement(688, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(689, \"li\", 133);\n        i0.ɵɵelement(690, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(691, \"li\", 133);\n        i0.ɵɵelement(692, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(693, \"li\", 133)(694, \"span\", 137);\n        i0.ɵɵtext(695, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(696, \"td\", 131);\n        i0.ɵɵtext(697, \"$8999\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(698, \"tr\")(699, \"td\", 131);\n        i0.ɵɵtext(700, \"Pixel 2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(701, \"td\", 131)(702, \"ul\", 132)(703, \"li\", 133);\n        i0.ɵɵelement(704, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(705, \"li\", 133);\n        i0.ɵɵelement(706, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(707, \"li\", 133);\n        i0.ɵɵelement(708, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(709, \"li\", 133)(710, \"span\", 137);\n        i0.ɵɵtext(711, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(712, \"td\", 131);\n        i0.ɵɵtext(713, \"$5550\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(714, \"tr\")(715, \"td\", 131);\n        i0.ɵɵtext(716, \"OnePlus\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(717, \"td\", 131)(718, \"ul\", 132)(719, \"li\", 133);\n        i0.ɵɵelement(720, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(721, \"li\", 133);\n        i0.ɵɵelement(722, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(723, \"li\", 133);\n        i0.ɵɵelement(724, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(725, \"li\", 133)(726, \"span\", 137);\n        i0.ɵɵtext(727, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(728, \"td\", 131);\n        i0.ɵɵtext(729, \"$9000\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(730, \"tr\")(731, \"td\", 131);\n        i0.ɵɵtext(732, \"Galaxy\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(733, \"td\", 131)(734, \"ul\", 132)(735, \"li\", 133);\n        i0.ɵɵelement(736, \"img\", 134);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(737, \"li\", 133);\n        i0.ɵɵelement(738, \"img\", 135);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(739, \"li\", 133);\n        i0.ɵɵelement(740, \"img\", 136);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(741, \"li\", 133)(742, \"span\", 137);\n        i0.ɵɵtext(743, \"+4\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(744, \"td\", 131);\n        i0.ɵɵtext(745, \"$7500\");\n        i0.ɵɵelementEnd()()()()()()()()()();\n        i0.ɵɵelementStart(746, \"div\", 92)(747, \"div\", 6)(748, \"div\", 7)(749, \"h2\");\n        i0.ɵɵtext(750, \"Task List\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(751, \"div\", 19)(752, \"mat-tab-group\")(753, \"mat-tab\");\n        i0.ɵɵtemplate(754, DataWidgetComponent_ng_template_754_Template, 2, 0, \"ng-template\", 138);\n        i0.ɵɵelementStart(755, \"div\", 139)(756, \"table\", 111)(757, \"thead\")(758, \"tr\")(759, \"th\");\n        i0.ɵɵtext(760, \"Task\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(761, \"th\");\n        i0.ɵɵtext(762, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(763, \"th\");\n        i0.ɵɵtext(764, \"Manager\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(765, \"th\");\n        i0.ɵɵtext(766, \"Progress\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(767, \"tbody\")(768, \"tr\")(769, \"td\");\n        i0.ɵɵtext(770, \"Task C\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(771, \"td\")(772, \"span\", 140);\n        i0.ɵɵtext(773, \"Completed\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(774, \"td\");\n        i0.ɵɵtext(775, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(776, \"td\")(777, \"div\", 114);\n        i0.ɵɵelement(778, \"div\", 141);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(779, \"tr\")(780, \"td\");\n        i0.ɵɵtext(781, \"Task A\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(782, \"td\")(783, \"span\", 142);\n        i0.ɵɵtext(784, \"On Process\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(785, \"td\");\n        i0.ɵɵtext(786, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(787, \"td\")(788, \"div\", 114);\n        i0.ɵɵelement(789, \"div\", 143);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(790, \"tr\")(791, \"td\");\n        i0.ɵɵtext(792, \"Task B\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(793, \"td\")(794, \"span\", 144);\n        i0.ɵɵtext(795, \"On Hold\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(796, \"td\");\n        i0.ɵɵtext(797, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(798, \"td\")(799, \"div\", 114);\n        i0.ɵɵelement(800, \"div\", 117);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(801, \"tr\")(802, \"td\");\n        i0.ɵɵtext(803, \"Task D\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(804, \"td\")(805, \"span\", 140);\n        i0.ɵɵtext(806, \"Completed\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(807, \"td\");\n        i0.ɵɵtext(808, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(809, \"td\")(810, \"div\", 114);\n        i0.ɵɵelement(811, \"div\", 141);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(812, \"tr\")(813, \"td\");\n        i0.ɵɵtext(814, \"Task E\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(815, \"td\")(816, \"span\", 144);\n        i0.ɵɵtext(817, \"On Hold\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(818, \"td\");\n        i0.ɵɵtext(819, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(820, \"td\")(821, \"div\", 114);\n        i0.ɵɵelement(822, \"div\", 117);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(823, \"tr\")(824, \"td\");\n        i0.ɵɵtext(825, \"Task F\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(826, \"td\")(827, \"span\", 144);\n        i0.ɵɵtext(828, \"On Hold\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(829, \"td\");\n        i0.ɵɵtext(830, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(831, \"td\")(832, \"div\", 114);\n        i0.ɵɵelement(833, \"div\", 117);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(834, \"mat-tab\");\n        i0.ɵɵtemplate(835, DataWidgetComponent_ng_template_835_Template, 2, 0, \"ng-template\", 138);\n        i0.ɵɵelementStart(836, \"div\", 139)(837, \"table\", 111)(838, \"thead\")(839, \"tr\")(840, \"th\");\n        i0.ɵɵtext(841, \"Task\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(842, \"th\");\n        i0.ɵɵtext(843, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(844, \"th\");\n        i0.ɵɵtext(845, \"Manager\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(846, \"th\");\n        i0.ɵɵtext(847, \"Progress\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(848, \"tbody\")(849, \"tr\")(850, \"td\");\n        i0.ɵɵtext(851, \"Task D\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(852, \"td\")(853, \"span\", 142);\n        i0.ɵɵtext(854, \"On Process\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(855, \"td\");\n        i0.ɵɵtext(856, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(857, \"td\")(858, \"div\", 114);\n        i0.ɵɵelement(859, \"div\", 143);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(860, \"tr\")(861, \"td\");\n        i0.ɵɵtext(862, \"Task E\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(863, \"td\")(864, \"span\", 144);\n        i0.ɵɵtext(865, \"On Hold\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(866, \"td\");\n        i0.ɵɵtext(867, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(868, \"td\")(869, \"div\", 114);\n        i0.ɵɵelement(870, \"div\", 117);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(871, \"tr\")(872, \"td\");\n        i0.ɵɵtext(873, \"Task F\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(874, \"td\")(875, \"span\", 140);\n        i0.ɵɵtext(876, \"Completed\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(877, \"td\");\n        i0.ɵɵtext(878, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(879, \"td\")(880, \"div\", 114);\n        i0.ɵɵelement(881, \"div\", 141);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(882, \"tr\")(883, \"td\");\n        i0.ɵɵtext(884, \"Task G\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(885, \"td\")(886, \"span\", 142);\n        i0.ɵɵtext(887, \"On Process\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(888, \"td\");\n        i0.ɵɵtext(889, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(890, \"td\")(891, \"div\", 114);\n        i0.ɵɵelement(892, \"div\", 143);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(893, \"tr\")(894, \"td\");\n        i0.ɵɵtext(895, \"Task K\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(896, \"td\")(897, \"span\", 144);\n        i0.ɵɵtext(898, \"On Hold\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(899, \"td\");\n        i0.ɵɵtext(900, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(901, \"td\")(902, \"div\", 114);\n        i0.ɵɵelement(903, \"div\", 117);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(904, \"tr\")(905, \"td\");\n        i0.ɵɵtext(906, \"Task M\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(907, \"td\")(908, \"span\", 142);\n        i0.ɵɵtext(909, \"On Process\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(910, \"td\");\n        i0.ɵɵtext(911, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(912, \"td\")(913, \"div\", 114);\n        i0.ɵɵelement(914, \"div\", 143);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(915, \"mat-tab\");\n        i0.ɵɵtemplate(916, DataWidgetComponent_ng_template_916_Template, 2, 0, \"ng-template\", 138);\n        i0.ɵɵelementStart(917, \"div\", 139)(918, \"table\", 111)(919, \"thead\")(920, \"tr\")(921, \"th\");\n        i0.ɵɵtext(922, \"Task\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(923, \"th\");\n        i0.ɵɵtext(924, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(925, \"th\");\n        i0.ɵɵtext(926, \"Manager\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(927, \"th\");\n        i0.ɵɵtext(928, \"Progress\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(929, \"tbody\")(930, \"tr\")(931, \"td\");\n        i0.ɵɵtext(932, \"Task E\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(933, \"td\")(934, \"span\", 144);\n        i0.ɵɵtext(935, \"On Hold\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(936, \"td\");\n        i0.ɵɵtext(937, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(938, \"td\")(939, \"div\", 114);\n        i0.ɵɵelement(940, \"div\", 117);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(941, \"tr\")(942, \"td\");\n        i0.ɵɵtext(943, \"Task D\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(944, \"td\")(945, \"span\", 142);\n        i0.ɵɵtext(946, \"On Process\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(947, \"td\");\n        i0.ɵɵtext(948, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(949, \"td\")(950, \"div\", 114);\n        i0.ɵɵelement(951, \"div\", 143);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(952, \"tr\")(953, \"td\");\n        i0.ɵɵtext(954, \"Task F\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(955, \"td\")(956, \"span\", 140);\n        i0.ɵɵtext(957, \"Completed\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(958, \"td\");\n        i0.ɵɵtext(959, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(960, \"td\")(961, \"div\", 114);\n        i0.ɵɵelement(962, \"div\", 141);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(963, \"tr\")(964, \"td\");\n        i0.ɵɵtext(965, \"Task L\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(966, \"td\")(967, \"span\", 142);\n        i0.ɵɵtext(968, \"On Process\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(969, \"td\");\n        i0.ɵɵtext(970, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(971, \"td\")(972, \"div\", 114);\n        i0.ɵɵelement(973, \"div\", 143);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(974, \"tr\")(975, \"td\");\n        i0.ɵɵtext(976, \"Task H\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(977, \"td\")(978, \"span\", 144);\n        i0.ɵɵtext(979, \"On Hold\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(980, \"td\");\n        i0.ɵɵtext(981, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(982, \"td\")(983, \"div\", 114);\n        i0.ɵɵelement(984, \"div\", 117);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(985, \"tr\")(986, \"td\");\n        i0.ɵɵtext(987, \"Task L\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(988, \"td\")(989, \"span\", 142);\n        i0.ɵɵtext(990, \"On Process\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(991, \"td\");\n        i0.ɵɵtext(992, \"John Doe\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(993, \"td\")(994, \"div\", 114);\n        i0.ɵɵelement(995, \"div\", 143);\n        i0.ɵɵelementEnd()()()()()()()()()()()();\n        i0.ɵɵelementStart(996, \"div\", 51)(997, \"div\", 145)(998, \"div\", 146)(999, \"div\", 19)(1000, \"div\", 4)(1001, \"div\", 147)(1002, \"div\", 148);\n        i0.ɵɵelement(1003, \"img\", 149);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1004, \"div\", 150)(1005, \"h6\", 151);\n        i0.ɵɵtext(1006, \"Sarah Smith\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1007, \"span\", 152);\n        i0.ɵɵtext(1008, \"Java Develper\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1009, \"p\");\n        i0.ɵɵtext(1010, \"102, Svayam Appartment, new P.L. road, Rajkot-369852\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1011, \"div\")(1012, \"button\", 153);\n        i0.ɵɵtext(1013, \"Follow\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1014, \"button\", 154);\n        i0.ɵɵtext(1015, \"Message\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(1016, \"div\", 145)(1017, \"div\", 146)(1018, \"div\", 19)(1019, \"div\", 4)(1020, \"div\", 147)(1021, \"div\", 148);\n        i0.ɵɵelement(1022, \"img\", 155);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1023, \"div\", 150)(1024, \"h6\", 151);\n        i0.ɵɵtext(1025, \"Sarah Smith\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1026, \"span\", 152);\n        i0.ɵɵtext(1027, \"Java Develper\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1028, \"p\");\n        i0.ɵɵtext(1029, \"102, Svayam Appartment, new P.L. road, Rajkot-369852\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1030, \"div\")(1031, \"button\", 153);\n        i0.ɵɵtext(1032, \"Follow\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1033, \"button\", 154);\n        i0.ɵɵtext(1034, \"Message\");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(1035, \"div\", 4)(1036, \"div\", 18)(1037, \"div\", 6)(1038, \"div\", 7)(1039, \"h2\");\n        i0.ɵɵtext(1040, \"Recent Activities\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1041, \"div\", 19)(1042, \"div\", 156)(1043, \"div\", 157)(1044, \"small\", 158);\n        i0.ɵɵelement(1045, \"i\", 159);\n        i0.ɵɵtext(1046, \" 5 mins ago\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1047, \"p\");\n        i0.ɵɵtext(1048, \"Lorem ipsum dolor sit amet conse ctetur which ascing elit users.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1049, \"div\", 160)(1050, \"div\", 157)(1051, \"small\", 158);\n        i0.ɵɵelement(1052, \"i\", 159);\n        i0.ɵɵtext(1053, \" 8 mins ago\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1054, \"p\");\n        i0.ɵɵtext(1055, \"Lorem ipsum dolor sit ametcon the sectetur that ascing elit users.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1056, \"div\", 161)(1057, \"div\", 157)(1058, \"small\", 158);\n        i0.ɵɵelement(1059, \"i\", 159);\n        i0.ɵɵtext(1060, \" 10 mins ago\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1061, \"p\");\n        i0.ɵɵtext(1062, \"Lorem ipsum dolor sit amet cons the ecte tur and adip ascing elit users.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1063, \"div\", 162)(1064, \"div\", 157)(1065, \"small\", 158);\n        i0.ɵɵelement(1066, \"i\", 159);\n        i0.ɵɵtext(1067, \" 12 mins ago\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1068, \"p\");\n        i0.ɵɵtext(1069, \"Lorem ipsum dolor sit amet consec tetur adip ascing elit users.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1070, \"div\", 156)(1071, \"div\", 157)(1072, \"small\", 158);\n        i0.ɵɵelement(1073, \"i\", 159);\n        i0.ɵɵtext(1074, \" 5 mins ago\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1075, \"p\");\n        i0.ɵɵtext(1076, \"Lorem ipsum dolor sit amet conse ctetur which ascing elit users.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(1077, \"div\", 18)(1078, \"div\", 6)(1079, \"div\", 163)(1080, \"div\", 164);\n        i0.ɵɵelement(1081, \"img\", 165);\n        i0.ɵɵelementStart(1082, \"div\", 166)(1083, \"div\", 167);\n        i0.ɵɵtext(1084, \"Aiden Chavez\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1085, \"div\", 168);\n        i0.ɵɵtext(1086, \"2 new messages\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1087, \"ng-scrollbar\", 169)(1088, \"div\", 170)(1089, \"ul\")(1090, \"li\", 171)(1091, \"div\", 172)(1092, \"span\", 173);\n        i0.ɵɵtext(1093, \"10:10 AM, Today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(1094, \" \\u00A0 \\u00A0 \");\n        i0.ɵɵelementStart(1095, \"span\", 174);\n        i0.ɵɵtext(1096, \"Michael\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1097, \"i\", 175);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1098, \"div\", 176);\n        i0.ɵɵtext(1099, \" Hi Aiden, how are you? How is the project coming along? \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1100, \"li\")(1101, \"div\", 177)(1102, \"span\", 174);\n        i0.ɵɵelement(1103, \"i\", 178);\n        i0.ɵɵtext(1104, \" Aiden\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1105, \"span\", 173);\n        i0.ɵɵtext(1106, \"10:12 AM, Today\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1107, \"div\", 179)(1108, \"p\");\n        i0.ɵɵtext(1109, \"Are we meeting today? Project has been already finished and I have results to show you.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1110, \"div\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1111, \"li\")(1112, \"div\", 177)(1113, \"span\", 174);\n        i0.ɵɵelement(1114, \"i\", 178);\n        i0.ɵɵtext(1115, \" Aiden\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1116, \"span\", 173);\n        i0.ɵɵtext(1117, \"10:12 AM, Today\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1118, \"div\", 179)(1119, \"p\");\n        i0.ɵɵtext(1120, \"Are we meeting today? Project has been already finished and I have results to show you.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1121, \"div\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1122, \"li\", 171)(1123, \"div\", 172)(1124, \"span\", 173);\n        i0.ɵɵtext(1125, \"10:10 AM, Today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(1126, \" \\u00A0 \\u00A0 \");\n        i0.ɵɵelementStart(1127, \"span\", 174);\n        i0.ɵɵtext(1128, \"Michael\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(1129, \"i\", 175);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1130, \"div\", 176);\n        i0.ɵɵtext(1131, \" Hi Aiden, how are you? How is the project coming along? \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(1132, \"div\", 180)(1133, \"div\", 181)(1134, \"div\", 182)(1135, \"mat-form-field\", 183);\n        i0.ɵɵelement(1136, \"input\", 184);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(1137, \"div\", 18)(1138, \"div\", 6)(1139, \"div\", 7)(1140, \"h2\");\n        i0.ɵɵtext(1141, \"Latest Posts\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1142, \"div\", 19)(1143, \"div\", 185)(1144, \"div\", 186)(1145, \"div\", 187);\n        i0.ɵɵelement(1146, \"img\", 188);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1147, \"div\", 56)(1148, \"h6\");\n        i0.ɵɵtext(1149, \"About Something\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1150, \"p\", 189);\n        i0.ɵɵtext(1151, \" Video | 10 minutes ago \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1152, \"p\", 158);\n        i0.ɵɵtext(1153, \"Lorem Ipsum is simply dummy text of the.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1154, \"div\", 186)(1155, \"div\", 187);\n        i0.ɵɵelement(1156, \"img\", 190);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1157, \"div\", 56)(1158, \"h6\");\n        i0.ɵɵtext(1159, \"Relationship\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1160, \"p\", 189);\n        i0.ɵɵtext(1161, \" Video | 24 minutes ago \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1162, \"p\", 158);\n        i0.ɵɵtext(1163, \"Lorem Ipsum is simply dummy text of the.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1164, \"div\", 186)(1165, \"div\", 187);\n        i0.ɵɵelement(1166, \"img\", 191);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1167, \"div\", 56)(1168, \"h6\");\n        i0.ɵɵtext(1169, \"Human body\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1170, \"p\", 189);\n        i0.ɵɵtext(1171, \" Video | 53 minutes ago \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1172, \"p\", 158);\n        i0.ɵɵtext(1173, \"Lorem Ipsum is simply dummy text of the.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1174, \"div\", 192)(1175, \"a\", 66);\n        i0.ɵɵtext(1176, \"View All Posts\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(1177, \"div\", 51)(1178, \"div\", 92)(1179, \"div\", 6)(1180, \"div\", 7)(1181, \"h2\");\n        i0.ɵɵtext(1182, \"Notice Board\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1183, \"div\", 19)(1184, \"div\", 193)(1185, \"div\", 194)(1186, \"div\", 112);\n        i0.ɵɵelement(1187, \"img\", 195);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1188, \"div\", 196)(1189, \"h6\", 197);\n        i0.ɵɵtext(1190, \"Airi Satou\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1191, \"p\");\n        i0.ɵɵtext(1192, \"Lorem ipsum dolor sit amet, id quo eruditi eloquentiam.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1193, \"small\", 158);\n        i0.ɵɵtext(1194, \"7 hours ago\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1195, \"div\", 194)(1196, \"div\", 112);\n        i0.ɵɵelement(1197, \"img\", 198);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1198, \"div\", 196)(1199, \"h6\", 199);\n        i0.ɵɵtext(1200, \"Sarah Smith\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1201, \"p\");\n        i0.ɵɵtext(1202, \"Lorem ipsum dolor sit amet, id quo eruditi eloquentiam.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1203, \"p\", 200);\n        i0.ɵɵtext(1204, \"1 hour ago\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1205, \"div\", 194)(1206, \"div\", 112);\n        i0.ɵɵelement(1207, \"img\", 201);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1208, \"div\", 196)(1209, \"h6\", 202);\n        i0.ɵɵtext(1210, \"Cara Stevens\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1211, \"p\");\n        i0.ɵɵtext(1212, \"Lorem ipsum dolor sit amet, id quo eruditi eloquentiam.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1213, \"div\", 200);\n        i0.ɵɵtext(1214, \"Yesterday\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1215, \"div\", 203)(1216, \"div\", 112);\n        i0.ɵɵelement(1217, \"img\", 204);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1218, \"div\", 196)(1219, \"h6\", 205);\n        i0.ɵɵtext(1220, \"Ashton Cox\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1221, \"p\");\n        i0.ɵɵtext(1222, \"Lorem ipsum dolor sit amet, id quo eruditi eloquentiam.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1223, \"div\", 200);\n        i0.ɵɵtext(1224, \"Yesterday\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(1225, \"div\", 92)(1226, \"div\", 6)(1227, \"div\", 7)(1228, \"h2\");\n        i0.ɵɵtext(1229, \"Recent Activity\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1230, \"div\", 19)(1231, \"ul\", 206)(1232, \"li\")(1233, \"div\", 207);\n        i0.ɵɵelement(1234, \"img\", 208);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1235, \"div\", 209)(1236, \"div\", 210)(1237, \"h5\", 211);\n        i0.ɵɵtext(1238, \"Lorem ipsum dolor sit amet, id quo eruditi.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1239, \"div\", 212)(1240, \"p\");\n        i0.ɵɵtext(1241, \"5 minutes ago\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1242, \"li\")(1243, \"div\", 207);\n        i0.ɵɵelement(1244, \"img\", 213);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1245, \"div\", 209)(1246, \"div\", 210)(1247, \"h5\", 211);\n        i0.ɵɵtext(1248, \"Lorem ipsum dolor sit amet, id quo eruditi.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1249, \"div\", 212)(1250, \"p\");\n        i0.ɵɵtext(1251, \"10 minutes ago\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1252, \"li\")(1253, \"div\", 207);\n        i0.ɵɵelement(1254, \"img\", 214);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1255, \"div\", 209)(1256, \"div\", 210)(1257, \"h5\", 211);\n        i0.ɵɵtext(1258, \"Lorem ipsum dolor sit amet, id quo eruditi.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1259, \"div\", 212)(1260, \"p\");\n        i0.ɵɵtext(1261, \"20 minutes ago\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1262, \"li\")(1263, \"div\", 207);\n        i0.ɵɵelement(1264, \"img\", 198);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1265, \"div\", 209)(1266, \"div\", 210)(1267, \"h5\", 211);\n        i0.ɵɵtext(1268, \"Lorem ipsum dolor sit amet, id quo eruditi.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1269, \"div\", 212)(1270, \"p\");\n        i0.ɵɵtext(1271, \"35 minutes ago\");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(1272, \"div\", 4)(1273, \"div\", 18)(1274, \"div\", 6)(1275, \"div\", 7)(1276, \"h2\");\n        i0.ɵɵtext(1277, \"Todo List\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1278, \"div\", 19)(1279, \"ng-scrollbar\", 215)(1280, \"div\", 216);\n        i0.ɵɵlistener(\"cdkDropListDropped\", function DataWidgetComponent_Template_div_cdkDropListDropped_1280_listener($event) {\n          return ctx.drop($event);\n        });\n        i0.ɵɵrepeaterCreate(1281, DataWidgetComponent_For_1282_Template, 14, 14, \"div\", 253, i0.ɵɵrepeaterTrackByIdentity);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(1283, \"div\", 5)(1284, \"div\", 6)(1285, \"div\", 7)(1286, \"h2\");\n        i0.ɵɵtext(1287, \"Medications\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(1288, \"div\", 19)(1289, \"div\", 9)(1290, \"table\", 217)(1291, \"tr\")(1292, \"td\");\n        i0.ɵɵelement(1293, \"i\", 218);\n        i0.ɵɵtext(1294, \" Econochlor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1295, \"td\", 219)(1296, \"span\", 220);\n        i0.ɵɵtext(1297, \"1 - 0 - 1\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1298, \"tr\")(1299, \"td\");\n        i0.ɵɵelement(1300, \"i\", 221);\n        i0.ɵɵtext(1301, \" Desmopressin tabs\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1302, \"td\", 219)(1303, \"span\", 220);\n        i0.ɵɵtext(1304, \"1 - 1 - 1\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1305, \"tr\")(1306, \"td\");\n        i0.ɵɵelement(1307, \"i\", 222);\n        i0.ɵɵtext(1308, \" Abciximab-injection\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1309, \"td\", 219)(1310, \"span\", 220);\n        i0.ɵɵtext(1311, \"1 Daily\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1312, \"tr\")(1313, \"td\");\n        i0.ɵɵelement(1314, \"i\", 223);\n        i0.ɵɵtext(1315, \" Kevzara sarilumab\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1316, \"td\", 219)(1317, \"span\", 220);\n        i0.ɵɵtext(1318, \"0 - 0 - 1\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1319, \"tr\")(1320, \"td\");\n        i0.ɵɵelement(1321, \"i\", 221);\n        i0.ɵɵtext(1322, \" Gentamicin-topical\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1323, \"td\", 219)(1324, \"span\", 220);\n        i0.ɵɵtext(1325, \"1 - 0 - 1\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1326, \"tr\")(1327, \"td\");\n        i0.ɵɵelement(1328, \"i\", 218);\n        i0.ɵɵtext(1329, \" Paliperidone palmitate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1330, \"td\", 219)(1331, \"span\", 220);\n        i0.ɵɵtext(1332, \"1 - 1 - 1\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1333, \"tr\")(1334, \"td\");\n        i0.ɵɵelement(1335, \"i\", 222);\n        i0.ɵɵtext(1336, \" Sermorelin-injectable\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1337, \"td\", 219)(1338, \"span\", 220);\n        i0.ɵɵtext(1339, \"1 Daily\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(1340, \"div\", 37)(1341, \"button\", 38);\n        i0.ɵɵtext(1342, \"Report Adverse Effect\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(1343, \"div\", 5)(1344, \"div\", 224)(1345, \"div\", 225)(1346, \"h6\", 226);\n        i0.ɵɵtext(1347, \"New Admission Report\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1348, \"div\", 227)(1349, \"div\", 228)(1350, \"label\", 229);\n        i0.ɵɵtext(1351, \"Today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1352, \"h6\");\n        i0.ɵɵtext(1353, \"105\");\n        i0.ɵɵelementStart(1354, \"i\", 230);\n        i0.ɵɵtext(1355, \"trending_up\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1356, \"div\", 228)(1357, \"label\", 229);\n        i0.ɵɵtext(1358, \"This Week\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1359, \"h6\");\n        i0.ɵɵtext(1360, \"825\");\n        i0.ɵɵelementStart(1361, \"i\", 231);\n        i0.ɵɵtext(1362, \"trending_down\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1363, \"div\", 228)(1364, \"label\", 229);\n        i0.ɵɵtext(1365, \"This Month\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1366, \"h6\");\n        i0.ɵɵtext(1367, \"22,067\");\n        i0.ɵɵelementStart(1368, \"i\", 230);\n        i0.ɵɵtext(1369, \"trending_up\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1370, \"div\", 232)(1371, \"div\", 233);\n        i0.ɵɵtext(1372, \"70%\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1373, \"div\", 234)(1374, \"div\", 225)(1375, \"h6\", 226);\n        i0.ɵɵtext(1376, \"Fees Collection Report\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1377, \"div\", 227)(1378, \"div\", 228)(1379, \"label\", 229);\n        i0.ɵɵtext(1380, \"Today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1381, \"h6\");\n        i0.ɵɵtext(1382, \"$147\");\n        i0.ɵɵelementStart(1383, \"i\", 231);\n        i0.ɵɵtext(1384, \"trending_down\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1385, \"div\", 228)(1386, \"label\", 229);\n        i0.ɵɵtext(1387, \"This Week\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1388, \"h6\");\n        i0.ɵɵtext(1389, \"$968\");\n        i0.ɵɵelementStart(1390, \"i\", 230);\n        i0.ɵɵtext(1391, \"trending_up\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(1392, \"div\", 228)(1393, \"label\", 229);\n        i0.ɵɵtext(1394, \"This Month\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(1395, \"h6\");\n        i0.ɵɵtext(1396, \"$1,147\");\n        i0.ɵɵelementStart(1397, \"i\", 231);\n        i0.ɵɵtext(1398, \"trending_down\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(1399, \"div\", 232)(1400, \"div\", 235);\n        i0.ɵɵtext(1401, \"50%\");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Data Widget\")(\"items\", i0.ɵɵpureFunction0(3, _c2))(\"active_item\", \"Data Widget\");\n        i0.ɵɵadvance(1278);\n        i0.ɵɵrepeater(ctx.tasks);\n      }\n    },\n    dependencies: [BreadcrumbComponent, MatProgressBarModule, i1.MatProgressBar, NgScrollbar, MatButtonModule, i2.MatButton, MatTabsModule, i3.MatTabLabel, i3.MatTab, i3.MatTabGroup, MatFormFieldModule, i4.MatFormField, MatInputModule, i5.MatInput, CdkDropList, CdkDrag, CdkDragHandle, MatIconModule, i6.MatIcon, MatCheckboxModule, i7.MatCheckbox, CdkDragPlaceholder, MatTooltipModule, i8.MatTooltip, NgClass],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["moveItemInArray", "CdkDropList", "CdkDrag", "CdkDragHandle", "CdkDragPlaceholder", "Ng<PERSON><PERSON>", "MatTooltipModule", "MatCheckboxModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatTabsModule", "MatButtonModule", "NgScrollbar", "MatProgressBarModule", "BreadcrumbComponent", "i0", "ɵɵelement", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵlistener", "DataWidgetComponent_For_1282_Template_mat_checkbox_change_5_listener", "restoredCtx", "ɵɵrestoreView", "_r14", "task_r4", "$implicit", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "toggle", "ɵɵtemplate", "DataWidgetComponent_For_1282_div_6_Template", "DataWidgetComponent_For_1282_Conditional_10_Template", "DataWidgetComponent_For_1282_Conditional_11_Template", "DataWidgetComponent_For_1282_Conditional_12_Template", "ɵɵadvance", "ɵɵproperty", "done", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "title", "ɵɵpureFunction3", "_c1", "priority", "ɵɵconditional", "DataWidgetComponent", "constructor", "tasks", "id", "drop", "event", "previousIndex", "currentIndex", "task", "_", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DataWidgetComponent_Template", "rf", "ctx", "DataWidgetComponent_ng_template_754_Template", "DataWidgetComponent_ng_template_835_Template", "DataWidgetComponent_ng_template_916_Template", "DataWidgetComponent_Template_div_cdkDropListDropped_1280_listener", "$event", "ɵɵrepeaterCreate", "DataWidgetComponent_For_1282_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵpureFunction0", "_c2", "ɵɵrepeater", "i1", "MatProgressBar", "i2", "MatButton", "i3", "MatTab<PERSON><PERSON><PERSON>", "Mat<PERSON><PERSON>", "MatTabGroup", "i4", "MatFormField", "i5", "MatInput", "i6", "MatIcon", "i7", "MatCheckbox", "i8", "MatTooltip", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\widget\\data-widget\\data-widget.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\widget\\data-widget\\data-widget.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport {\r\n  CdkDragDrop,\r\n  moveItemInArray,\r\n  CdkDropList,\r\n  CdkDrag,\r\n  CdkDragHandle,\r\n  CdkDragPlaceholder,\r\n} from '@angular/cdk/drag-drop';\r\nimport { NgClass } from '@angular/common';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { NgScrollbar } from 'ngx-scrollbar';\r\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n@Component({\r\n  selector: 'app-data-widget',\r\n  templateUrl: './data-widget.component.html',\r\n  styleUrls: ['./data-widget.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    BreadcrumbComponent,\r\n    MatProgressBarModule,\r\n    NgScrollbar,\r\n    MatButtonModule,\r\n    MatTabsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    CdkDropList,\r\n    CdkDrag,\r\n    CdkDragHandle,\r\n    MatIconModule,\r\n    MatCheckboxModule,\r\n    CdkDragPlaceholder,\r\n    MatTooltipModule,\r\n    NgClass,\r\n  ],\r\n})\r\nexport class DataWidgetComponent {\r\n  constructor() {\r\n    // /constructor\r\n  }\r\n\r\n  // TODO start\r\n  tasks = [\r\n    {\r\n      id: '1',\r\n      title: 'Submit Science Homework',\r\n      done: true,\r\n      priority: 'High',\r\n    },\r\n    {\r\n      id: '2',\r\n      title: 'Request for festivle holiday',\r\n      done: false,\r\n      priority: 'High',\r\n    },\r\n    {\r\n      id: '3',\r\n      title: 'Order new java book',\r\n      done: false,\r\n      priority: 'Low',\r\n    },\r\n    {\r\n      id: '4',\r\n      title: 'Remind for lunch in hotel',\r\n      done: true,\r\n      priority: 'Normal',\r\n    },\r\n    {\r\n      id: '5',\r\n      title: 'Pay Hostel Fees',\r\n      done: false,\r\n      priority: 'High',\r\n    },\r\n    {\r\n      id: '6',\r\n      title: 'Attend Seminar On Sunday',\r\n      done: false,\r\n      priority: 'Normal',\r\n    },\r\n    {\r\n      id: '7',\r\n      title: 'Renew bus pass',\r\n      done: true,\r\n      priority: 'High',\r\n    },\r\n    {\r\n      id: '8',\r\n      title: 'Issue book in library',\r\n      done: false,\r\n      priority: 'High',\r\n    },\r\n    {\r\n      id: '9',\r\n      title: 'Project report submit',\r\n      done: false,\r\n      priority: 'Low',\r\n    },\r\n  ];\r\n\r\n  drop(event: CdkDragDrop<string[]>) {\r\n    moveItemInArray(this.tasks, event.previousIndex, event.currentIndex);\r\n  }\r\n\r\n  toggle(task: { done: boolean }) {\r\n    task.done = !task.done;\r\n  }\r\n  // TODO end\r\n}\r\n", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Data Widget'\" [items]=\"['Home']\" [active_item]=\"'Data Widget'\"></app-breadcrumb>\r\n    </div>\r\n    <div class=\"row\">\r\n      <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Your Progress</h2>\r\n          </div>\r\n          <div class=\"tableBody\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-hover\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Subject</th>\r\n                    <th>Progress</th>\r\n                    <th>Duration</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr>\r\n                    <td>Chemistry</td>\r\n                    <td>30%<mat-progress-bar mode=\"determinate\" value=\"30\" color=\"warn\"></mat-progress-bar>\r\n                    </td>\r\n                    <td>2 Months</td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td>Mathematics</td>\r\n                    <td>55%<mat-progress-bar mode=\"determinate\" value=\"55\"></mat-progress-bar>\r\n                    </td>\r\n                    <td>3 Months</td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td>Painting</td>\r\n                    <td>67%<mat-progress-bar mode=\"determinate\" value=\"67\"></mat-progress-bar>\r\n                    </td>\r\n                    <td>1 Months</td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td>Business studies</td>\r\n                    <td>70%<mat-progress-bar mode=\"determinate\" value=\"70\"></mat-progress-bar>\r\n                    </td>\r\n                    <td>2 Months</td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td>Biology</td>\r\n                    <td>24%<mat-progress-bar mode=\"determinate\" value=\"24\" color=\"warn\"></mat-progress-bar>\r\n                    </td>\r\n                    <td>3 Months</td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td>Computer studies</td>\r\n                    <td>77%<mat-progress-bar mode=\"determinate\" value=\"77\"></mat-progress-bar>\r\n                    </td>\r\n                    <td>4 Months</td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td>Geography</td>\r\n                    <td>41%<mat-progress-bar mode=\"determinate\" value=\"41\" color=\"warn\"></mat-progress-bar>\r\n                    </td>\r\n                    <td>2 Months</td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xs-12 col-sm-12 col-md-4 col-lg-4\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Assignments</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"doc-file-type\">\r\n              <ng-scrollbar style=\"height: 285px\" visibility=\"hover\">\r\n                <ul class=\"list-unstyled\">\r\n                  <li class=\"d-flex mb-3\">\r\n                    <span class=\"msr-3 align-self-center img-icon primary-rgba text-primary\"><i\r\n                        class=\"far fa-file-word\"></i></span>\r\n                    <div class=\"set-flex\">\r\n                      <h5 class=\"font-14 mb-1\">Java Programming</h5>\r\n                      <p>.doc, 4.3 MB</p>\r\n                    </div>\r\n                    <div class=\"ms-auto\">\r\n                      <td>\r\n                        <i class=\"far fa-trash-alt psr-3\"></i>\r\n                        <i class=\"far fa-arrow-alt-circle-down\"></i>\r\n                      </td>\r\n                    </div>\r\n                  </li>\r\n                  <li class=\"d-flex mb-3\">\r\n                    <span class=\"msr-3 align-self-center img-icon success-rgba text-success\"><i\r\n                        class=\"far fa-file-excel\"></i></span>\r\n                    <div class=\"set-flex\">\r\n                      <h5 class=\"font-14 mb-1\">Angular Theory</h5>\r\n                      <p>.xls, 2.5 MB</p>\r\n                    </div>\r\n                    <div class=\"ms-auto\">\r\n                      <td>\r\n                        <i class=\"far fa-trash-alt psr-3\"></i>\r\n                        <i class=\"far fa-arrow-alt-circle-down\"></i>\r\n                      </td>\r\n                    </div>\r\n                  </li>\r\n                  <li class=\"d-flex mb-3\">\r\n                    <span class=\"msr-3 align-self-center img-icon danger-rgba text-danger\"><i\r\n                        class=\"far fa-file-pdf\"></i></span>\r\n                    <div class=\"set-flex\">\r\n                      <h5 class=\"font-14 mb-1\">Maths Sums Solution</h5>\r\n                      <p>.pdf, 10.5 MB</p>\r\n                    </div>\r\n                    <div class=\"ms-auto\">\r\n                      <td>\r\n                        <i class=\"far fa-trash-alt psr-3\"></i>\r\n                        <i class=\"far fa-arrow-alt-circle-down\"></i>\r\n                      </td>\r\n                    </div>\r\n                  </li>\r\n                  <li class=\"d-flex mb-3\">\r\n                    <span class=\"msr-3 align-self-center img-icon info-rgba text-info\"><i\r\n                        class=\"far fa-file-archive\"></i></span>\r\n                    <div class=\"set-flex\">\r\n                      <h5 class=\"font-14 mb-1\">Submit Science Journal</h5>\r\n                      <p>.zip, 53.2 MB</p>\r\n                    </div>\r\n                    <div class=\"ms-auto\">\r\n                      <td>\r\n                        <i class=\"far fa-trash-alt psr-3\"></i>\r\n                        <i class=\"far fa-arrow-alt-circle-down\"></i>\r\n                      </td>\r\n                    </div>\r\n                  </li>\r\n                  <li class=\"d-flex mb-3\">\r\n                    <span class=\"msr-3 align-self-center img-icon primary-rgba text-primary\"><i\r\n                        class=\"far fa-file-word\"></i></span>\r\n                    <div class=\"set-flex\">\r\n                      <h5 class=\"font-14 mb-1\">Marketing Instructions</h5>\r\n                      <p>.doc, 5.3 MB</p>\r\n                    </div>\r\n                    <div class=\"ms-auto\">\r\n                      <td>\r\n                        <i class=\"far fa-trash-alt psr-3\"></i>\r\n                        <i class=\"far fa-arrow-alt-circle-down\"></i>\r\n                      </td>\r\n                    </div>\r\n                  </li>\r\n                </ul>\r\n              </ng-scrollbar>\r\n              <div class=\"text-center p-t-20\">\r\n                <button mat-stroked-button color=\"primary\">More Records</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>\r\n              Upcomming Appointments\r\n            </h2>\r\n          </div>\r\n          <div class=\"card-body\">\r\n            <ng-scrollbar style=\"height: 335px\" visibility=\"hover\">\r\n              <ul class=\"list-unstyled user-progress list-unstyled-border list-unstyled-noborder\">\r\n                <li class=\"lecture-list\">\r\n                  <img alt=\"image\" class=\"msr-3 rounded-circle\" width=\"40\" src=\"assets/images/user/usrbig1.jpg\">\r\n                  <div class=\"set-flex\">\r\n                    <div class=\"media-title font-14 font-b-500\">Cara Stevens</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">M.B.B.S.</div>\r\n                  </div>\r\n                  <div class=\"ms-auto\">\r\n                    <div class=\"media-title font-14 font-b-500\">12 June '20</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">09:00-10:00</div>\r\n                  </div>\r\n                </li>\r\n                <li class=\"lecture-list\">\r\n                  <img alt=\"image\" class=\"msr-3 rounded-circle\" width=\"40\" src=\"assets/images/user/usrbig2.jpg\">\r\n                  <div class=\"set-flex\">\r\n                    <div class=\"media-title font-14 font-b-500\">Airi Satou</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">M.S.</div>\r\n                  </div>\r\n                  <div class=\"ms-auto\">\r\n                    <div class=\"media-title font-14 font-b-500\">13 June '20</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">11:00-12:00</div>\r\n                  </div>\r\n                </li>\r\n                <li class=\"lecture-list\">\r\n                  <img alt=\"image\" class=\"msr-3 rounded-circle\" width=\"40\" src=\"assets/images/user/usrbig3.jpg\">\r\n                  <div class=\"set-flex\">\r\n                    <div class=\"media-title font-14 font-b-500\">Jens Brincker</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">Geography</div>\r\n                  </div>\r\n                  <div class=\"ms-auto\">\r\n                    <div class=\"media-title font-14 font-b-500\">15 June '20</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">09:30-10:30</div>\r\n                  </div>\r\n                </li>\r\n                <li class=\"lecture-list\">\r\n                  <img alt=\"image\" class=\"msr-3 rounded-circle\" width=\"40\" src=\"assets/images/user/usrbig4.jpg\">\r\n                  <div class=\"set-flex\">\r\n                    <div class=\"media-title font-14 font-b-500\">Angelica Ramos</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">B.H.M.S.</div>\r\n                  </div>\r\n                  <div class=\"ms-auto\">\r\n                    <div class=\"media-title font-14 font-b-500\">16 June '20</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">14:00-15:00</div>\r\n                  </div>\r\n                </li>\r\n                <li class=\"lecture-list\">\r\n                  <img alt=\"image\" class=\"msr-3 rounded-circle\" width=\"40\" src=\"assets/images/user/usrbig5.jpg\">\r\n                  <div class=\"set-flex\">\r\n                    <div class=\"media-title font-14 font-b-500\">Cara Stevens</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">M.B.B.S.</div>\r\n                  </div>\r\n                  <div class=\"ms-auto\">\r\n                    <div class=\"media-title font-14 font-b-500\">18 June '20</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">11:00-12:30</div>\r\n                  </div>\r\n                </li>\r\n                <li class=\"lecture-list\">\r\n                  <img alt=\"image\" class=\"msr-3 rounded-circle\" width=\"40\" src=\"assets/images/user/usrbig6.jpg\">\r\n                  <div class=\"set-flex\">\r\n                    <div class=\"media-title font-14 font-b-500\">Jacob Ryan</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">M.D.</div>\r\n                  </div>\r\n                  <div class=\"ms-auto\">\r\n                    <div class=\"media-title font-14 font-b-500\">22 June '20</div>\r\n                    <div class=\"text-job text-muted mb-0 font-12\">13:00-14:15</div>\r\n                  </div>\r\n                </li>\r\n              </ul>\r\n            </ng-scrollbar>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <!-- Customer Review -->\r\n      <div class=\"col-xs-12 col-sm-12 col-md-8 col-lg-8\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Customer Review</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"review-block\">\r\n              <div class=\"row\">\r\n                <div class=\"review-img\">\r\n                  <img src=\"assets/images/user/user1.jpg\" alt=\"\">\r\n                </div>\r\n                <div class=\"col\">\r\n                  <h6 class=\"m-b-0 m-t-5\">Alis Smith\r\n                    <span class=\"float-end m-r-10 text-muted font-12\"> a week ago</span>\r\n                  </h6>\r\n                  <i class=\"material-icons\">star</i>\r\n                  <i class=\"material-icons\">star</i>\r\n                  <i class=\"material-icons\">star</i>\r\n                  <i class=\"material-icons\">star_half</i>\r\n                  <i class=\"material-icons\">star_border</i>\r\n                  <p class=\"m-t-10 m-b-15 text-muted\">Lorem ipsum dolor sit amet, consectetur\r\n                    adipiscing elit. Etiam vel rutrum ex, at\r\n                    ornare mi. In quis scelerisque dui, eget rhoncus orci. Fusce et sodales\r\n                    ipsum.\r\n                    Nam id nunc euismod, aliquet arcu quis, mattis nisi.</p>\r\n                  <a href=\"#!\">\r\n                    <i class=\"material-icons m-r-10\">thumb_up</i>\r\n                  </a>\r\n                  <a href=\"#!\">\r\n                    <i class=\"material-icons m-r-10 col-red\">thumb_down</i>\r\n                  </a>\r\n                </div>\r\n              </div>\r\n              <div class=\"row\">\r\n                <div class=\"review-img\">\r\n                  <img src=\"assets/images/user/user2.jpg\" alt=\"\">\r\n                </div>\r\n                <div class=\"col\">\r\n                  <h6 class=\"m-b-0 m-t-5\">John Dio\r\n                    <span class=\"float-end m-r-10 text-muted font-12\"> a week ago</span>\r\n                  </h6>\r\n                  <i class=\"material-icons\">star</i>\r\n                  <i class=\"material-icons\">star_half</i>\r\n                  <i class=\"material-icons\">star_border</i>\r\n                  <i class=\"material-icons\">star_border</i>\r\n                  <i class=\"material-icons\">star_border</i>\r\n                  <p class=\"m-t-10 m-b-15 text-muted\">Nam quis ligula est. Nunc sed risus non\r\n                    turpis tristique tempor. Ut sollicitudin\r\n                    faucibus magna nec gravida. Suspendisse ullamcorper justo vel porta\r\n                    imperdiet.\r\n                    Nunc nec ipsum vel augue placerat faucibus. </p>\r\n                  <a href=\"#!\">\r\n                    <i class=\"material-icons m-r-10\">thumb_up</i>\r\n                  </a>\r\n                  <a href=\"#!\">\r\n                    <i class=\"material-icons m-r-10 col-red\">thumb_down</i>\r\n                  </a>\r\n                </div>\r\n              </div>\r\n              <div class=\"text-center  m-b-5\">\r\n                <a href=\"#!\" class=\"b-b-primary text-primary\">View all Customer Reviews</a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- #END# Customer Review -->\r\n      <div class=\"col-md-4 col-sm-12 col-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Earning Source</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"totalEarning\">\r\n              <h4>$90,808</h4>\r\n            </div>\r\n            <div class=\"tab-pane body\" id=\"skills\">\r\n              <ul class=\"list-unstyled\">\r\n                <li>\r\n                  <div class=\"mb-2\">\r\n                    <span class=\"progress-label\">envato.com</span>\r\n                    <span class=\"float-end progress-percent label label-info m-b-5\">17%</span>\r\n                  </div>\r\n                  <div class=\"progress skill-progress m-b-20 w-100\">\r\n                    <div class=\"progress-bar l-bg-green width-per-45\" role=\"progressbar\" aria-valuenow=\"45\"\r\n                      aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                  </div>\r\n                </li>\r\n                <li>\r\n                  <div class=\"mb-2\">\r\n                    <span class=\"float-start progress-label\">google.com</span>\r\n                    <span class=\"float-end progress-percent label label-danger m-b-5\">27%</span>\r\n                  </div>\r\n                  <div class=\"progress skill-progress m-b-20 w-100\">\r\n                    <div class=\"progress-bar l-bg-purple width-per-27\" role=\"progressbar\" aria-valuenow=\"27\"\r\n                      aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                  </div>\r\n                </li>\r\n                <li>\r\n                  <div class=\"mb-2\">\r\n                    <span class=\"float-start progress-label\">yahoo.com</span>\r\n                    <span class=\"float-end progress-percent label label-primary m-b-5\">25%</span>\r\n                  </div>\r\n                  <div class=\"progress skill-progress m-b-20 w-100\">\r\n                    <div class=\"progress-bar l-bg-orange width-per-25\" role=\"progressbar\" aria-valuenow=\"25\"\r\n                      aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                  </div>\r\n                </li>\r\n                <li>\r\n                  <div class=\"mb-2\">\r\n                    <span class=\"float-start progress-label\">store</span>\r\n                    <span class=\"float-end progress-percent label label-success m-b-5\">18%</span>\r\n                  </div>\r\n                  <div class=\"progress skill-progress m-b-20 w-100\">\r\n                    <div class=\"progress-bar l-bg-cyan width-per-18\" role=\"progressbar\" aria-valuenow=\"18\"\r\n                      aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                  </div>\r\n                </li>\r\n                <li>\r\n                  <div class=\"mb-2\">\r\n                    <span class=\"float-start progress-label\">Others</span>\r\n                    <span class=\"float-end progress-percent label label-warning m-b-5\">13%</span>\r\n                  </div>\r\n                  <div class=\"progress skill-progress m-b-20 w-100\">\r\n                    <div class=\"progress-bar l-bg-red width-per-13\" role=\"progressbar\" aria-valuenow=\"13\"\r\n                      aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                  </div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-lg-4 col-md-4 col-sm-4 col-xs-12\">\r\n        <div class=\"box-part text-center\">\r\n          <i class=\"fab fa-twitter fa-3x col-blue\"></i>\r\n          <div class=\"title p-t-15\">\r\n            <h4>Twitter</h4>\r\n          </div>\r\n          <div class=\"text p-b-10\">\r\n            <span>Lorem ipsum dolor sit amet, id quo eruditi\r\n              eloquentiam. Assum decore te sed. Elitr scripta ocurreret qui\r\n              ad.</span>\r\n          </div>\r\n          <a href=\"#\">Learn More</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-4 col-md-4 col-sm-4 col-xs-12\">\r\n        <div class=\"box-part text-center\">\r\n          <i class=\"fab fa-instagram fa-3x col-red\"></i>\r\n          <div class=\"title p-t-15\">\r\n            <h4>Instagram</h4>\r\n          </div>\r\n          <div class=\"text p-b-10\">\r\n            <span>Lorem ipsum dolor sit amet, id quo eruditi\r\n              eloquentiam. Assum decore te sed. Elitr scripta ocurreret qui\r\n              ad.</span>\r\n          </div>\r\n          <a href=\"#\">Learn More</a>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-4 col-md-4 col-sm-4 col-xs-12\">\r\n        <div class=\"box-part text-center\">\r\n          <i class=\"fab fa-facebook-f fa-3x col-blue\"></i>\r\n          <div class=\"title p-t-15\">\r\n            <h4>Facebook</h4>\r\n          </div>\r\n          <div class=\"text p-b-10\">\r\n            <span>Lorem ipsum dolor sit amet, id quo eruditi\r\n              eloquentiam. Assum decore te sed. Elitr scripta ocurreret qui\r\n              ad.</span>\r\n          </div>\r\n          <a href=\"#\">Learn More</a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xs-12 col-sm-12 col-md-6 col-lg-6\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Activity</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <ng-scrollbar style=\"height: 400px\" visibility=\"hover\">\r\n              <div class=\"assign-style\">\r\n                <ul class=\"feedBody\">\r\n                  <li class=\"active-feed\">\r\n                    <div class=\"feed-user-img\">\r\n                      <img src=\"assets/images/user/user1.jpg\" class=\"img-radius \" alt=\"User-Profile-Image\">\r\n                    </div>\r\n                    <h6>\r\n                      <span class=\"feedLblStyle lblFileStyle\">File</span> Sarah Smith\r\n                      <small class=\"text-muted float-end\">6 hours ago</small>\r\n                    </h6>\r\n                    <p class=\"m-b-15 m-t-15\">\r\n                      hii John, I have upload doc related to task.\r\n                    </p>\r\n                  </li>\r\n                  <li class=\"diactive-feed\">\r\n                    <div class=\"feed-user-img\">\r\n                      <img src=\"assets/images/user/user2.jpg\" class=\"img-radius \" alt=\"User-Profile-Image\">\r\n                    </div>\r\n                    <h6>\r\n                      <span class=\"feedLblStyle lblTaskStyle\">Task </span> Jalpa Joshi\r\n                      <small class=\"text-muted float-end\">5 hours ago\r\n                      </small>\r\n                    </h6>\r\n                    <p class=\"m-b-15 m-t-15\">\r\n                      Please do as specify. Let me know if you have any query.\r\n                    </p>\r\n                  </li>\r\n                  <li class=\"diactive-feed\">\r\n                    <div class=\"feed-user-img\">\r\n                      <img src=\"assets/images/user/user3.jpg\" class=\"img-radius \" alt=\"User-Profile-Image\">\r\n                    </div>\r\n                    <h6>\r\n                      <span class=\"feedLblStyle lblCommentStyle\">comment</span> Lina Smith\r\n                      <small class=\"text-muted float-end\">6 hours ago</small>\r\n                    </h6>\r\n                    <p class=\"m-b-15 m-t-15\">\r\n                      Hey, How are you??\r\n                    </p>\r\n                  </li>\r\n                  <li class=\"active-feed\">\r\n                    <div class=\"feed-user-img\">\r\n                      <img src=\"assets/images/user/user4.jpg\" class=\"img-radius \" alt=\"User-Profile-Image\">\r\n                    </div>\r\n                    <h6>\r\n                      <span class=\"feedLblStyle lblReplyStyle\">Reply</span> Jacob Ryan\r\n                      <small class=\"text-muted float-end\">7 hours ago</small>\r\n                    </h6>\r\n                    <p class=\"m-b-15 m-t-15\">\r\n                      I am fine. You??\r\n                    </p>\r\n                  </li>\r\n                  <li class=\"active-feed\">\r\n                    <div class=\"feed-user-img\">\r\n                      <img src=\"assets/images/user/user5.jpg\" class=\"img-radius \" alt=\"User-Profile-Image\">\r\n                    </div>\r\n                    <h6>\r\n                      <span class=\"feedLblStyle lblFileStyle\">File</span> Sarah Smith\r\n                      <small class=\"text-muted float-end\">6 hours ago</small>\r\n                    </h6>\r\n                    <p class=\"m-b-15 m-t-15\">\r\n                      hii John, I have upload doc related to task.\r\n                    </p>\r\n                  </li>\r\n                  <li class=\"diactive-feed\">\r\n                    <div class=\"feed-user-img\">\r\n                      <img src=\"assets/images/user/user6.jpg\" class=\"img-radius \" alt=\"User-Profile-Image\">\r\n                    </div>\r\n                    <h6>\r\n                      <span class=\"feedLblStyle lblTaskStyle\">Task </span> Jalpa Joshi\r\n                      <small class=\"text-muted float-end\">5 hours ago\r\n                      </small>\r\n                    </h6>\r\n                    <p class=\"m-b-15 m-t-15\">\r\n                      Please do as specify. Let me know if you have any query.\r\n                    </p>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </ng-scrollbar>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xs-12 col-sm-12 col-md-6 col-lg-6\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Assign Task</h2>\r\n          </div>\r\n          <div class=\"tableBody\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-hover dashboard-task-infos\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>User</th>\r\n                    <th>Task</th>\r\n                    <th>Status</th>\r\n                    <th>Manager</th>\r\n                    <th>Progress</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr>\r\n                    <td class=\"table-img\">\r\n                      <img src=\"assets/images/user/user1.jpg\" alt=\"\">\r\n                    </td>\r\n                    <td>Task A</td>\r\n                    <td>\r\n                      <span class=\"label l-bg-green shadow-style\">Doing</span>\r\n                    </td>\r\n                    <td>John Doe</td>\r\n                    <td>\r\n                      <div class=\"progress shadow-style\">\r\n                        <div class=\"progress-bar l-bg-green width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                          aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td class=\"table-img\">\r\n                      <img src=\"assets/images/user/user2.jpg\" alt=\"\">\r\n                    </td>\r\n                    <td>Task B</td>\r\n                    <td>\r\n                      <span class=\"label l-bg-purple shadow-style\">To Do</span>\r\n                    </td>\r\n                    <td>John Doe</td>\r\n                    <td>\r\n                      <div class=\"progress shadow-style\">\r\n                        <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                          aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td class=\"table-img\">\r\n                      <img src=\"assets/images/user/user3.jpg\" alt=\"\">\r\n                    </td>\r\n                    <td>Task C</td>\r\n                    <td>\r\n                      <span class=\"label l-bg-orange shadow-style\">On Hold</span>\r\n                    </td>\r\n                    <td>John Doe</td>\r\n                    <td>\r\n                      <div class=\"progress shadow-style\">\r\n                        <div class=\"progress-bar l-bg-orange width-per-72\" role=\"progressbar\" aria-valuenow=\"72\"\r\n                          aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td class=\"table-img\">\r\n                      <img src=\"assets/images/user/user4.jpg\" alt=\"\">\r\n                    </td>\r\n                    <td>Task D</td>\r\n                    <td>\r\n                      <span class=\"label l-bg-cyan shadow-style\">Wait Approvel</span>\r\n                    </td>\r\n                    <td>John Doe</td>\r\n                    <td>\r\n                      <div class=\"progress shadow-style\">\r\n                        <div class=\"progress-bar l-bg-cyan width-per-95\" role=\"progressbar\" aria-valuenow=\"95\"\r\n                          aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td class=\"table-img\">\r\n                      <img src=\"assets/images/user/user5.jpg\" alt=\"\">\r\n                    </td>\r\n                    <td>Task E</td>\r\n                    <td>\r\n                      <span class=\"label bg-green shadow-style\">Suspended</span>\r\n                    </td>\r\n                    <td>John Doe</td>\r\n                    <td>\r\n                      <div class=\"progress shadow-style\">\r\n                        <div class=\"progress-bar l-bg-green width-per-87\" role=\"progressbar\" aria-valuenow=\"87\"\r\n                          aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td class=\"table-img\">\r\n                      <img src=\"assets/images/user/user1.jpg\" alt=\"\">\r\n                    </td>\r\n                    <td>Task A</td>\r\n                    <td>\r\n                      <span class=\"label bg-green shadow-style\">Doing</span>\r\n                    </td>\r\n                    <td>John Doe</td>\r\n                    <td>\r\n                      <div class=\"progress shadow-style\">\r\n                        <div class=\"progress-bar l-bg-green width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                          aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td class=\"table-img\">\r\n                      <img src=\"assets/images/user/user2.jpg\" alt=\"\">\r\n                    </td>\r\n                    <td>Task B</td>\r\n                    <td>\r\n                      <span class=\"label l-bg-purple shadow-style\">To Do</span>\r\n                    </td>\r\n                    <td>John Doe</td>\r\n                    <td>\r\n                      <div class=\"progress shadow-style\">\r\n                        <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                          aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row\">\r\n      <div class=\"col-xs-12 col-sm-12 col-md-6 col-lg-6\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>New Orders</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <ng-scrollbar style=\"height: 390px\" visibility=\"hover\">\r\n              <div id=\"new-orders\" class=\"media-list position-relative\">\r\n                <div class=\"table-responsive\">\r\n                  <table id=\"new-orders-table\" class=\"table table-hover table-xl mb-0\">\r\n                    <thead>\r\n                      <tr>\r\n                        <th class=\"border-top-0\">Product</th>\r\n                        <th class=\"border-top-0\">Customers</th>\r\n                        <th class=\"border-top-0\">Total</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">iPhone X</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$8999</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">Pixel 2</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$5550</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">OnePlus</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$9000</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">Galaxy</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$7500</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">Moto Z2</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$8500</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">iPhone X</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$8999</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">iPhone X</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$8999</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">Pixel 2</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$5550</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">OnePlus</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$9000</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td class=\"text-truncate\">Galaxy</td>\r\n                        <td class=\"text-truncate\">\r\n                          <ul class=\"list-unstyled order-list\">\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user1.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user2.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <img class=\"rounded-circle\" src=\"assets/images/user/user3.jpg\" alt=\"user\">\r\n                            </li>\r\n                            <li class=\"avatar avatar-sm\">\r\n                              <span class=\"badge\">+4</span>\r\n                            </li>\r\n                          </ul>\r\n                        </td>\r\n                        <td class=\"text-truncate\">$7500</td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n            </ng-scrollbar>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xs-12 col-sm-12 col-md-6 col-lg-6\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Task List</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <mat-tab-group>\r\n              <mat-tab>\r\n                <ng-template mat-tab-label>\r\n                  <img src=\"assets/images/user/user1.jpg\" alt=\"user\">Sarah Smith\r\n                </ng-template>\r\n                <div class=\"table-responsive mt-3\">\r\n                  <table class=\"table table-hover dashboard-task-infos\">\r\n                    <thead>\r\n                      <tr>\r\n                        <th>Task</th>\r\n                        <th>Status</th>\r\n                        <th>Manager</th>\r\n                        <th>Progress</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      <tr>\r\n                        <td>Task C</td>\r\n                        <td><span class=\"badge col-green\">Completed</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-green width-per-72\" role=\"progressbar\" aria-valuenow=\"72\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task A</td>\r\n                        <td><span class=\"badge col-red\">On Process</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-red width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task B</td>\r\n                        <td><span class=\"badge col-purple\">On Hold</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task D</td>\r\n                        <td><span class=\"badge col-green\">Completed</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-green width-per-72\" role=\"progressbar\" aria-valuenow=\"72\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task E</td>\r\n                        <td><span class=\"badge col-purple\">On Hold</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task F</td>\r\n                        <td><span class=\"badge col-purple\">On Hold</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </mat-tab>\r\n              <mat-tab>\r\n                <ng-template mat-tab-label>\r\n                  <img src=\"assets/images/user/user2.jpg\" alt=\"user\">Jalpa Joshi\r\n                </ng-template>\r\n                <div class=\"table-responsive mt-3\">\r\n                  <table class=\"table table-hover dashboard-task-infos\">\r\n                    <thead>\r\n                      <tr>\r\n                        <th>Task</th>\r\n                        <th>Status</th>\r\n                        <th>Manager</th>\r\n                        <th>Progress</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      <tr>\r\n                        <td>Task D</td>\r\n                        <td><span class=\"badge col-red\">On Process</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-red width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task E</td>\r\n                        <td><span class=\"badge col-purple\">On Hold</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task F</td>\r\n                        <td><span class=\"badge col-green\">Completed</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-green width-per-72\" role=\"progressbar\" aria-valuenow=\"72\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task G</td>\r\n                        <td><span class=\"badge col-red\">On Process</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-red width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task K</td>\r\n                        <td><span class=\"badge col-purple\">On Hold</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task M</td>\r\n                        <td><span class=\"badge col-red\">On Process</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-red width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </mat-tab>\r\n              <mat-tab>\r\n                <ng-template mat-tab-label>\r\n                  <img src=\"assets/images/user/user3.jpg\" alt=\"user\">Mark Peter\r\n                </ng-template>\r\n                <div class=\"table-responsive mt-3\">\r\n                  <table class=\"table table-hover dashboard-task-infos\">\r\n                    <thead>\r\n                      <tr>\r\n                        <th>Task</th>\r\n                        <th>Status</th>\r\n                        <th>Manager</th>\r\n                        <th>Progress</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      <tr>\r\n                        <td>Task E</td>\r\n                        <td><span class=\"badge col-purple\">On Hold</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task D</td>\r\n                        <td><span class=\"badge col-red\">On Process</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-red width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task F</td>\r\n                        <td><span class=\"badge col-green\">Completed</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-green width-per-72\" role=\"progressbar\" aria-valuenow=\"72\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task L</td>\r\n                        <td><span class=\"badge col-red\">On Process</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-red width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task H</td>\r\n                        <td><span class=\"badge col-purple\">On Hold</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-purple width-per-40\" role=\"progressbar\" aria-valuenow=\"40\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <td>Task L</td>\r\n                        <td><span class=\"badge col-red\">On Process</span></td>\r\n                        <td>John Doe</td>\r\n                        <td>\r\n                          <div class=\"progress shadow-style\">\r\n                            <div class=\"progress-bar l-bg-red width-per-62\" role=\"progressbar\" aria-valuenow=\"62\"\r\n                              aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </mat-tab>\r\n            </mat-tab-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-lg-6 col-md-6 col-sm-6 col-xs-12\">\r\n        <div class=\"card profile-header\">\r\n          <div class=\"body\">\r\n            <div class=\"row\">\r\n              <div class=\"col-lg-4 col-md-4 col-12\">\r\n                <div class=\"profile-image float-md-right\"> <img src=\"assets/images/user/usrbig6.jpg\" alt=\"\">\r\n                </div>\r\n              </div>\r\n              <div class=\"col-lg-8 col-md-8 col-12\">\r\n                <h6 class=\"m-t-0 m-b-0\">Sarah Smith</h6>\r\n                <span class=\"job_post\">Java Develper</span>\r\n                <p>102, Svayam Appartment, new P.L. road, Rajkot-369852</p>\r\n                <div>\r\n                  <button type=\"button\" class=\"btn btn-outline-success btn-border-radius msr-2\">Follow</button>\r\n                  <button type=\"button\" class=\"btn btn-outline-danger btn-border-radius\">Message</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-6 col-md-6 col-sm-6 col-xs-12\">\r\n        <div class=\"card profile-header\">\r\n          <div class=\"body\">\r\n            <div class=\"row\">\r\n              <div class=\"col-lg-4 col-md-4 col-12\">\r\n                <div class=\"profile-image float-md-right\"> <img src=\"assets/images/user/usrbig1.jpg\" alt=\"\">\r\n                </div>\r\n              </div>\r\n              <div class=\"col-lg-8 col-md-8 col-12\">\r\n                <h6 class=\"m-t-0 m-b-0\">Sarah Smith</h6>\r\n                <span class=\"job_post\">Java Develper</span>\r\n                <p>102, Svayam Appartment, new P.L. road, Rajkot-369852</p>\r\n                <div>\r\n                  <button type=\"button\" class=\"btn btn-outline-success btn-border-radius msr-2\">Follow</button>\r\n                  <button type=\"button\" class=\"btn btn-outline-danger btn-border-radius\">Message</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row\">\r\n      <div class=\"col-xs-12 col-sm-12 col-md-4 col-lg-4\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Recent Activities</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"sl-item sl-primary\">\r\n              <div class=\"sl-content\">\r\n                <small class=\"text-muted\">\r\n                  <i class=\"fa fa-user position-left\"></i> 5 mins ago</small>\r\n                <p>Lorem ipsum dolor sit amet conse ctetur which ascing elit users.</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"sl-item sl-danger\">\r\n              <div class=\"sl-content\">\r\n                <small class=\"text-muted\">\r\n                  <i class=\"fa fa-user position-left\"></i> 8 mins ago</small>\r\n                <p>Lorem ipsum dolor sit ametcon the sectetur that ascing elit users.</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"sl-item sl-success\">\r\n              <div class=\"sl-content\">\r\n                <small class=\"text-muted\">\r\n                  <i class=\"fa fa-user position-left\"></i> 10 mins ago</small>\r\n                <p>Lorem ipsum dolor sit amet cons the ecte tur and adip ascing elit users.</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"sl-item sl-warning\">\r\n              <div class=\"sl-content\">\r\n                <small class=\"text-muted\">\r\n                  <i class=\"fa fa-user position-left\"></i> 12 mins ago</small>\r\n                <p>Lorem ipsum dolor sit amet consec tetur adip ascing elit users.</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"sl-item sl-primary\">\r\n              <div class=\"sl-content\">\r\n                <small class=\"text-muted\">\r\n                  <i class=\"fa fa-user position-left\"></i> 5 mins ago</small>\r\n                <p>Lorem ipsum dolor sit amet conse ctetur which ascing elit users.</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xs-12 col-sm-12 col-md-4 col-lg-4\">\r\n        <div class=\"card\">\r\n          <div class=\"chat\">\r\n            <div class=\"chat-header clearfix\">\r\n              <img src=\"assets/images/user/user2.jpg\" alt=\"avatar\">\r\n              <div class=\"chat-about\">\r\n                <div class=\"chat-with\">Aiden Chavez</div>\r\n                <div class=\"chat-num-messages\">2 new messages</div>\r\n              </div>\r\n            </div>\r\n            <ng-scrollbar style=\"height: 270px\" visibility=\"hover\">\r\n              <div class=\"chat-history\" id=\"chat-conversation\">\r\n                <ul>\r\n                  <li class=\"clearfix\">\r\n                    <div class=\"message-data text-end\">\r\n                      <span class=\"message-data-time\">10:10 AM, Today</span> &nbsp; &nbsp;\r\n                      <span class=\"message-data-name\">Michael</span>\r\n                      <i class=\"zmdi zmdi-circle me\"></i>\r\n                    </div>\r\n                    <div class=\"message other-message float-end\"> Hi Aiden, how are you? How is\r\n                      the project coming along? </div>\r\n                  </li>\r\n                  <li>\r\n                    <div class=\"message-data\">\r\n                      <span class=\"message-data-name\">\r\n                        <i class=\"zmdi zmdi-circle online\"></i> Aiden</span>\r\n                      <span class=\"message-data-time\">10:12 AM, Today</span>\r\n                    </div>\r\n                    <div class=\"message my-message\">\r\n                      <p>Are we meeting today? Project has been already finished and I have\r\n                        results to\r\n                        show you.</p>\r\n                      <div class=\"row\">\r\n                      </div>\r\n                    </div>\r\n                  </li>\r\n                  <li>\r\n                    <div class=\"message-data\">\r\n                      <span class=\"message-data-name\">\r\n                        <i class=\"zmdi zmdi-circle online\"></i> Aiden</span>\r\n                      <span class=\"message-data-time\">10:12 AM, Today</span>\r\n                    </div>\r\n                    <div class=\"message my-message\">\r\n                      <p>Are we meeting today? Project has been already finished and I have\r\n                        results to\r\n                        show you.</p>\r\n                      <div class=\"row\">\r\n                      </div>\r\n                    </div>\r\n                  </li>\r\n                  <li class=\"clearfix\">\r\n                    <div class=\"message-data text-end\">\r\n                      <span class=\"message-data-time\">10:10 AM, Today</span> &nbsp; &nbsp;\r\n                      <span class=\"message-data-name\">Michael</span>\r\n                      <i class=\"zmdi zmdi-circle me\"></i>\r\n                    </div>\r\n                    <div class=\"message other-message float-end\"> Hi Aiden, how are you? How is\r\n                      the project coming along? </div>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </ng-scrollbar>\r\n            <div class=\"chat-message clearfix\">\r\n              <div class=\"form-group\">\r\n                <div class=\"form-line\">\r\n                  <mat-form-field class=\"example-full-width\">\r\n                    <input matInput placeholder=\"Enter text here..\">\r\n                  </mat-form-field>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xs-12 col-sm-12 col-md-4 col-lg-4\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Latest Posts</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"card-block\">\r\n              <div class=\"row m-b-20\">\r\n                <div class=\"col-auto p-r-0\">\r\n                  <img src=\"assets/images/posts/post1.jpg\" alt=\"user image\" class=\"latest-posts-img\">\r\n                </div>\r\n                <div class=\"col\">\r\n                  <h6>About Something</h6>\r\n                  <p class=\"text-muted m-b-5 font-12\">\r\n                    Video | 10 minutes ago\r\n                  </p>\r\n                  <p class=\"text-muted \">Lorem Ipsum is simply dummy text of the.</p>\r\n                </div>\r\n              </div>\r\n              <div class=\"row m-b-20\">\r\n                <div class=\"col-auto p-r-0\">\r\n                  <img src=\"assets/images/posts/post2.jpg\" alt=\"user image\" class=\"latest-posts-img\">\r\n                </div>\r\n                <div class=\"col\">\r\n                  <h6>Relationship</h6>\r\n                  <p class=\"text-muted m-b-5 font-12\">\r\n                    Video | 24 minutes ago\r\n                  </p>\r\n                  <p class=\"text-muted \">Lorem Ipsum is simply dummy text of the.</p>\r\n                </div>\r\n              </div>\r\n              <div class=\"row m-b-20\">\r\n                <div class=\"col-auto p-r-0\">\r\n                  <img src=\"assets/images/posts/post3.jpg\" alt=\"user image\" class=\"latest-posts-img\">\r\n                </div>\r\n                <div class=\"col\">\r\n                  <h6>Human body</h6>\r\n                  <p class=\"text-muted m-b-5 font-12\">\r\n                    Video | 53 minutes ago\r\n                  </p>\r\n                  <p class=\"text-muted \">Lorem Ipsum is simply dummy text of the.</p>\r\n                </div>\r\n              </div>\r\n              <div class=\"text-center\">\r\n                <a href=\"#!\" class=\"b-b-primary text-primary\">View All Posts</a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row clearfix\">\r\n      <div class=\"col-xs-12 col-sm-12 col-md-6 col-lg-6\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Notice Board</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"recent-comment\">\r\n              <div class=\"notice-board\">\r\n                <div class=\"table-img\">\r\n                  <img class=\"notice-object\" src=\"assets/images/user/user6.jpg\" alt=\"...\">\r\n                </div>\r\n                <div class=\"notice-body\">\r\n                  <h6 class=\"notice-heading col-green\">Airi Satou</h6>\r\n                  <p>Lorem ipsum dolor sit amet, id quo eruditi eloquentiam.</p>\r\n                  <small class=\"text-muted\">7 hours ago</small>\r\n                </div>\r\n              </div>\r\n              <div class=\"notice-board\">\r\n                <div class=\"table-img\">\r\n                  <img class=\"notice-object\" src=\"assets/images/user/user4.jpg\" alt=\"...\">\r\n                </div>\r\n                <div class=\"notice-body\">\r\n                  <h6 class=\"notice-heading color-primary col-indigo\">Sarah Smith</h6>\r\n                  <p>Lorem ipsum dolor sit amet, id quo eruditi eloquentiam.</p>\r\n                  <p class=\"comment-date\">1 hour ago</p>\r\n                </div>\r\n              </div>\r\n              <div class=\"notice-board\">\r\n                <div class=\"table-img\">\r\n                  <img class=\"notice-object\" src=\"assets/images/user/user3.jpg\" alt=\"...\">\r\n                </div>\r\n                <div class=\"notice-body\">\r\n                  <h6 class=\"notice-heading color-danger col-cyan\">Cara Stevens</h6>\r\n                  <p>Lorem ipsum dolor sit amet, id quo eruditi eloquentiam.</p>\r\n                  <div class=\"comment-date\">Yesterday</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"notice-board no-border\">\r\n                <div class=\"table-img\">\r\n                  <img class=\"notice-object\" src=\"assets/images/user/user7.jpg\" alt=\"...\">\r\n                </div>\r\n                <div class=\"notice-body\">\r\n                  <h6 class=\"notice-heading color-info col-orange\">Ashton Cox</h6>\r\n                  <p>Lorem ipsum dolor sit amet, id quo eruditi eloquentiam.</p>\r\n                  <div class=\"comment-date\">Yesterday</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xs-12 col-sm-12 col-md-6 col-lg-6\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Recent Activity</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <ul class=\"timeline\">\r\n              <li>\r\n                <div class=\"timeline-badge primary\">\r\n                  <img class=\"notice-object\" src=\"assets/images/user/user1.jpg\" alt=\"...\">\r\n                </div>\r\n                <div class=\"timeline-panel\">\r\n                  <div class=\"timeline-heading\">\r\n                    <h5 class=\"timeline-title\">Lorem ipsum dolor sit amet, id quo eruditi.</h5>\r\n                  </div>\r\n                  <div class=\"timeline-body\">\r\n                    <p>5 minutes ago</p>\r\n                  </div>\r\n                </div>\r\n              </li>\r\n              <li>\r\n                <div class=\"timeline-badge primary\">\r\n                  <img class=\"notice-object\" src=\"assets/images/user/user2.jpg\" alt=\"...\">\r\n                </div>\r\n                <div class=\"timeline-panel\">\r\n                  <div class=\"timeline-heading\">\r\n                    <h5 class=\"timeline-title\">Lorem ipsum dolor sit amet, id quo eruditi.</h5>\r\n                  </div>\r\n                  <div class=\"timeline-body\">\r\n                    <p>10 minutes ago</p>\r\n                  </div>\r\n                </div>\r\n              </li>\r\n              <li>\r\n                <div class=\"timeline-badge primary\">\r\n                  <img class=\"notice-object\" src=\"assets/images/user/user8.jpg\" alt=\"...\">\r\n                </div>\r\n                <div class=\"timeline-panel\">\r\n                  <div class=\"timeline-heading\">\r\n                    <h5 class=\"timeline-title\">Lorem ipsum dolor sit amet, id quo eruditi.</h5>\r\n                  </div>\r\n                  <div class=\"timeline-body\">\r\n                    <p>20 minutes ago</p>\r\n                  </div>\r\n                </div>\r\n              </li>\r\n              <li>\r\n                <div class=\"timeline-badge primary\">\r\n                  <img class=\"notice-object\" src=\"assets/images/user/user4.jpg\" alt=\"...\">\r\n                </div>\r\n                <div class=\"timeline-panel\">\r\n                  <div class=\"timeline-heading\">\r\n                    <h5 class=\"timeline-title\">Lorem ipsum dolor sit amet, id quo eruditi.</h5>\r\n                  </div>\r\n                  <div class=\"timeline-body\">\r\n                    <p>35 minutes ago</p>\r\n                  </div>\r\n                </div>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"row\">\r\n      <div class=\"col-xs-12 col-sm-12 col-md-4 col-lg-4\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Todo List</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <ng-scrollbar style=\"height: 370px\" visibility=\"hover\">\r\n              <div cdkDropList class=\"task-list\" (cdkDropListDropped)=\"drop($event)\">\r\n                @for (task of tasks; track task) {\r\n                <div class=\"task-box\" cdkDrag>\r\n                  <div>\r\n                    <div class=\"task-handle m-r-20\" cdkDragHandle>\r\n                      <mat-icon aria-hidden=\"false\">drag_indicator</mat-icon>\r\n                    </div>\r\n                  </div>\r\n                  <mat-checkbox (change)=\"toggle(task)\" [checked]=\"!!task.done\" class=\"m-r-15\" color=\"primary\">\r\n                  </mat-checkbox>\r\n                  <div class=\"task-custom-placeholder\" *cdkDragPlaceholder></div>\r\n                  <div matTooltip=\"Title\" [ngClass]=\"{done:task.done}\">\r\n                    {{task.title}}</div>\r\n                  <div\r\n                    [ngClass]=\"{'task-low': task.priority==='Low', 'task-high': task.priority==='High','task-normal': task.priority==='Normal'}\">\r\n                    @if (task?.priority === 'Low') {\r\n                    <mat-icon matTooltip=\"Low\" aria-hidden=\"false\" class=\"lbl-low\">\r\n                      arrow_downward\r\n                    </mat-icon>\r\n                    }\r\n                    @if (task?.priority === 'High') {\r\n                    <mat-icon matTooltip=\"High\" aria-hidden=\"false\" class=\"lbl-high\">\r\n                      arrow_upward\r\n                    </mat-icon>\r\n                    }\r\n                    @if (task?.priority === 'Normal') {\r\n                    <mat-icon matTooltip=\"Normal\" aria-hidden=\"false\" class=\"lbl-normal\">\r\n                      remove</mat-icon>\r\n                    }\r\n                    {{task.priority}}\r\n                  </div>\r\n                </div>\r\n                }\r\n              </div>\r\n            </ng-scrollbar>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n        <div class=\"card\">\r\n          <div class=\"header\">\r\n            <h2>Medications</h2>\r\n          </div>\r\n          <div class=\"body\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-borderless medicine-list\">\r\n                <tr>\r\n                  <td><i class=\"fas fa-tablets pill-style\"></i> Econochlor</td>\r\n                  <td class=\"text-end w-25\">\r\n                    <span class=\"badge-outline\">1 - 0 - 1</span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td><i class=\"fas fa-capsules pill-style\"></i> Desmopressin tabs</td>\r\n                  <td class=\"text-end w-25\">\r\n                    <span class=\"badge-outline\">1 - 1 - 1</span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td><i class=\"fas fa-syringe pill-style\"></i> Abciximab-injection</td>\r\n                  <td class=\"text-end w-25\">\r\n                    <span class=\"badge-outline\">1 Daily</span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td><i class=\"fas fa-pills pill-style\"></i> Kevzara sarilumab</td>\r\n                  <td class=\"text-end w-25\">\r\n                    <span class=\"badge-outline\">0 - 0 - 1</span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td><i class=\"fas fa-capsules pill-style\"></i> Gentamicin-topical</td>\r\n                  <td class=\"text-end w-25\">\r\n                    <span class=\"badge-outline\">1 - 0 - 1</span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td><i class=\"fas fa-tablets pill-style\"></i> Paliperidone palmitate</td>\r\n                  <td class=\"text-end w-25\">\r\n                    <span class=\"badge-outline\">1 - 1 - 1</span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <td><i class=\"fas fa-syringe pill-style\"></i> Sermorelin-injectable</td>\r\n                  <td class=\"text-end w-25\">\r\n                    <span class=\"badge-outline\">1 Daily</span>\r\n                  </td>\r\n                </tr>\r\n              </table>\r\n            </div>\r\n            <div class=\"text-center p-t-20\">\r\n              <button mat-stroked-button color=\"primary\">Report Adverse Effect</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12\">\r\n        <div class=\"plain-card\">\r\n          <div class=\"card-inner\">\r\n            <h6 class=\"tx-primary m-b-15\">New Admission Report</h6>\r\n            <div class=\"row mb-2\">\r\n              <div class=\"col-4\">\r\n                <label class=\"font-16 font-b-500\">Today</label>\r\n                <h6>105<i class=\"material-icons col-green font-20\">trending_up</i></h6>\r\n              </div>\r\n              <div class=\"col-4\">\r\n                <label class=\"font-16 font-b-500\">This Week</label>\r\n                <h6>825<i class=\"material-icons col-orange font-20\">trending_down</i></h6>\r\n              </div>\r\n              <div class=\"col-4\">\r\n                <label class=\"font-16 font-b-500\">This Month</label>\r\n                <h6>22,067<i class=\"material-icons col-green font-20\">trending_up</i></h6>\r\n              </div>\r\n            </div>\r\n            <div class=\"progress shadow-style mb-2 mt-3\">\r\n              <div class=\"progress-bar l-bg-green width-per-70\" role=\"progressbar\" aria-valuenow=\"70\" aria-valuemin=\"0\"\r\n                aria-valuemax=\"100\">70%</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"plain-card mt-4\">\r\n          <div class=\"card-inner\">\r\n            <h6 class=\"tx-primary m-b-15\">Fees Collection Report</h6>\r\n            <div class=\"row mb-2\">\r\n              <div class=\"col-4\">\r\n                <label class=\"font-16 font-b-500\">Today</label>\r\n                <h6>$147<i class=\"material-icons col-orange font-20\">trending_down</i></h6>\r\n              </div>\r\n              <div class=\"col-4\">\r\n                <label class=\"font-16 font-b-500\">This Week</label>\r\n                <h6>$968<i class=\"material-icons col-green font-20\">trending_up</i></h6>\r\n              </div>\r\n              <div class=\"col-4\">\r\n                <label class=\"font-16 font-b-500\">This Month</label>\r\n                <h6>$1,147<i class=\"material-icons col-orange font-20\">trending_down</i></h6>\r\n              </div>\r\n            </div>\r\n            <div class=\"progress shadow-style mb-2 mt-3\">\r\n              <div class=\"progress-bar l-bg-red width-per-50\" role=\"progressbar\" aria-valuenow=\"50\" aria-valuemin=\"0\"\r\n                aria-valuemax=\"100\">50%</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AACA,SAEEA,eAAe,EACfC,WAAW,EACXC,OAAO,EACPC,aAAa,EACbC,kBAAkB,QACb,wBAAwB;AAC/B,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,mBAAmB,QAAQ,oDAAoD;;;;;;;;;;;;ICi2BtEC,EAAA,CAAAC,SAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,mBACrD;;;;;IAoFEF,EAAA,CAAAC,SAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,mBACrD;;;;;IAoFEF,EAAA,CAAAC,SAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,kBACrD;;;;;IAmbEF,EAAA,CAAAC,SAAA,eAA+D;;;;;IAM7DD,EAAA,CAAAG,cAAA,oBAA+D;IAC7DH,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAI,YAAA,EAAW;;;;;IAGXJ,EAAA,CAAAG,cAAA,oBAAiE;IAC/DH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAI,YAAA,EAAW;;;;;IAGXJ,EAAA,CAAAG,cAAA,oBAAqE;IACnEH,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAI,YAAA,EAAW;;;;;;;;;;;;;;IAzBvBJ,EAAA,CAAAG,cAAA,eAA8B;IAGMH,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAI,YAAA,EAAW;IAG3DJ,EAAA,CAAAG,cAAA,wBAA6F;IAA/EH,EAAA,CAAAK,UAAA,oBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAUb,EAAA,CAAAc,WAAA,CAAAF,OAAA,CAAAG,MAAA,CAAAL,OAAA,CAAY;IAAA,EAAC;IACrCV,EAAA,CAAAI,YAAA,EAAe;IACfJ,EAAA,CAAAgB,UAAA,IAAAC,2CAAA,mBAA+D;IAC/DjB,EAAA,CAAAG,cAAA,eAAqD;IACnDH,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAI,YAAA,EAAM;IACtBJ,EAAA,CAAAG,cAAA,eAC+H;IAC7HH,EAAA,CAAAgB,UAAA,KAAAE,oDAAA,wBAIC,KAAAC,oDAAA,6BAAAC,oDAAA;IAUDpB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;IAtBgCJ,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAsB,UAAA,cAAAZ,OAAA,CAAAa,IAAA,CAAuB;IAGrCvB,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAAf,OAAA,CAAAa,IAAA,EAA4B;IAClDvB,EAAA,CAAAqB,SAAA,EAAc;IAAdrB,EAAA,CAAA0B,kBAAA,MAAAhB,OAAA,CAAAiB,KAAA,KAAc;IAEd3B,EAAA,CAAAqB,SAAA,EAA4H;IAA5HrB,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAA4B,eAAA,KAAAC,GAAA,EAAAnB,OAAA,CAAAoB,QAAA,YAAApB,OAAA,CAAAoB,QAAA,aAAApB,OAAA,CAAAoB,QAAA,eAA4H;IAC5H9B,EAAA,CAAAqB,SAAA,EAIC;IAJDrB,EAAA,CAAA+B,aAAA,MAAArB,OAAA,kBAAAA,OAAA,CAAAoB,QAAA,sBAIC;IACD9B,EAAA,CAAAqB,SAAA,EAIC;IAJDrB,EAAA,CAAA+B,aAAA,MAAArB,OAAA,kBAAAA,OAAA,CAAAoB,QAAA,uBAIC;IACD9B,EAAA,CAAAqB,SAAA,EAGC;IAHDrB,EAAA,CAAA+B,aAAA,MAAArB,OAAA,kBAAAA,OAAA,CAAAoB,QAAA,yBAGC;IACD9B,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA0B,kBAAA,MAAAhB,OAAA,CAAAoB,QAAA,MACF;;;;AD37ClB,OAAM,MAAOE,mBAAmB;EAC9BC,YAAA;IAIA;IACA,KAAAC,KAAK,GAAG,CACN;MACEC,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,yBAAyB;MAChCJ,IAAI,EAAE,IAAI;MACVO,QAAQ,EAAE;KACX,EACD;MACEK,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,8BAA8B;MACrCJ,IAAI,EAAE,KAAK;MACXO,QAAQ,EAAE;KACX,EACD;MACEK,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,qBAAqB;MAC5BJ,IAAI,EAAE,KAAK;MACXO,QAAQ,EAAE;KACX,EACD;MACEK,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,2BAA2B;MAClCJ,IAAI,EAAE,IAAI;MACVO,QAAQ,EAAE;KACX,EACD;MACEK,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,iBAAiB;MACxBJ,IAAI,EAAE,KAAK;MACXO,QAAQ,EAAE;KACX,EACD;MACEK,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,0BAA0B;MACjCJ,IAAI,EAAE,KAAK;MACXO,QAAQ,EAAE;KACX,EACD;MACEK,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,gBAAgB;MACvBJ,IAAI,EAAE,IAAI;MACVO,QAAQ,EAAE;KACX,EACD;MACEK,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,uBAAuB;MAC9BJ,IAAI,EAAE,KAAK;MACXO,QAAQ,EAAE;KACX,EACD;MACEK,EAAE,EAAE,GAAG;MACPR,KAAK,EAAE,uBAAuB;MAC9BJ,IAAI,EAAE,KAAK;MACXO,QAAQ,EAAE;KACX,CACF;IA3DC;EACF;EA4DAM,IAAIA,CAACC,KAA4B;IAC/BrD,eAAe,CAAC,IAAI,CAACkD,KAAK,EAAEG,KAAK,CAACC,aAAa,EAAED,KAAK,CAACE,YAAY,CAAC;EACtE;EAEAxB,MAAMA,CAACyB,IAAuB;IAC5BA,IAAI,CAACjB,IAAI,GAAG,CAACiB,IAAI,CAACjB,IAAI;EACxB;EAAC,QAAAkB,CAAA,G;qBArEUT,mBAAmB;EAAA;EAAA,QAAAU,EAAA,G;UAAnBV,mBAAmB;IAAAW,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA7C,EAAA,CAAA8C,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3ChCpD,EAAA,CAAAG,cAAA,iBAAyB;QAInBH,EAAA,CAAAC,SAAA,wBAA0G;QAC5GD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,aAAiB;QAILH,EAAA,CAAAE,MAAA,oBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAExBJ,EAAA,CAAAG,cAAA,cAAuB;QAKTH,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAChBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAGrBJ,EAAA,CAAAG,cAAA,aAAO;QAECH,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAClBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAC,SAAA,4BAAgF;QACvFD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEnBJ,EAAA,CAAAG,cAAA,UAAI;QACEH,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACpBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAC,SAAA,4BAAmE;QAC1ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEnBJ,EAAA,CAAAG,cAAA,UAAI;QACEH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAC,SAAA,4BAAmE;QAC1ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEnBJ,EAAA,CAAAG,cAAA,UAAI;QACEH,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACzBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAC,SAAA,4BAAmE;QAC1ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEnBJ,EAAA,CAAAG,cAAA,UAAI;QACEH,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAChBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAC,SAAA,4BAAgF;QACvFD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEnBJ,EAAA,CAAAG,cAAA,UAAI;QACEH,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACzBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAC,SAAA,4BAAmE;QAC1ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEnBJ,EAAA,CAAAG,cAAA,UAAI;QACEH,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAClBJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAC,SAAA,4BAAgF;QACvFD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,UAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAQ/BJ,EAAA,CAAAG,cAAA,eAAmD;QAGzCH,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtBJ,EAAA,CAAAG,cAAA,eAAkB;QAKiEH,EAAA,CAAAC,SAAA,aACxC;QAAAD,EAAA,CAAAI,YAAA,EAAO;QACxCJ,EAAA,CAAAG,cAAA,eAAsB;QACKH,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAC9CJ,EAAA,CAAAG,cAAA,SAAG;QAAAH,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAErBJ,EAAA,CAAAG,cAAA,eAAqB;QAEjBH,EAAA,CAAAC,SAAA,aAAsC;QAExCD,EAAA,CAAAI,YAAA,EAAK;QAGTJ,EAAA,CAAAG,cAAA,cAAwB;QACmDH,EAAA,CAAAC,SAAA,cACvC;QAAAD,EAAA,CAAAI,YAAA,EAAO;QACzCJ,EAAA,CAAAG,cAAA,gBAAsB;QACKH,EAAA,CAAAE,MAAA,uBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAC5CJ,EAAA,CAAAG,cAAA,UAAG;QAAAH,EAAA,CAAAE,MAAA,qBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAErBJ,EAAA,CAAAG,cAAA,gBAAqB;QAEjBH,EAAA,CAAAC,SAAA,cAAsC;QAExCD,EAAA,CAAAI,YAAA,EAAK;QAGTJ,EAAA,CAAAG,cAAA,eAAwB;QACiDH,EAAA,CAAAC,SAAA,cACvC;QAAAD,EAAA,CAAAI,YAAA,EAAO;QACvCJ,EAAA,CAAAG,cAAA,gBAAsB;QACKH,EAAA,CAAAE,MAAA,4BAAmB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjDJ,EAAA,CAAAG,cAAA,UAAG;QAAAH,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAEtBJ,EAAA,CAAAG,cAAA,gBAAqB;QAEjBH,EAAA,CAAAC,SAAA,cAAsC;QAExCD,EAAA,CAAAI,YAAA,EAAK;QAGTJ,EAAA,CAAAG,cAAA,eAAwB;QAC6CH,EAAA,CAAAC,SAAA,cAC/B;QAAAD,EAAA,CAAAI,YAAA,EAAO;QAC3CJ,EAAA,CAAAG,cAAA,gBAAsB;QACKH,EAAA,CAAAE,MAAA,+BAAsB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACpDJ,EAAA,CAAAG,cAAA,UAAG;QAAAH,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAEtBJ,EAAA,CAAAG,cAAA,gBAAqB;QAEjBH,EAAA,CAAAC,SAAA,cAAsC;QAExCD,EAAA,CAAAI,YAAA,EAAK;QAGTJ,EAAA,CAAAG,cAAA,eAAwB;QACmDH,EAAA,CAAAC,SAAA,cACxC;QAAAD,EAAA,CAAAI,YAAA,EAAO;QACxCJ,EAAA,CAAAG,cAAA,gBAAsB;QACKH,EAAA,CAAAE,MAAA,+BAAsB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACpDJ,EAAA,CAAAG,cAAA,UAAG;QAAAH,EAAA,CAAAE,MAAA,qBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAErBJ,EAAA,CAAAG,cAAA,gBAAqB;QAEjBH,EAAA,CAAAC,SAAA,cAAsC;QAExCD,EAAA,CAAAI,YAAA,EAAK;QAKbJ,EAAA,CAAAG,cAAA,gBAAgC;QACaH,EAAA,CAAAE,MAAA,qBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAS;QAM1EJ,EAAA,CAAAG,cAAA,eAAmD;QAI3CH,EAAA,CAAAE,MAAA,iCACF;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEPJ,EAAA,CAAAG,cAAA,gBAAuB;QAIfH,EAAA,CAAAC,SAAA,gBAA8F;QAC9FD,EAAA,CAAAG,cAAA,gBAAsB;QACwBH,EAAA,CAAAE,MAAA,qBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC9DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAE9DJ,EAAA,CAAAG,cAAA,gBAAqB;QACyBH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC7DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAGnEJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAC,SAAA,gBAA8F;QAC9FD,EAAA,CAAAG,cAAA,gBAAsB;QACwBH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC5DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAE1DJ,EAAA,CAAAG,cAAA,gBAAqB;QACyBH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC7DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAGnEJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAC,SAAA,gBAA8F;QAC9FD,EAAA,CAAAG,cAAA,gBAAsB;QACwBH,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC/DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAE/DJ,EAAA,CAAAG,cAAA,gBAAqB;QACyBH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC7DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAGnEJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAC,SAAA,gBAA8F;QAC9FD,EAAA,CAAAG,cAAA,gBAAsB;QACwBH,EAAA,CAAAE,MAAA,uBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAChEJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAE9DJ,EAAA,CAAAG,cAAA,gBAAqB;QACyBH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC7DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAGnEJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAC,SAAA,gBAA8F;QAC9FD,EAAA,CAAAG,cAAA,gBAAsB;QACwBH,EAAA,CAAAE,MAAA,qBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC9DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAE9DJ,EAAA,CAAAG,cAAA,gBAAqB;QACyBH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC7DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAGnEJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAC,SAAA,gBAA8F;QAC9FD,EAAA,CAAAG,cAAA,gBAAsB;QACwBH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC5DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAE1DJ,EAAA,CAAAG,cAAA,gBAAqB;QACyBH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAC7DJ,EAAA,CAAAG,cAAA,gBAA8C;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAS/EJ,EAAA,CAAAG,cAAA,gBAA0B;QAKdH,EAAA,CAAAE,MAAA,wBAAe;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAE1BJ,EAAA,CAAAG,cAAA,gBAAkB;QAIVH,EAAA,CAAAC,SAAA,gBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,gBAAiB;QACSH,EAAA,CAAAE,MAAA,oBACtB;QAAAF,EAAA,CAAAG,cAAA,iBAAkD;QAACH,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAEtEJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAClCJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAClCJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAClCJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACvCJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACzCJ,EAAA,CAAAG,cAAA,cAAoC;QAAAH,EAAA,CAAAE,MAAA,6NAIkB;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAC1DJ,EAAA,CAAAG,cAAA,cAAa;QACsBH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAE/CJ,EAAA,CAAAG,cAAA,cAAa;QAC8BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAI7DJ,EAAA,CAAAG,cAAA,eAAiB;QAEbH,EAAA,CAAAC,SAAA,gBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,gBAAiB;QACSH,EAAA,CAAAE,MAAA,kBACtB;QAAAF,EAAA,CAAAG,cAAA,iBAAkD;QAACH,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAEtEJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAClCJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACvCJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACzCJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACzCJ,EAAA,CAAAG,cAAA,cAA0B;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACzCJ,EAAA,CAAAG,cAAA,cAAoC;QAAAH,EAAA,CAAAE,MAAA,qNAIU;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAClDJ,EAAA,CAAAG,cAAA,cAAa;QACsBH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAE/CJ,EAAA,CAAAG,cAAA,cAAa;QAC8BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAI7DJ,EAAA,CAAAG,cAAA,gBAAgC;QACgBH,EAAA,CAAAE,MAAA,kCAAyB;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAOrFJ,EAAA,CAAAG,cAAA,gBAAuC;QAG7BH,EAAA,CAAAE,MAAA,uBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEzBJ,EAAA,CAAAG,cAAA,gBAAkB;QAEVH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAElBJ,EAAA,CAAAG,cAAA,gBAAuC;QAIFH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAC9CJ,EAAA,CAAAG,cAAA,iBAAgE;QAAAH,EAAA,CAAAE,MAAA,YAAG;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE5EJ,EAAA,CAAAG,cAAA,gBAAkD;QAChDH,EAAA,CAAAC,SAAA,gBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAG,cAAA,WAAI;QAEyCH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAC1DJ,EAAA,CAAAG,cAAA,iBAAkE;QAAAH,EAAA,CAAAE,MAAA,YAAG;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE9EJ,EAAA,CAAAG,cAAA,gBAAkD;QAChDH,EAAA,CAAAC,SAAA,gBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAG,cAAA,WAAI;QAEyCH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACzDJ,EAAA,CAAAG,cAAA,iBAAmE;QAAAH,EAAA,CAAAE,MAAA,YAAG;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE/EJ,EAAA,CAAAG,cAAA,gBAAkD;QAChDH,EAAA,CAAAC,SAAA,gBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAG,cAAA,WAAI;QAEyCH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACrDJ,EAAA,CAAAG,cAAA,iBAAmE;QAAAH,EAAA,CAAAE,MAAA,YAAG;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE/EJ,EAAA,CAAAG,cAAA,gBAAkD;QAChDH,EAAA,CAAAC,SAAA,gBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAG,cAAA,WAAI;QAEyCH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACtDJ,EAAA,CAAAG,cAAA,iBAAmE;QAAAH,EAAA,CAAAE,MAAA,YAAG;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE/EJ,EAAA,CAAAG,cAAA,gBAAkD;QAChDH,EAAA,CAAAC,SAAA,gBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAQpBJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,cAA6C;QAC7CD,EAAA,CAAAG,cAAA,gBAA0B;QACpBH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAElBJ,EAAA,CAAAG,cAAA,gBAAyB;QACjBH,EAAA,CAAAE,MAAA,qHAED;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAEdJ,EAAA,CAAAG,cAAA,cAAY;QAAAH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAG9BJ,EAAA,CAAAG,cAAA,gBAAkD;QAE9CH,EAAA,CAAAC,SAAA,cAA8C;QAC9CD,EAAA,CAAAG,cAAA,gBAA0B;QACpBH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEpBJ,EAAA,CAAAG,cAAA,gBAAyB;QACjBH,EAAA,CAAAE,MAAA,qHAED;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAEdJ,EAAA,CAAAG,cAAA,cAAY;QAAAH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAG9BJ,EAAA,CAAAG,cAAA,gBAAkD;QAE9CH,EAAA,CAAAC,SAAA,cAAgD;QAChDD,EAAA,CAAAG,cAAA,gBAA0B;QACpBH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEnBJ,EAAA,CAAAG,cAAA,gBAAyB;QACjBH,EAAA,CAAAE,MAAA,qHAED;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAEdJ,EAAA,CAAAG,cAAA,cAAY;QAAAH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAIhCJ,EAAA,CAAAG,cAAA,gBAA0B;QAIdH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEnBJ,EAAA,CAAAG,cAAA,gBAAkB;QAMNH,EAAA,CAAAC,SAAA,gBAAqF;QACvFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,WAAI;QACsCH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAACJ,EAAA,CAAAE,MAAA,sBACpD;QAAAF,EAAA,CAAAG,cAAA,mBAAoC;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAEzDJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAE,MAAA,uDACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAENJ,EAAA,CAAAG,cAAA,gBAA0B;QAEtBH,EAAA,CAAAC,SAAA,iBAAqF;QACvFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,WAAI;QACsCH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAACJ,EAAA,CAAAE,MAAA,sBACrD;QAAAF,EAAA,CAAAG,cAAA,mBAAoC;QAAAH,EAAA,CAAAE,MAAA,qBACpC;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAEVJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAE,MAAA,mEACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAENJ,EAAA,CAAAG,cAAA,gBAA0B;QAEtBH,EAAA,CAAAC,SAAA,iBAAqF;QACvFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,WAAI;QACyCH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAACJ,EAAA,CAAAE,MAAA,qBAC1D;QAAAF,EAAA,CAAAG,cAAA,mBAAoC;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAEzDJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAE,MAAA,6BACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAENJ,EAAA,CAAAG,cAAA,eAAwB;QAEpBH,EAAA,CAAAC,SAAA,iBAAqF;QACvFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,WAAI;QACuCH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAACJ,EAAA,CAAAE,MAAA,qBACtD;QAAAF,EAAA,CAAAG,cAAA,mBAAoC;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAEzDJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAE,MAAA,2BACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAENJ,EAAA,CAAAG,cAAA,eAAwB;QAEpBH,EAAA,CAAAC,SAAA,iBAAqF;QACvFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,WAAI;QACsCH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAACJ,EAAA,CAAAE,MAAA,sBACpD;QAAAF,EAAA,CAAAG,cAAA,mBAAoC;QAAAH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAEzDJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAE,MAAA,uDACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAENJ,EAAA,CAAAG,cAAA,gBAA0B;QAEtBH,EAAA,CAAAC,SAAA,iBAAqF;QACvFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,WAAI;QACsCH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAACJ,EAAA,CAAAE,MAAA,sBACrD;QAAAF,EAAA,CAAAG,cAAA,mBAAoC;QAAAH,EAAA,CAAAE,MAAA,qBACpC;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAEVJ,EAAA,CAAAG,cAAA,eAAyB;QACvBH,EAAA,CAAAE,MAAA,mEACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAQlBJ,EAAA,CAAAG,cAAA,gBAAmD;QAGzCH,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtBJ,EAAA,CAAAG,cAAA,eAAuB;QAKTH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACbJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACbJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAChBJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAGrBJ,EAAA,CAAAG,cAAA,cAAO;QAGDH,EAAA,CAAAC,SAAA,gBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAC0CH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE1DJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,gBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAC2CH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE3DJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAC2CH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE7DJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QACyCH,EAAA,CAAAE,MAAA,sBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAEjEJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QACwCH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE5DJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,gBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QACwCH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAExDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,gBAA+C;QACjDD,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAC2CH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAE3DJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAUxBJ,EAAA,CAAAG,cAAA,eAAiB;QAILH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAErBJ,EAAA,CAAAG,cAAA,gBAAkB;QAOqBH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACrCJ,EAAA,CAAAG,cAAA,gBAAyB;QAAAH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACvCJ,EAAA,CAAAG,cAAA,gBAAyB;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAGvCJ,EAAA,CAAAG,cAAA,cAAO;QAEuBH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACvCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACtCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACtCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACrCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACtCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACvCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACvCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACtCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACtCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtCJ,EAAA,CAAAG,cAAA,WAAI;QACwBH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACrCJ,EAAA,CAAAG,cAAA,gBAA0B;QAGpBH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QAC3BH,EAAA,CAAAC,SAAA,iBAA0E;QAC5ED,EAAA,CAAAI,YAAA,EAAK;QACLJ,EAAA,CAAAG,cAAA,gBAA6B;QACPH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAInCJ,EAAA,CAAAG,cAAA,gBAA0B;QAAAH,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAUtDJ,EAAA,CAAAG,cAAA,gBAAmD;QAGzCH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEpBJ,EAAA,CAAAG,cAAA,gBAAkB;QAGZH,EAAA,CAAAgB,UAAA,MAAAsC,4CAAA,2BAEc;QACdtD,EAAA,CAAAG,cAAA,iBAAmC;QAIvBH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACbJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAChBJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAGrBJ,EAAA,CAAAG,cAAA,cAAO;QAECH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA8BH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAClDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA4BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA+BH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA8BH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAClDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA+BH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA+BH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAOlBJ,EAAA,CAAAG,cAAA,gBAAS;QACPH,EAAA,CAAAgB,UAAA,MAAAuC,4CAAA,2BAEc;QACdvD,EAAA,CAAAG,cAAA,iBAAmC;QAIvBH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACbJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAChBJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAGrBJ,EAAA,CAAAG,cAAA,cAAO;QAECH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA4BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA+BH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA8BH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAClDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA4BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA+BH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA4BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAOlBJ,EAAA,CAAAG,cAAA,gBAAS;QACPH,EAAA,CAAAgB,UAAA,MAAAwC,4CAAA,2BAEc;QACdxD,EAAA,CAAAG,cAAA,iBAAmC;QAIvBH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACbJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAChBJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAGrBJ,EAAA,CAAAG,cAAA,cAAO;QAECH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA+BH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA4BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA8BH,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAClDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA4BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA+BH,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAGVJ,EAAA,CAAAG,cAAA,WAAI;QACEH,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAG,cAAA,WAAI;QAA4BH,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACjDJ,EAAA,CAAAG,cAAA,WAAI;QAAAH,EAAA,CAAAE,MAAA,iBAAQ;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACjBJ,EAAA,CAAAG,cAAA,WAAI;QAEAH,EAAA,CAAAC,SAAA,iBAC8C;QAChDD,EAAA,CAAAI,YAAA,EAAM;QAY5BJ,EAAA,CAAAG,cAAA,gBAA0B;QAM6BH,EAAA,CAAAC,SAAA,kBAAiD;QAC5FD,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAG,cAAA,kBAAsC;QACZH,EAAA,CAAAE,MAAA,qBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACxCJ,EAAA,CAAAG,cAAA,mBAAuB;QAAAH,EAAA,CAAAE,MAAA,uBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAC3CJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,8DAAoD;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAC3DJ,EAAA,CAAAG,cAAA,aAAK;QAC2EH,EAAA,CAAAE,MAAA,gBAAM;QAAAF,EAAA,CAAAI,YAAA,EAAS;QAC7FJ,EAAA,CAAAG,cAAA,qBAAuE;QAAAH,EAAA,CAAAE,MAAA,iBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAS;QAOnGJ,EAAA,CAAAG,cAAA,kBAAkD;QAKGH,EAAA,CAAAC,SAAA,kBAAiD;QAC5FD,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAG,cAAA,kBAAsC;QACZH,EAAA,CAAAE,MAAA,qBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACxCJ,EAAA,CAAAG,cAAA,mBAAuB;QAAAH,EAAA,CAAAE,MAAA,uBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAC3CJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,8DAAoD;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAC3DJ,EAAA,CAAAG,cAAA,aAAK;QAC2EH,EAAA,CAAAE,MAAA,gBAAM;QAAAF,EAAA,CAAAI,YAAA,EAAS;QAC7FJ,EAAA,CAAAG,cAAA,qBAAuE;QAAAH,EAAA,CAAAE,MAAA,iBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAS;QAQrGJ,EAAA,CAAAG,cAAA,gBAAiB;QAILH,EAAA,CAAAE,MAAA,2BAAiB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAE5BJ,EAAA,CAAAG,cAAA,iBAAkB;QAIVH,EAAA,CAAAC,SAAA,gBAAwC;QAACD,EAAA,CAAAE,MAAA,qBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAC7DJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,0EAAgE;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAG3EJ,EAAA,CAAAG,cAAA,kBAA+B;QAGzBH,EAAA,CAAAC,SAAA,gBAAwC;QAACD,EAAA,CAAAE,MAAA,qBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAC7DJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,4EAAkE;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAG7EJ,EAAA,CAAAG,cAAA,kBAAgC;QAG1BH,EAAA,CAAAC,SAAA,gBAAwC;QAACD,EAAA,CAAAE,MAAA,sBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAC9DJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,kFAAwE;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAGnFJ,EAAA,CAAAG,cAAA,kBAAgC;QAG1BH,EAAA,CAAAC,SAAA,gBAAwC;QAACD,EAAA,CAAAE,MAAA,sBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAC9DJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,yEAA+D;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAG1EJ,EAAA,CAAAG,cAAA,kBAAgC;QAG1BH,EAAA,CAAAC,SAAA,gBAAwC;QAACD,EAAA,CAAAE,MAAA,qBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAC7DJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,0EAAgE;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAMjFJ,EAAA,CAAAG,cAAA,iBAAmD;QAI3CH,EAAA,CAAAC,SAAA,kBAAqD;QACrDD,EAAA,CAAAG,cAAA,kBAAwB;QACCH,EAAA,CAAAE,MAAA,sBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAM;QACzCJ,EAAA,CAAAG,cAAA,kBAA+B;QAAAH,EAAA,CAAAE,MAAA,wBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAGvDJ,EAAA,CAAAG,cAAA,2BAAuD;QAKbH,EAAA,CAAAE,MAAA,yBAAe;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAACJ,EAAA,CAAAE,MAAA,yBACvD;QAAAF,EAAA,CAAAG,cAAA,mBAAgC;QAAAH,EAAA,CAAAE,MAAA,iBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAC9CJ,EAAA,CAAAC,SAAA,gBAAmC;QACrCD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAA6C;QAACH,EAAA,CAAAE,MAAA,mEAClB;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAEpCJ,EAAA,CAAAG,cAAA,YAAI;QAGEH,EAAA,CAAAC,SAAA,gBAAuC;QAACD,EAAA,CAAAE,MAAA,gBAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACtDJ,EAAA,CAAAG,cAAA,mBAAgC;QAAAH,EAAA,CAAAE,MAAA,yBAAe;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAExDJ,EAAA,CAAAG,cAAA,kBAAgC;QAC3BH,EAAA,CAAAE,MAAA,iGAEQ;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACfJ,EAAA,CAAAC,SAAA,gBACM;QACRD,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAG,cAAA,YAAI;QAGEH,EAAA,CAAAC,SAAA,gBAAuC;QAACD,EAAA,CAAAE,MAAA,gBAAK;QAAAF,EAAA,CAAAI,YAAA,EAAO;QACtDJ,EAAA,CAAAG,cAAA,mBAAgC;QAAAH,EAAA,CAAAE,MAAA,yBAAe;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAExDJ,EAAA,CAAAG,cAAA,kBAAgC;QAC3BH,EAAA,CAAAE,MAAA,iGAEQ;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACfJ,EAAA,CAAAC,SAAA,gBACM;QACRD,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAG,cAAA,iBAAqB;QAEeH,EAAA,CAAAE,MAAA,yBAAe;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAACJ,EAAA,CAAAE,MAAA,yBACvD;QAAAF,EAAA,CAAAG,cAAA,mBAAgC;QAAAH,EAAA,CAAAE,MAAA,iBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAC9CJ,EAAA,CAAAC,SAAA,gBAAmC;QACrCD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAA6C;QAACH,EAAA,CAAAE,MAAA,mEAClB;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAK1CJ,EAAA,CAAAG,cAAA,kBAAmC;QAI3BH,EAAA,CAAAC,SAAA,oBAAgD;QAClDD,EAAA,CAAAI,YAAA,EAAiB;QAO7BJ,EAAA,CAAAG,cAAA,iBAAmD;QAGzCH,EAAA,CAAAE,MAAA,sBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEvBJ,EAAA,CAAAG,cAAA,iBAAkB;QAIVH,EAAA,CAAAC,SAAA,kBAAmF;QACrFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,iBAAiB;QACXH,EAAA,CAAAE,MAAA,yBAAe;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACxBJ,EAAA,CAAAG,cAAA,gBAAoC;QAClCH,EAAA,CAAAE,MAAA,kCACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACJJ,EAAA,CAAAG,cAAA,gBAAuB;QAAAH,EAAA,CAAAE,MAAA,kDAAwC;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAGvEJ,EAAA,CAAAG,cAAA,kBAAwB;QAEpBH,EAAA,CAAAC,SAAA,kBAAmF;QACrFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,iBAAiB;QACXH,EAAA,CAAAE,MAAA,sBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACrBJ,EAAA,CAAAG,cAAA,gBAAoC;QAClCH,EAAA,CAAAE,MAAA,kCACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACJJ,EAAA,CAAAG,cAAA,gBAAuB;QAAAH,EAAA,CAAAE,MAAA,kDAAwC;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAGvEJ,EAAA,CAAAG,cAAA,kBAAwB;QAEpBH,EAAA,CAAAC,SAAA,kBAAmF;QACrFD,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,iBAAiB;QACXH,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACnBJ,EAAA,CAAAG,cAAA,gBAAoC;QAClCH,EAAA,CAAAE,MAAA,kCACF;QAAAF,EAAA,CAAAI,YAAA,EAAI;QACJJ,EAAA,CAAAG,cAAA,gBAAuB;QAAAH,EAAA,CAAAE,MAAA,kDAAwC;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAGvEJ,EAAA,CAAAG,cAAA,kBAAyB;QACuBH,EAAA,CAAAE,MAAA,wBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAO5EJ,EAAA,CAAAG,cAAA,iBAA0B;QAIdH,EAAA,CAAAE,MAAA,sBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEvBJ,EAAA,CAAAG,cAAA,iBAAkB;QAIVH,EAAA,CAAAC,SAAA,kBAAwE;QAC1ED,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAAyB;QACcH,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACpDJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,iEAAuD;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAC9DJ,EAAA,CAAAG,cAAA,oBAA0B;QAAAH,EAAA,CAAAE,MAAA,qBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAGjDJ,EAAA,CAAAG,cAAA,kBAA0B;QAEtBH,EAAA,CAAAC,SAAA,kBAAwE;QAC1ED,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAAyB;QAC6BH,EAAA,CAAAE,MAAA,qBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACpEJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,iEAAuD;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAC9DJ,EAAA,CAAAG,cAAA,gBAAwB;QAAAH,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAG1CJ,EAAA,CAAAG,cAAA,kBAA0B;QAEtBH,EAAA,CAAAC,SAAA,kBAAwE;QAC1ED,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAAyB;QAC0BH,EAAA,CAAAE,MAAA,sBAAY;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAClEJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,iEAAuD;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAC9DJ,EAAA,CAAAG,cAAA,kBAA0B;QAAAH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAG7CJ,EAAA,CAAAG,cAAA,kBAAoC;QAEhCH,EAAA,CAAAC,SAAA,kBAAwE;QAC1ED,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAAyB;QAC0BH,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAChEJ,EAAA,CAAAG,cAAA,WAAG;QAAAH,EAAA,CAAAE,MAAA,iEAAuD;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAC9DJ,EAAA,CAAAG,cAAA,kBAA0B;QAAAH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAOrDJ,EAAA,CAAAG,cAAA,iBAAmD;QAGzCH,EAAA,CAAAE,MAAA,yBAAe;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAE1BJ,EAAA,CAAAG,cAAA,iBAAkB;QAIVH,EAAA,CAAAC,SAAA,kBAAwE;QAC1ED,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAA4B;QAEGH,EAAA,CAAAE,MAAA,qDAA2C;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAE7EJ,EAAA,CAAAG,cAAA,kBAA2B;QACtBH,EAAA,CAAAE,MAAA,uBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAI1BJ,EAAA,CAAAG,cAAA,YAAI;QAEAH,EAAA,CAAAC,SAAA,kBAAwE;QAC1ED,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAA4B;QAEGH,EAAA,CAAAE,MAAA,qDAA2C;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAE7EJ,EAAA,CAAAG,cAAA,kBAA2B;QACtBH,EAAA,CAAAE,MAAA,wBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAI3BJ,EAAA,CAAAG,cAAA,YAAI;QAEAH,EAAA,CAAAC,SAAA,kBAAwE;QAC1ED,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAA4B;QAEGH,EAAA,CAAAE,MAAA,qDAA2C;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAE7EJ,EAAA,CAAAG,cAAA,kBAA2B;QACtBH,EAAA,CAAAE,MAAA,wBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAI3BJ,EAAA,CAAAG,cAAA,YAAI;QAEAH,EAAA,CAAAC,SAAA,kBAAwE;QAC1ED,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAG,cAAA,kBAA4B;QAEGH,EAAA,CAAAE,MAAA,qDAA2C;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAE7EJ,EAAA,CAAAG,cAAA,kBAA2B;QACtBH,EAAA,CAAAE,MAAA,wBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAI;QASrCJ,EAAA,CAAAG,cAAA,gBAAiB;QAILH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEpBJ,EAAA,CAAAG,cAAA,iBAAkB;QAEqBH,EAAA,CAAAK,UAAA,gCAAAoD,kEAAAC,MAAA;UAAA,OAAsBL,GAAA,CAAAjB,IAAA,CAAAsB,MAAA,CAAY;QAAA,EAAC;QACpE1D,EAAA,CAAA2D,gBAAA,OAAAC,qCAAA,sBAAA5D,EAAA,CAAA6D,yBAAA,CA+BC;QACH7D,EAAA,CAAAI,YAAA,EAAM;QAKdJ,EAAA,CAAAG,cAAA,gBAAmD;QAGzCH,EAAA,CAAAE,MAAA,qBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAEtBJ,EAAA,CAAAG,cAAA,iBAAkB;QAINH,EAAA,CAAAC,SAAA,gBAAyC;QAACD,EAAA,CAAAE,MAAA,qBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAC7DJ,EAAA,CAAAG,cAAA,iBAA0B;QACIH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAGhDJ,EAAA,CAAAG,cAAA,YAAI;QACEH,EAAA,CAAAC,SAAA,gBAA0C;QAACD,EAAA,CAAAE,MAAA,4BAAiB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACrEJ,EAAA,CAAAG,cAAA,iBAA0B;QACIH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAGhDJ,EAAA,CAAAG,cAAA,YAAI;QACEH,EAAA,CAAAC,SAAA,gBAAyC;QAACD,EAAA,CAAAE,MAAA,8BAAmB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACtEJ,EAAA,CAAAG,cAAA,iBAA0B;QACIH,EAAA,CAAAE,MAAA,iBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAG9CJ,EAAA,CAAAG,cAAA,YAAI;QACEH,EAAA,CAAAC,SAAA,gBAAuC;QAACD,EAAA,CAAAE,MAAA,4BAAiB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QAClEJ,EAAA,CAAAG,cAAA,iBAA0B;QACIH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAGhDJ,EAAA,CAAAG,cAAA,YAAI;QACEH,EAAA,CAAAC,SAAA,gBAA0C;QAACD,EAAA,CAAAE,MAAA,6BAAkB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACtEJ,EAAA,CAAAG,cAAA,iBAA0B;QACIH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAGhDJ,EAAA,CAAAG,cAAA,YAAI;QACEH,EAAA,CAAAC,SAAA,gBAAyC;QAACD,EAAA,CAAAE,MAAA,iCAAsB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACzEJ,EAAA,CAAAG,cAAA,iBAA0B;QACIH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAGhDJ,EAAA,CAAAG,cAAA,YAAI;QACEH,EAAA,CAAAC,SAAA,gBAAyC;QAACD,EAAA,CAAAE,MAAA,gCAAqB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACxEJ,EAAA,CAAAG,cAAA,iBAA0B;QACIH,EAAA,CAAAE,MAAA,iBAAO;QAAAF,EAAA,CAAAI,YAAA,EAAO;QAKlDJ,EAAA,CAAAG,cAAA,iBAAgC;QACaH,EAAA,CAAAE,MAAA,+BAAqB;QAAAF,EAAA,CAAAI,YAAA,EAAS;QAKjFJ,EAAA,CAAAG,cAAA,gBAAmD;QAGfH,EAAA,CAAAE,MAAA,8BAAoB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACvDJ,EAAA,CAAAG,cAAA,kBAAsB;QAEgBH,EAAA,CAAAE,MAAA,eAAK;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAC/CJ,EAAA,CAAAG,cAAA,YAAI;QAAAH,EAAA,CAAAE,MAAA,aAAG;QAAAF,EAAA,CAAAG,cAAA,gBAA4C;QAAAH,EAAA,CAAAE,MAAA,qBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAEpEJ,EAAA,CAAAG,cAAA,kBAAmB;QACiBH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QACnDJ,EAAA,CAAAG,cAAA,YAAI;QAAAH,EAAA,CAAAE,MAAA,aAAG;QAAAF,EAAA,CAAAG,cAAA,gBAA6C;QAAAH,EAAA,CAAAE,MAAA,uBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAEvEJ,EAAA,CAAAG,cAAA,kBAAmB;QACiBH,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QACpDJ,EAAA,CAAAG,cAAA,YAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAM;QAAAF,EAAA,CAAAG,cAAA,gBAA4C;QAAAH,EAAA,CAAAE,MAAA,qBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAGzEJ,EAAA,CAAAG,cAAA,kBAA6C;QAErBH,EAAA,CAAAE,MAAA,aAAG;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAIrCJ,EAAA,CAAAG,cAAA,kBAA6B;QAEKH,EAAA,CAAAE,MAAA,gCAAsB;QAAAF,EAAA,CAAAI,YAAA,EAAK;QACzDJ,EAAA,CAAAG,cAAA,kBAAsB;QAEgBH,EAAA,CAAAE,MAAA,eAAK;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QAC/CJ,EAAA,CAAAG,cAAA,YAAI;QAAAH,EAAA,CAAAE,MAAA,cAAI;QAAAF,EAAA,CAAAG,cAAA,gBAA6C;QAAAH,EAAA,CAAAE,MAAA,uBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAExEJ,EAAA,CAAAG,cAAA,kBAAmB;QACiBH,EAAA,CAAAE,MAAA,mBAAS;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QACnDJ,EAAA,CAAAG,cAAA,YAAI;QAAAH,EAAA,CAAAE,MAAA,cAAI;QAAAF,EAAA,CAAAG,cAAA,gBAA4C;QAAAH,EAAA,CAAAE,MAAA,qBAAW;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAErEJ,EAAA,CAAAG,cAAA,kBAAmB;QACiBH,EAAA,CAAAE,MAAA,oBAAU;QAAAF,EAAA,CAAAI,YAAA,EAAQ;QACpDJ,EAAA,CAAAG,cAAA,YAAI;QAAAH,EAAA,CAAAE,MAAA,gBAAM;QAAAF,EAAA,CAAAG,cAAA,gBAA6C;QAAAH,EAAA,CAAAE,MAAA,uBAAa;QAAAF,EAAA,CAAAI,YAAA,EAAI;QAG5EJ,EAAA,CAAAG,cAAA,kBAA6C;QAErBH,EAAA,CAAAE,MAAA,aAAG;QAAAF,EAAA,CAAAI,YAAA,EAAM;;;QA/kDvBJ,EAAA,CAAAqB,SAAA,GAAuB;QAAvBrB,EAAA,CAAAsB,UAAA,wBAAuB,UAAAtB,EAAA,CAAA8D,eAAA,IAAAC,GAAA;QAq8C7B/D,EAAA,CAAAqB,SAAA,MA+BC;QA/BDrB,EAAA,CAAAgE,UAAA,CAAAX,GAAA,CAAAnB,KAAA,CA+BC;;;mBD98CbnC,mBAAmB,EACnBD,oBAAoB,EAAAmE,EAAA,CAAAC,cAAA,EACpBrE,WAAW,EACXD,eAAe,EAAAuE,EAAA,CAAAC,SAAA,EACfzE,aAAa,EAAA0E,EAAA,CAAAC,WAAA,EAAAD,EAAA,CAAAE,MAAA,EAAAF,EAAA,CAAAG,WAAA,EACb9E,kBAAkB,EAAA+E,EAAA,CAAAC,YAAA,EAClBjF,cAAc,EAAAkF,EAAA,CAAAC,QAAA,EACd3F,WAAW,EACXC,OAAO,EACPC,aAAa,EACbK,aAAa,EAAAqF,EAAA,CAAAC,OAAA,EACbvF,iBAAiB,EAAAwF,EAAA,CAAAC,WAAA,EACjB5F,kBAAkB,EAClBE,gBAAgB,EAAA2F,EAAA,CAAAC,UAAA,EAChB7F,OAAO;IAAA8F,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}