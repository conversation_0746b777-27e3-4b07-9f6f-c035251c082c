{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TodayService extends UnsubscribeOnDestroyAdapter {\n  constructor(httpClient) {\n    super();\n    this.httpClient = httpClient;\n    this.API_URL = 'assets/data/today.json';\n    this.isTblLoading = true;\n    this.dataChange = new BehaviorSubject([]);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  /** CRUD METHODS */\n  getAllTodays() {\n    this.subs.sink = this.httpClient.get(this.API_URL).subscribe({\n      next: data => {\n        this.isTblLoading = false;\n        this.dataChange.next(data);\n      },\n      error: error => {\n        this.isTblLoading = false;\n        console.log(error.name + ' ' + error.message);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function TodayService_Factory(t) {\n    return new (t || TodayService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TodayService,\n    factory: TodayService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "UnsubscribeOnDestroyAdapter", "TodayService", "constructor", "httpClient", "API_URL", "isTblLoading", "dataChange", "data", "value", "getDialogData", "dialogData", "getAllTodays", "subs", "sink", "get", "subscribe", "next", "error", "console", "log", "name", "message", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\attendance\\today\\today.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { Today } from './today.model';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { UnsubscribeOnDestroyAdapter } from '@shared';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TodayService extends UnsubscribeOnDestroyAdapter {\r\n  private readonly API_URL = 'assets/data/today.json';\r\n  isTblLoading = true;\r\n  dataChange: BehaviorSubject<Today[]> = new BehaviorSubject<Today[]>([]);\r\n  // Temporarily stores data from dialogs\r\n  dialogData!: Today;\r\n  constructor(private httpClient: HttpClient) {\r\n    super();\r\n  }\r\n  get data(): Today[] {\r\n    return this.dataChange.value;\r\n  }\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n  /** CRUD METHODS */\r\n  getAllTodays(): void {\r\n    this.subs.sink = this.httpClient.get<Today[]>(this.API_URL).subscribe({\r\n      next: (data) => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(data);\r\n      },\r\n      error: (error: HttpErrorResponse) => {\r\n        this.isTblLoading = false;\r\n        console.log(error.name + ' ' + error.message);\r\n      },\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;AAGtC,SAASC,2BAA2B,QAAQ,SAAS;;;AAKrD,OAAM,MAAOC,YAAa,SAAQD,2BAA2B;EAM3DE,YAAoBC,UAAsB;IACxC,KAAK,EAAE;IADW,KAAAA,UAAU,GAAVA,UAAU;IALb,KAAAC,OAAO,GAAG,wBAAwB;IACnD,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,UAAU,GAA6B,IAAIP,eAAe,CAAU,EAAE,CAAC;EAKvE;EACA,IAAIQ,IAAIA,CAAA;IACN,OAAO,IAAI,CAACD,UAAU,CAACE,KAAK;EAC9B;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EACA;EACAC,YAAYA,CAAA;IACV,IAAI,CAACC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACV,UAAU,CAACW,GAAG,CAAU,IAAI,CAACV,OAAO,CAAC,CAACW,SAAS,CAAC;MACpEC,IAAI,EAAGT,IAAI,IAAI;QACb,IAAI,CAACF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,UAAU,CAACU,IAAI,CAACT,IAAI,CAAC;MAC5B,CAAC;MACDU,KAAK,EAAGA,KAAwB,IAAI;QAClC,IAAI,CAACZ,YAAY,GAAG,KAAK;QACzBa,OAAO,CAACC,GAAG,CAACF,KAAK,CAACG,IAAI,GAAG,GAAG,GAAGH,KAAK,CAACI,OAAO,CAAC;MAC/C;KACD,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBA3BUrB,YAAY,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAZ1B,YAAY;IAAA2B,OAAA,EAAZ3B,YAAY,CAAA4B,IAAA;IAAAC,UAAA,EAFX;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}